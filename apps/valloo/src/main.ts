import { enableProdMode, Injector } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { environment } from '@corporativo/shared';

export let injector: Injector;
if (environment.production) {
  enableProdMode();
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .then(ref => {
    injector = ref.injector;
    // @ts-ignore
    window['injector'] = injector;
  })
  .catch(err => console.log(err));
