.logo {
  width: 122px;
  height: 44px;
}

.background-fundo {
  position: relative;
  background-image: url('/assets/images/cartao-com-mao.png');
  background-size: cover;
}

.background-fundo::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, #D9D9D9 0%, rgba(255, 115, 87, 0.6) 41.83%);
  z-index: -1;
}

.background-modal {
  background: linear-gradient(180deg, #727575 0%, #B3B3AC 49.67%);
}

.titulo {
  h1 {
    color: var(--ion-toolbar-color-title) !important;
  }
}

ion-header {

  .home {
    --background: var(--ion-toolbar-background-home);
  }

  ion-toolbar {
    background: var(--ion-color-background) !important;
    --background: var(--ion-toolbar-background);

    ion-button {
      top: 0px !important;
    }

    ion-buttons {
      ion-button {
        width: unset !important;
      }
    }
  }
}

.usuario-conta {
  ion-item {
    --background: var(--ion-color-background);
  }
}

.desbloquear-cartao {
  display: flex !important;
  justify-content: center !important;
}

.funcionalidades {
  flex-wrap: wrap !important;
  display: flex !important;
  gap: 1rem !important;
  align-items: flex-start !important;
  overflow-wrap: break-word;
  word-wrap: break-word;
  margin-left: unset !important;
  margin-right: unset !important;
}

.mensagem-instituicao {
  font-size: 10px !important;
}

.entrar {
  color: white;
}

.btn{
  color: white;
}

ion-button[fill="outline"]{
  --background-activated: #fef1ee;
  --color-activated: #ff7357;
}

ion-segment {
  ion-segment-button {
    --indicator-color: var(--ion-color-secondary) !important;
    --color-checked: white !important;
    background: #F0F0F0;
  }
}

.cadastrar-chaves {
  ion-item {
    ion-button {
      ion-icon {
        color: white !important;
      }
    }
  }
}

.limite {
  ion-button {
    --ion-color-contrast: white !important;
  }
}
