import { AppEnvironment, merge } from '@corporativo/shared';

export const environment: AppEnvironment = merge({
  production: true,
  appId: 'br.com.valloo.corporativo',
  app: 'valloCorporativo',
  appName: 'Valloo corporativo',
  env: 'prod',
  idInstituicao: 2801,
  idProcessadora: 10,
  idProgramaFidelidade: 2,
  idProdutoInstituicao: 290101,
  isbp: 23273917,
  tokenCaf: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenFaceLiveness: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenCafBeta: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2NTljNzMxYWUyMzY2YzAwMDhiOWIzM2UifQ.QoddeSSVsQrm1DeYyCnhW7Wny928_EtiagHPBchsIuo',
  idGrupoAcesso: 7,
  idGrupoAcessoPj: 42,
  idAplicativo: 8,
  urlMotiva: 'https://motiva.itspay.com.br',
  onesignalAppId: '************************************',
  hosts: {
    valloo: {
      host: 'issuer.valloo.com.br',
      protocol: 'https',
      port: '',
      root: '/api/api',
    }
  },
  buttonType: 'rounded',
  buttonLayout: 'carousel',
  homeVersion: 'v1',
  showChat: false,
  showIcon: true,
  showTitleDots: true,
  tituloAjuda: 'Ajuda',
  tipoLoginPj: 'VALLOO_CORPORATIVO',
  tipoLoginPf: null,
  urlApple: 'https://apps.apple.com/us/app/valloo/id6503487388',
  corFuncionalidades: false,
  telefoneContato: '(61) 3364-0005'
});
