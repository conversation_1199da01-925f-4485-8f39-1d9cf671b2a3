// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { AppEnvironment, merge } from '@corporativo/shared';

export const environment: AppEnvironment = merge({
  production: false,
  appId: '',
  app: 'valloCorporativo',
  appName: 'Valloo corporativo',
  env: 'local',
  idInstituicao: 2801,
  idProcessadora: 10,
  idProgramaFidelidade: 2,
  idProdutoInstituicao: 280101,
  isbp: 23273917,
  tokenCaf: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenFaceLiveness: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenCafBeta: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2NTljNzMxYWUyMzY2YzAwMDhiOWIzM2UifQ.QoddeSSVsQrm1DeYyCnhW7Wny928_EtiagHPBchsIuo',
  idGrupoAcesso: null,
  idGrupoAcessoPj: null,
  idAplicativo: 26,
  urlMotiva: 'https://motiva.valloo-hom.com.br',
  onesignalAppId: '************************************',
  hosts: {
    valloo: {
      host: 'localhost',
      protocol: 'http',
      port: '28080',
      root: '/api/api',
    }
  },
  buttonType: 'rounded',
  buttonLayout: 'grid',
  homeVersion: 'v1',
  showChat: false,
  showIcon: true,
  showTitleDots: true,
  tituloAjuda: 'Ajuda',
  tipoLoginPj: 'VALLOO_CORPORATIVO',
  tipoLoginPf: 'VALLOO_CORPORATIVO',
  urlApple: 'https://apps.apple.com/us/app/valloo/id6503487388',
  corFuncionalidades: false,
  telefoneContato: '(61) 3364-0005'
});


/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
