import { Component } from '@angular/core';
import { MenuController, ModalController, Platform } from '@ionic/angular';
import { Router } from '@angular/router';
import { environment } from '../environments/environment';
import OneSignal from 'onesignal-cordova-plugin';
import { AuthService, StorageService, Usuario, Versao, VersaoService } from '@corporativo/shared';
import { ModalAtencaoComponent } from '@corporativo/modals';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  standalone: false,
})
export class AppComponent {
  usuario!: Usuario;

  public appPages = [
    { title: 'Termos de Uso', url: '/termos-uso', icon: 'paper-plane' },
    {
      title: 'Política de privacidade',
      url: '/politica-privacidade',
      icon: 'heart',
    },
    {
      title: 'Informações do aplicativo',
      url: '/informacoes-aplicativo',
      icon: 'archive',
    },
  ];

  constructor(
    private menuController: MenuController,
    private storageService: StorageService,
    private modalController: ModalController,
    private authService: AuthService,
    private router: Router,
    private platform: Platform,
    private versaoService: VersaoService
  ) {
    this.platform.ready().then(async () => {
      await this.menuController.enable(false);
      this.menuController.swipeGesture(false);
      this.usuario = this.authService.getUser();
      this.initPush();
      this.verificarVersao();
    });
  }

  verificarVersao() {
    this.versaoService.verificarVersao().subscribe((versao: Versao) => {
      this.storageService.setVersaoApp(versao);
      if (!versao.permiteContinuar) {
        this.storageService.setAtualizarVersao(true);
        this.router.navigate(['atualizar-versao'], {
          state: { mensagem: versao.mensagem },
          replaceUrl: true,
          skipLocationChange: true,
        });
      } else {
        this.storageService.setAtualizarVersao(false);
      }
    });
  }

  async sair() {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Você tem certeza que deseja sair?',
        mensagem: '',
        tituloBotaoPrimario: 'Sim',
        tituloBotaoSecundario: 'Não'
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role === 'primaria') {
      this.authService.clearToken();
      await this.menuController.enable(false);
      await this.router.navigate(['/login']);
    }
  }

  async initPush() {
    if (!this.platform.is('hybrid')) {
      return;
    }
    const appId = environment.onesignalAppId;
    console.log('==> appId', appId);
    OneSignal.initialize(appId);
    const oneSignalId = await OneSignal.User.pushSubscription.getIdAsync();
    const permission = await OneSignal.Notifications.getPermissionAsync();
    console.log('==> permission', permission);
    const requestPermission = await OneSignal.Notifications.requestPermission(
      true
    );
    console.log('==> requestPermission', requestPermission);
    console.log('==> oneSignalId', oneSignalId);
    this.storageService.setOneSignalId(oneSignalId);
  }
}
