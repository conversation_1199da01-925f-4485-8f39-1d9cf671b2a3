<ion-app>
  <ion-split-pane contentId="main-content">
    <ion-menu contentId="main-content" type="overlay">
      <ion-header class="ion-no-border">
        <ion-toolbar color="primary">
          <div class="dados-conta">
            <ng-container *ngIf="usuario">
              <ion-text>Olá, {{usuario.nomeCompleto | titlecase}}</ion-text>
              <ion-text>{{usuario.documento | maskHideCpfCnpj}}</ion-text>
            </ng-container>
          </div>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <ion-list>
          <ion-menu-toggle auto-hide="false" *ngFor="let p of appPages; let i = index">
            <ion-item routerDirection="root" [routerLink]="[p.url]" lines="full" detail="true" routerLinkActive="selected">
              <ion-label>{{ p.title }}</ion-label>
            </ion-item>
          </ion-menu-toggle>
          <ion-item lines="full" detail="true" (click)="sair()">
            <ion-label>
              Sair
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-content>
    </ion-menu>
    <ion-router-outlet id="main-content"></ion-router-outlet>
  </ion-split-pane>
</ion-app>
