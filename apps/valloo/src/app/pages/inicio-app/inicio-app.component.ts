import { Component } from "@angular/core";
import { Router } from '@angular/router';
import { StorageService } from '@corporativo/shared';

@Component({
  selector: 'mobile-inicio-app',
  templateUrl: './inicio-app.component.html',
  styleUrl: './inicio-app.component.scss',
  standalone: false,
})
export class InicioAppComponent {

  constructor(
    private router: Router,
    private storageService: StorageService) {
  }

  irPara(rota: string) {
    this.storageService.marcarPossuiContaInicioApp();
    return this.router.navigate([rota]);
  }
}
