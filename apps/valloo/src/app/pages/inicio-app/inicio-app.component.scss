section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  background-blend-mode: overlay;

  article:first-child {
    height: 100%;
    width: 100%;
  }

  article:last-child {
    background-color: var(--ion-color-background);
    height: 20%;
    width: 100%;
    border-radius: 24px 24px 0 0;
    padding: 2rem 2rem 0 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    p {
      font-size: 16px;
      font-weight: 600;
      line-height: 19px;
    }
  }

  .img {
    padding: 2rem 1.5rem;
    margin-top: 1rem;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    .chat {
      ion-button {
        --border-radius: 50%;
        width: 50px;
        height: 50px;

        ion-icon {
          font-size: 24px;
        }
      }
    }
  }
}

ion-footer {
  padding: 0 1.5rem 1.5rem 1.5rem;
  background-color: var(--ion-color-background);
}
