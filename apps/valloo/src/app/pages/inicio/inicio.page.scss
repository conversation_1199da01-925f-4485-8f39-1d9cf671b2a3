ion-header {
  ion-toolbar {
    --padding-top: 1.5rem;
    --padding-start: 1.5rem;
    --padding-end: 1.5rem;
    --padding-bottom: 1.5rem;

    ion-buttons {
      ion-button {
        --border-radius: 50%;
        width: 44px;
        height: 44px;
        min-width: 44px;

        ion-icon {
          font-size: 15px;
        }

        margin: 0;
      }

      .contador {
        position: relative;
        display: inline-block;
      }

      ion-badge {
        position: absolute;
        top: -5px;
        right: -4px;
        padding: 5px 7px;
        font-size: 10px;
        //background: #F44336;
        color: white;
      }

      .notificacao {
        margin-left: 8px;
      }
    }
  }
}

.usuario-cartoes {
  display: flex;
  padding-top: 16px;

  h1 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
  }

  mobile-usuario-conta {
    width: 25%;
  }

  div {
    width: 75%;
  }

  mobile-cartoes {
    width: 75%;
  }
}

ion-content {
  --padding-start: 1.5rem;
  --padding-end: 1.5rem;

  section {
    margin-bottom: 1.5rem;
  }
}

.contas {
  margin-top: 1rem;
  display: flex;
  overflow: scroll;
  -webkit-overflow-scrolling: touch; /* para rolagem suave no iOS */
  scrollbar-width: none;

  .conta {
    padding: 0.8rem;
    width: 128px;
    min-width: 128px;
    border-radius: 8px;
    background: var(--ion-color-medium);
    margin-right: 1rem;

    h3 {
      font-size: 12px;
      font-weight: 600;
      line-height: 14px;
      margin: 0 0 0.7rem 0;
      color: var(--ion-color-gray-50);
    }

    p {
      font-size: 14px;
      font-weight: 300;
      line-height: 19px;
      margin: 0;
      color: var(--ion-color-gray-50);
    }

    .ocultar-valor {
      font-size: 8px;
    }

    &.selecionado {
      background: var(--ion-color-primary);

      h3, p {
        color: var(--ion-color-light);
      }

      p {
        font-weight: 700;
      }

      .ocultar-valor {
        font-size: 14px;
      }
    }
  }

  .conta:last-child {
    margin-right: 0;
  }
}

.contas::-webkit-scrollbar {
  display: none; /* Oculta a barra de rolagem */
}

ion-content::part(scroll) {
  background: var(--ion-color-background);
}

ion-refresher {
  background: var(--ion-color-background);

  ion-refresher-content {
    background: var(--ion-color-background);
  }
}

.servico {
  --padding-start: 0;
  margin-bottom: 8px;

  ion-label {
    h2 {
      display: flex;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 400;
      color: var(--ion-color-dark);
    }
  }

  .icone {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;

    &.success {
      background-color: rgba(82, 196, 115, 0.1);

      ion-icon {
        color: #52c473;
        font-size: 20px;
      }
    }
  }
}

.banner-ze {
  &::part(image) {
    border-radius: 8px;
  }
}

.estado-vazio-cartao {
  --border-radius: 8px;
}

.estado-vazio {
  margin: 0.5rem 0;
  display: flex;
  justify-content: center;
}

.dados-consumo-label, .dados-consumo-value {
  display: none;
}

section {
  ion-item {
    margin: 1rem 0;
    --border-radius: 8px;
    --inner-padding-end: 0;
    --padding-end: 0;
    --background: var(--ion-color-medium);

    ion-label {
      h2 {
        font-weight: 700;
        font-size: 16px;
      }
    }
  }
}

ion-card {
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 8px;
  box-shadow: none;
  margin: 8px 0;
}

ion-card-content {
  padding: 12px 16px;
}
