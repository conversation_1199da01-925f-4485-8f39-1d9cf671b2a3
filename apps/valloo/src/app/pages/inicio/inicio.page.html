<ion-header class="ion-no-border">
  <ion-toolbar class="home">
    <ion-buttons slot="end">
      <ion-button class="menu" color="medium" [fill]="buttonClass" (click)="abrirMenu()">
        <ion-icon name="c-menu" slot="icon-only"></ion-icon>
      </ion-button>
      <div class="contador">
        <ion-button color="medium" [fill]="buttonClass" class="notificacao" (click)="abrirNotificacoes()">
          <ion-icon name="sino" slot="icon-only"></ion-icon>
        </ion-button>
        <ion-badge *ngIf="countNotificacoes" color="tertiary">{{ countNotificacoes }}</ion-badge>
      </div>
    </ion-buttons>
    <img class="logo" slot="start" src="assets/images/logo.png" alt="Logo" />
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" [pullFactor]="1" [pullMin]="150" [pullMax]="150" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content
      pullingIcon="chevron-down-circle"
      pullingText="Puxe para atualizar"
      refreshingSpinner="circles"
      refreshingText="Atualizando..."
    >
    </ion-refresher-content>
  </ion-refresher>
  @if (carregando) {
    <section class="usuario-cartoes">
      <mobile-usuario-conta [inicio]="true" [usuario]="usuario" (clicar)="irParaMeuPerfil()"></mobile-usuario-conta>
      <div>
        <h1>{{ saudacao }}, {{ usuario.nomeCompleto | firstName  | titlecase }}</h1>
        <ion-item lines="none" color="medium" class="estado-vazio-cartao">
          <mobile-skeleton></mobile-skeleton>
        </ion-item>
      </div>
    </section>
    <mobile-titulo-secao [mostrarOcultarValor]="false">
      Minhas contas
    </mobile-titulo-secao>
    <section>
      <article class="contas">
        <div class="conta ion-activatable ripple-parent" [class.selecionado]="i === 0"
             *ngFor="let it of [1,2,3]; let i = index">
          <h3>
            <mobile-skeleton></mobile-skeleton>
          </h3>
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </div>
      </article>
    </section>
    <section>
      <article appDynamicClass class="ion-margin-top">
        <ng-container *ngFor="let e of [1, 2, 3]">
          <mobile-button [carregando]="true"></mobile-button>
        </ng-container>
      </article>
    </section>
    <section>
      <mobile-titulo-secao [mostrarOcultarValor]="false">
        <mobile-skeleton></mobile-skeleton>
      </mobile-titulo-secao>
      <article appDynamicClass class="ion-margin-top">
        <ng-container *ngFor="let e of [1, 2, 3]">
          <mobile-button [carregando]="true"></mobile-button>
        </ng-container>
      </article>
    </section>
  } @else {
    <section class="usuario-cartoes">
      <mobile-usuario-conta [inicio]="true" [usuario]="usuario" (clicar)="irParaMeuPerfil()"></mobile-usuario-conta>
      <div>
        <h1>{{ saudacao }}, {{ usuario.nomeCompleto | firstName  | titlecase }}</h1>
        <mobile-cartoes (trocar)="pegarCartao($event)" [refresh]="refreshCards"></mobile-cartoes>
      </div>
    </section>
    <mobile-titulo-secao [mostrarOcultarValor]="true" (ocultarValor)="ocultarValores($event)">
      Minhas contas
    </mobile-titulo-secao>
    <section>
      <article class="contas" *ngIf="credencial && credencial.contas">
        <div class="conta ion-activatable ripple-parent" [class.selecionado]="conta.selecionado"
             *ngFor="let conta of credencial.contas" (click)="selecionarConta(conta)">
          <h3>{{ conta.descProdutoInstituicao }}</h3>
          <ng-container *ngIf="ocultarSaldo else mostrar">
            <p *ngIf="ocultarValorConta" class="ocultar-valor">●●●●</p>
            <p *ngIf="!ocultarValorConta">●●●●</p>
          </ng-container>
          <ng-template #mostrar>
            <p *ngIf="conta.tipoProduto !== 'MOEDEIRO' ">
              {{ conta.saldoConta.saldoDisponivel | currency:'BRL' }}
            </p>
            <p *ngIf="conta.tipoProduto === 'MOEDEIRO'">
              PS {{ conta.saldoConta.saldoDisponivel | currencyBr }}
            </p>
          </ng-template>
          <ion-ripple-effect></ion-ripple-effect>
        </div>
      </article>
    </section>
    <section>
      <article appDynamicClass class="ion-margin-top">
        <ng-container *ngIf="produtos$ | async as produtos; else carregando">
          <ng-container *ngFor="let produto of produtos">
            <mobile-button *ngIf="produto.ativo" [icon]="produto.icone"
                                   [routerLink]="'/' + produto.url + '/'+ conta.idConta "
                                   [nome]="produto.nome" [color]="produto.color"></mobile-button>
          </ng-container>
           <div class="estado-vazio" *ngIf="produtos.length == 0">
              <p>Nenhuma funcionalidade vinculado a conta.</p>
           </div>
        </ng-container>
        <ng-template #carregando>
          <ng-container *ngFor="let i of [1, 2, 3]">
            <mobile-button [carregando]="true"></mobile-button>
          </ng-container>
        </ng-template>
      </article>
    </section>
    <section>
      <mobile-titulo-secao *ngIf="mostrarFuncionalidadeCartao" [mostrarOcultarValor]="false">
        Funcionalidades do cartão
      </mobile-titulo-secao>
      <article appDynamicClass class="ion-margin-top">
        <ng-container *ngFor="let func of funcionalidadesCartao">
          <mobile-button *ngIf="func.ativo" [icon]="func.icone"
                                 [routerLink]="'/' + func.url + '/' + conta.idConta"
                                 [nome]="func.nome" [color]="func.color"></mobile-button>
        </ng-container>
      </article>
    </section>
  }
</ion-content>
