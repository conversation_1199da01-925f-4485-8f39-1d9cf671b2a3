import { Component, OnInit } from '@angular/core';
import { MenuController, ModalController, Platform } from '@ionic/angular';
import { Observable, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AuthService,
  Conta,
  ContaService,
  Credencial,
  environment,
  FuncionalidadeService,
  NotificacoesService,
  ProdutoService,
  StorageService,
  Usuario
} from '@corporativo/shared';
import { ModalEnderecoComponent, ModalNotificacaoDestaqueComponent } from '@corporativo/modals';

@Component({
  selector: 'app-inicio',
  templateUrl: './inicio.page.html',
  styleUrls: ['./inicio.page.scss'],
  standalone: false,
})
export class InicioPage implements OnInit {
  produtos$!: Observable<any[]>;
  servicos$!: Observable<any>;
  dadosConsumo$!: Observable<any>;
  usuario!: Usuario;
  credencial!: Credencial;
  conta!: Conta;
  funcionalidadesCartao: any[] = [];
  ocultarSaldo = false;
  carregando = false;
  saudacao!: string;
  refreshCards = false;
  ocultarValorConta = false;
  countNotificacoes = 0;
  atualizarSubscription$!: Subscription;
  isContaTeste = false;
  buttonClass = environment.homeVersion === 'v1' ? 'solid' : 'clear';
  mostrarFuncionalidadeCartao: boolean = true;
  mostrarDadosConsumo: boolean = false;
  dadosPessoais: any;

  constructor(private menuController: MenuController,
              private authService: AuthService,
              private produtoService: ProdutoService,
              private funcionalidadeService: FuncionalidadeService,
              private router: Router,
              private platform: Platform,
              private modalController: ModalController,
              private notificacoesService: NotificacoesService,
              private storageService: StorageService,
              private contaService: ContaService,
              private activatedRoute: ActivatedRoute) {
  }

  ngOnInit() {
    this.usuario = this.authService.getUser();
    this.activatedRoute.params.subscribe(x => {
      this.menuController.enable(true);
    });
    // this.buscarNotificacaoDestaque();
  }

  ionViewDidEnter() {
    this.usuario = this.authService.getUser();
    this.isContaTeste = this.usuario.documento == '44968992211';
    this.buscarSaudacao();
    this.atualizarSaldo();
    // this.buscarParametroValor();
  }

  ionViewDidLeave() {
    this.refreshCards = false;
    this.carregando = false;
    if (this.atualizarSubscription$) {
      this.atualizarSubscription$.unsubscribe();
    }
  }

  abrirMenu() {
    return this.menuController.open();
  }

  handleRefresh(event: any) {
    this.atualizar(event);
  }

  atualizarSaldo() {
    this.refreshCards = true;
    // this.buscarNotificacoes();
  }

  atualizar(event?: any) {
    this.carregando = true;
    this.atualizarSubscription$ = this.authService.buscarDados().subscribe(x => {
      this.buscarFuncionalidades();
      // this.buscarNotificacoes();
      this.usuario = this.authService.getUser();
      this.carregando = false;
      if (event) {
        event.target.complete();
      }
    });
  }

  buscarFuncionalidades() {
    if (!this.conta) {
      return;
    }
    this.produtos$ = this.produtoService.getFuncionalidades(this.conta.idConta);

    this.produtos$.subscribe(produtos => {
      this.mostrarDadosConsumo = produtos.some(produto =>
        produto.codigo === 'dados_consumo_pagina_inicial' && produto.ativo
      );

      this.produtos$ = new Observable<any[]>(observer => {
        const filteredProdutos = produtos.filter(produto =>
          produto.codigo !== 'dados_consumo_pagina_inicial'
        );
        observer.next(filteredProdutos);
        observer.complete();
      });

      if (this.mostrarDadosConsumo && this.conta) {
        this.buscarDadosConsumo();
      }
    });
  }

  async pegarCartao(credencial: Credencial) {
    this.dadosPessoais = this.storageService.getDadosPessoais();
    if (!credencial) {
      return;
    }
    this.credencial = credencial;
    this.credencial.contas.map(x => x.selecionado = false);
    credencial.contas[0].selecionado = true;
    this.conta = credencial.contas[0];
    this.buscarFuncionalidades();
    this.funcionalidadesCartao = this.produtoService.getFuncionalidadesCartao(this.credencial);
    this.buscarFuncionalidadesCartoes(this.funcionalidadesCartao);
    this.buscarCredencialAtivo(this.credencial);
  }

  selecionarConta(conta: Conta) {
    this.credencial.contas.map(x => x.selecionado = false);
    conta.selecionado = true;
    this.conta = conta;
    this.buscarFuncionalidades();
    this.buscarDadosConsumo();
  }

  ocultarValores(ocultar: boolean) {
    this.ocultarValorConta = this.platform.is('ios');
    this.ocultarSaldo = ocultar;
  }

  saqueFgts() {
    return this.router.navigate(['saque-fgts']);
  }

  parcelamentoDebitos() {
    return this.router.navigate(['parcelar-debitos'], {
      state: {
        credencialSelecionada: this.credencial,
        idConta: this.conta.idConta
      }
    });
  }

  irParaMeuPerfil() {
    return this.router.navigate(['meu-perfil'], { state: { credencial: this.credencial } });
  }

  buscarSaudacao() {
    const dataAtual = new Date();
    const hora = dataAtual.getHours();
    const minutos = dataAtual.getMinutes();
    const totalMinutos = hora * 60 + minutos;

    if (totalMinutos >= 6 * 60 && totalMinutos < 12 * 60) {
      this.saudacao = 'Bom dia';
    } else if (totalMinutos >= 12 * 60 + 1 && totalMinutos < 18 * 60) {
      this.saudacao = 'Boa tarde';
    } else {
      this.saudacao = 'Boa noite';
    }
  }

  buscarNotificacoes() {
    this.notificacoesService.buscarCountNotificacoes().subscribe({
      next: (count: number) => {
        this.countNotificacoes = count;
      }
    });
  }

  abrirNotificacoes() {
    return this.router.navigate([`/notificacoes/${this.conta.idConta}`]);
  }

  buscarNotificacaoDestaque() {
    this.notificacoesService.buscarNotificacaoDestaque(this.usuario.documento).subscribe({
      next: async (mensagem: any) => {
        if (mensagem && !mensagem.blLido) {
          await this.modalNotificacao(mensagem);
        }
      }
    });
  }

  async modalNotificacao(mensagem: any) {
    const modal = await this.modalController.create({
      component: ModalNotificacaoDestaqueComponent,
      componentProps: {
        mensagem: mensagem,
        idCredencial: this.credencial.idCredencial,
        idConta: this.conta.idConta,
        credencial: this.credencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  irParaExtratosContasMigradas() {
    this.router.navigate([`extrato-contas-migradas/${this.conta.idConta}`]);
  }

  // buscarParametroValor() {
  //   this.servicos$ = this.funcionalidadeService.buscarServicos();
  // }

  async verificarAlteracaoSenha(credencial: Credencial) {
    // const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
    //   const conta = x.contas.find(c => c.idConta == credencial.idConta);
    //   return conta ? x : null;
    // });
    // let credencialAtiva = null;
    // let credencialInativa = null;
    // if (credenciais.length) {
    //   for (const cartao of credenciais) {
    //     if (cartao.status == 1) {
    //       credencialAtiva = cartao;
    //     } else {
    //       credencialInativa = cartao;
    //     }
    //   }
    // }
    // if (!credencialAtiva) {
    //   return;
    // }
    //
    // if (!credencial.senhaAlterada && credencialAtiva.virtual) {
    //   const modal = await this.modalController.create({
    //     component: ModalSenhaCartaoComponent,
    //     componentProps: { credencial: credencial }
    //   });
    //   await modal.present();
    // } else {
    //   let modal: any = await this.modalController.getTop();
    //   while (modal) {
    //     await this.modalController.dismiss();
    //     modal = await this.modalController.getTop();
    //   }
    // }
  }

  temPontoRelacionamentoDuplicado(credenciais: Credencial[]) {
    const pontosUnicos = new Set();
    credenciais = credenciais.filter(credencial => !credencial.virtual && credencial.status === 1);
    for (const item of credenciais) {
      if (pontosUnicos.has(item.pontoRelacionamento)) {
        return true;
      }
      pontosUnicos.add(item.pontoRelacionamento);
    }
    return false;
  }

  buscarFuncionalidadesCartoes(funcionalidades: any[]) {
    this.funcionalidadeService.buscarFuncionalidesCartao(funcionalidades)
      .subscribe((funcionalidades: any[]) => {
          this.mostrarFuncionalidadeCartao = !!funcionalidades.filter(x => x.ativo).length;
          this.funcionalidadesCartao = funcionalidades;
        }
      );
  }

  async atualizarEndereco(credencial: any) {
    const modal = await this.modalController.create({
      component: ModalEnderecoComponent,
      componentProps: { credencial: this.credencial, tituloSecao: 'Atualize seu endereço', mostrarVoltar: false }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data) {
      this.usuario = this.authService.getUser();
      await this.verificarAlteracaoSenha(credencial);
    }
  }

  async verificarDados(credencial: any) {
    this.usuario = this.authService.getUser();
     if (this.usuario.enderecoResidencial == null || !this.usuario.enderecoResidencial.dtHrConfirmacao) {
      await this.atualizarEndereco(credencial);
      return;
    } else {
      await this.verificarAlteracaoSenha(credencial);
      return;
    }
  }

  async atualizarDadosPessoais(credencial: any) {
    const form: any = {
      email: this.dadosPessoais.email,
      cep: this.dadosPessoais.endereco.cep,
      logradouro: this.dadosPessoais.endereco.logradouro,
      numero: this.dadosPessoais.endereco.numero,
      complemento: this.dadosPessoais.endereco.complemento,
      bairro: this.dadosPessoais.endereco.bairro,
      cidade: this.dadosPessoais.endereco.cidade,
      uf: this.dadosPessoais.endereco.uf,
      dtHrConfirmacao: new Date()
    }

    const dados = {
      documento: this.usuario.documento,
      dddTelefoneCelular: this.usuario.dddTelefoneCelular,
      telefoneCelular: this.usuario.telefoneCelular
    }

    const dadosPessoais = {
      nome: this.dadosPessoais.nome,
      dataNascimento: this.dadosPessoais.dataNascimento,
      sexo: this.dadosPessoais.sexo,
      estadoCivil: this.dadosPessoais.estadoCivil,
      nacionalidade: this.dadosPessoais.nacionalidade,
    }

    this.funcionalidadeService.atualizarDados(form, dados, dadosPessoais).subscribe({
      next: (response: any) => {
        this.storageService.removeStorage("_valloo_dados_pessoais");
        if (response.sucesso) {
          this.usuario.enderecoResidencial = form;
          this.storageService.setUser(this.usuario);
          this.verificarDados(credencial);
        }
      }
    })
  }

  buscarDadosConsumo() {
    if (this.conta) {
      this.dadosConsumo$ = this.contaService.buscarDadosConsumo(this.conta.idConta);
    }
  }

  buscarCredencialAtivo(credencial: Credencial) {
    console.log('==> credencial: ', credencial);
    if (credencial.status === 5 || credencial.status === 0) {
      this.router.navigate(['/criar-senha'], {state: {credencial: credencial}});
    }
  }
}
