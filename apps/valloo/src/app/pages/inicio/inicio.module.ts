import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { InicioPageRoutingModule } from './inicio-routing.module';
import { InicioPage } from './inicio.page';
import {
  ButtonModule,
  CartoesModule,
  EstadoVazioModule,
  SkeletonModule,
  TituloSecaoModule,
  UsuarioContaModule,
} from '@corporativo/components';
import { DirectivesModule, PipesModule } from '@corporativo/shared';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InicioPageRoutingModule,
    TituloSecaoModule,
    ButtonModule,
    UsuarioContaModule,
    CartoesModule,
    PipesModule,
    SkeletonModule,
    DirectivesModule,
    EstadoVazioModule,
  ],
  declarations: [InicioPage],
})
export class InicioPageModule {}
