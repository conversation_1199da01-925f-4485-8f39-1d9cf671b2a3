import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {SelfieCafPage} from './selfie-caf.page';
import {SelfieCafPageRoutingModule} from './selfie-caf-routing.module';
import {TitleToolbarModule} from '../../../../../../libs/components/src';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MaskitoDirective,
    ReactiveFormsModule,
    TitleToolbarModule,
    SelfieCafPageRoutingModule
  ],
  declarations: [SelfieCafPage]
})
export class SelfieCafPageModule {
}
