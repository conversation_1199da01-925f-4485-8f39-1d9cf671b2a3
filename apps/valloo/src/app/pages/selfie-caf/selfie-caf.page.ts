import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {
  AuthService,
  CredencialService,
  OnboardingService,
  TipoObjetivoEnum,
  Usuario
} from '@corporativo/shared';
import {FluxoService} from '../../../../../../libs/shared/src/lib/services/fluxo.service';
import {ModalController} from '@ionic/angular';
import {loading} from '../../../../../../libs/utils/loading.util';
import {concatMap} from 'rxjs';
import {toast} from '@utils/toast.util';
import {ModalErroComponent, ModalSucessoComponent} from '@corporativo/modals';
import {environment} from '../../../environments/environment';
import {jwtDecode} from 'jwt-decode';
import {CafFaceAuthenticator} from '@valloo-tecnologia/caf-face-auth';

@Component({
  selector: 'app-selfie-caf',
  templateUrl: './selfie-caf.page.html',
  styleUrls: ['./selfie-caf.page.scss'],
  standalone: false
})
export class SelfieCafPage implements OnInit {

  jwt: any;
  fluxoDados: any;
  credencial: any;
  senha: any;
  usuario!: Usuario;

  constructor(
      public router: Router,
      public onboardingService: OnboardingService,
      private credencialService: CredencialService,
      private authService: AuthService,
      private fluxoService: FluxoService,
      private modalController: ModalController
  ) {}

  async ngOnInit() {
    this.usuario = this.authService.getUser();
  }

  async ionViewDidEnter() {
    this.usuario = this.authService.getUser();
    const fluxo = this.fluxoService.get();
    this.fluxoDados = fluxo.fluxoDados;

    if (this.fluxoDados.tipo == 'troca-senha-cartao') {
      this.credencial = fluxo.dados;
    }
    if (this.fluxoDados.criarSenhaCartao) {
      this.credencial = fluxo.credencial;
      this.senha = fluxo.senha;
    }
    if (this.fluxoDados.tipo == 'troca-dispositivo') {
      await this.capturarValidarSelfie();
    } else {
      await this.validarSelfieCaf();
      // await this.registraValidacaoFacial(this.fluxoDados.tipoObjetivo);
    }
  }

  async capturarValidarSelfie() {
    const documento = this.fluxoDados.documento;
     CafFaceAuthenticator.addListener('onSuccess', async (data: {
      result: { signedResponse: string }
    }) => {
      this.jwt = data.result.signedResponse;
      const decoded: any = jwtDecode(this.jwt);
      if (!decoded.isAlive) {
        return this.router.navigate(['/erro-alterar-dispositivo'], {
          state: {
            isAliveInvalid: true,
            fluxoDados: this.fluxoDados
          }
        });
      }

      const response$ = this.onboardingService.alterarDispositivo(this.fluxoDados.documento, this.jwt);
      await loading(response$.subscribe({
        next: () => {
          return this.modalSucessoAlterarDispositivo();
        }, error: (error) => {
          const message = error.error.message ? error.error.message : error.error.msg;
          this.abrirModalError(message);
        }
      }));
    });
    CafFaceAuthenticator.addListener('onCancel', async (data) => {
      console.log('CafFaceAuth onCancel:', data);
      return;
    });
    CafFaceAuthenticator.addListener('onError', async (data) => {
      console.log('CafFaceAuth onError:', data);
      return this.router.navigate(['/erro-alterar-dispositivo'], { state: { fluxoDados: this.fluxoDados } });
    });
    CafFaceAuthenticator.start({
      mobileToken: environment.tokenCaf,
      personId: documento,
      stage: 'PROD'
    });
  }

  validarSelfieCaf() {
    CafFaceAuthenticator.addListener('onSuccess', async (data: { result: { signedResponse: string} }) => {
      this.jwt = data.result.signedResponse;
      await this.registraValidacaoFacial(this.fluxoDados.tipoObjetivo);
    });
    CafFaceAuthenticator.addListener('onCancel', async (data) => {
      return this.router.navigate(['/erro-validacao-caf'], {state: {fluxoDados: this.fluxoDados}});
    });
    CafFaceAuthenticator.addListener('onError', async (data) => {
      console.log('CafFaceAuth onError:', data);
      return this.router.navigate(['/erro-validacao-caf'], {state: {fluxoDados: this.fluxoDados}});
    });

    CafFaceAuthenticator.start({
      mobileToken: environment.tokenCaf,
      personId: this.fluxoDados.cpf,
      stage: 'PROD'
    });
  }

  async registraValidacaoFacial(idObjetivo: any) {
    // const jwt = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZXF1ZXN0SWQiOiJiN2Q5NWNkMi01NjE2LTRmYjAtYjdhNS1lOWE2Yzc0MGU3NTUiLCJtZXNzYWdlIjoiU3VjY2Vzc2Z1bGx5IiwiaXNNYXRjaCI6dHJ1ZSwiYXR0ZW1wdElkIjoiNjYxN2Y3YzgyYzc0MDAxZDE4Y2Q3ZTRhIiwiaXNBbGl2ZSI6dHJ1ZSwidG9rZW4iOiJkNzMxNjFiNDM0NTZhNjczODFkZTlhNjVhZDBjMWZjZmQ1MTU0NDU1MTk1ZjZhMzEzYWM0MWJkOTE4MDF2dTAxIiwidXNlcklkIjoiYTVmODc3NGQtZGIxMi00MDBmLWI3NGQtMzc2MzMwM2M2YWUzIiwiaW1hZ2VVcmwiOiJodHRwczovL21vYmlsZS1wcm9kLWxpdmVuZXNzLWF0dGVtcHRzLnMzLmFtYXpvbmF3cy5jb20vZDUyYzRhYzUtNzY2Zi00MzMzLTllNTgtNWZhNmFiYjM5YmU1LzA1NzAxNTE2MTk5L2xpdmVuZXNzL2F0dGVtcHRfMTcxMjg0Njc4OTYzOS5wbmc_QVdTQWNjZXNzS2V5SWQ9QVNJQVM2RjJYUEE3WEhJVFlTTVkmRXhwaXJlcz0xNzEyODQ4NTkwJlNpZ25hdHVyZT14N3FWUDVmcUElMkJPQkhWOGpzZFhJT2tKNXNhOCUzRCZYLUFtem4tVHJhY2UtSWQ9Um9vdCUzRDEtNjYxN2Y3YzQtMDUyNDBkN2QxZjY5NTA1ZjA0MmZkNzQwJTNCUGFyZW50JTNEMGZhYTdhMGQxY2U0OTQ5ZCUzQlNhbXBsZWQlM0QwJTNCTGluZWFnZSUzRGY0MmE4MWNmJTNBMCZ4LWFtei1zZWN1cml0eS10b2tlbj1JUW9KYjNKcFoybHVYMlZqRUE4YUNYTmhMV1ZoYzNRdE1TSkdNRVFDSUZpM3hwZG9YeVdYSEM4RUY3YmFKMlIlMkZGNzVJS3NWOTVsZXRCQndKN2FTNUFpQSUyQmN6RCUyRkh3VG1xa3dvT3F4T0pNUjgzTHclMkZBWWxXeFE4SjZjRnhPODdPVlNxYkF3aElFQVFhRERJd01qSTFOVEEzTVRJNU5TSU1wJTJGdkIwaVolMkZWdVB3WGV2SEt2Z0NqWllTbjRpSXZodUJBdVZFd3VXenp1aTd6azM5RVhydDUwdmNpUDhyYTd3c1FLUzFmWmRwTEZvMHdZWmk4Sm1NbnI1NXJBMUxSdkdqNjl3bW9Obm1PeGJOZ0hDOFQ5UXk1RGNlNUR5ZTJJbyUyQkRaYWVxaWNuMGdiWWhqQ3JmdWsxMkdNWnFlU2ZJJTJCRnFhM2lkVW5IVVRrR2wyblZMRmRKenNwaSUyQjc3VG5pRUd5dmtJQSUyRnAlMkJ5NWUyUmFnZXFZc0FzczdDS20yT25TSUx0ZXduWmx6aDRlRXFtSFZxZ0E0NzcydVVGUzd5JTJCWXdlOUc4dWF4dHB6ZFloTDFwYW5QVUtvTXdyTTVYJTJGYndMcEt3VmE5YnZhVlE0Mk1XQlVJZXdtenVRVjdtcUlJNEhKSnhIQU9oRlVTRHh0Z1ozZjdOZ3lFVSUyRk1TSzhrQWJ6cmhYY0E5TjJNUmU0dzZDWko3ZmRsN0NTUU41eWVIZGRwZk4wb1l3JTJGUFBCSkNhSkhMZjFWUlZ0Smw3YXQlMkZZSXF0R2c4OENDNWlOWjAlMkI0OXQ1MWolMkJtSXpwdVFKWXBhQ2JjVHlpeVRVVGxFUzBvd1hqQU1hdUhZYmNCdFNKMUFkQkY3c09rTndMaTdndUhTMW9zU3BENkY4bmZlcGdJMkNCV1RNa0pUbGpybWtEQzA2ZCUyQndCanFlQWYxek0xUDNGN3pybG8xYlExd2JZY3NEdWVacGp1ajhRTjliV0VyMnRJNiUyRkRxNXZ1NTl0diUyRldpNVdOSVpudkZHR0JPYkc2VGxBQm1lUkV3VGNjTGdQZk9TWFk4V1B6WjV6bzRLSDRpJTJGOHFDUXMxZ0klMkJIZU43VlJ4SWQyUGFKVTl3UVYlMkIzcm1UJTJGRWRnbCUyRkZhTUVvQXpBMXFjdGFEblpDbHZ2QjZhelRSWDVDcXY5dEhKZUJwTjRXcnBrQUxiRXQ2ZlR0UzNEVTFjaVhJcXNOMjdwJTJGIiwicGVyc29uSWQiOiIwNTcwMTUxNjE5OSIsImlhdCI6MTcxMjg0Njc5MX0.xSyoHUG5tjETMjIDPaBh13ek2lxfGbLjSZLa20oN780'
    const response$ = this.onboardingService.registraValidacaoFacial(this.fluxoDados.cpf, idObjetivo, this.jwt);
    await loading(response$.subscribe({
      next: () => {
        if (idObjetivo === TipoObjetivoEnum.TrocaSenhaLogin) {
          return this.router.navigate(['/esqueci-senha/redefinir-senha'], { state: { fluxoDados: this.fluxoDados } });
        }
        if (idObjetivo === TipoObjetivoEnum.TrocaSenhaCartao && !this.fluxoDados.criarSenhaCartao) {
          this.fluxoDados.confirmacao = true;
          this.fluxoService.set({
            fluxoDados: this.fluxoDados,
            credencial: this.credencial,
            jwt: this.jwt
          });
          return this.router.navigate(['/alterar-senha']);
        }
        if (this.fluxoDados.criarSenhaCartao) {
          return this.cadastrarSenha();
        } else {
          return this.router.navigate(['/cadastrar-senha'], {state:{fluxoDados: this.fluxoDados}});
        }
      }, error: (err) => {
        return this.router.navigate(['erro-validacao-caf'], {
          state: {
            fluxoDados: this.fluxoDados,
            dados: this.credencial,
            mensagem: err.error.msg
          }
        });
      }
    }));
  }

  cadastrarSenha() {
    const response$ = this.credencialService.salvarSenha(this.credencial.idCredencial, this.senha, this.authService.getToken() || '');
    loading(response$.pipe(
      concatMap((dados: any) => {
        if (dados.confirmacao) {
          return this.credencialService.desbloquearPortador(this.credencial.idCredencial);
        } else {
          return toast('Aconteceu algum erro ao tentar cadastrar sua senha');
        }
      })).subscribe({
      next: (value: any) => {
        if (value) {
          for (const cred of this.usuario.credenciais) {
            if (this.credencial.idCredencial === cred.idCredencial) {
              cred.status = 1;
            }
          }
          this.abrirModalSucesso();
        }
      }, error: err => {
        const message = err.error.msg ? err.error.msg : err.error.message;
        this.abrirModalErro(message);
      }
    }));
  }

  async abrirModalErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Criar senha',
        tituloTexto: 'Criar senha',
        mensagem: message,
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Ir para o início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.router.navigate(['/inicio']);
    } else {
      await this.validarSelfieCaf();
    }
  }

  async abrirModalSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Criar senha',
        tituloTexto: 'Senha',
        mensagem: 'Senha criada com sucesso! ',
        tituloBotao: `Ir para o início`
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.router.navigate(['/inicio']);
    }
  }

  async modalSucessoAlterarDispositivo() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Validado com sucesso',
        mensagem: 'Você concluiu a validação do dispositivo com sucesso, acesse o aplicativo e aproveite seus benefícios!',
        tituloBotao: 'Acessar o aplicativo'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.acessarAplicativo();
    }
  }

  async acessarAplicativo() {
    const auth = {
      cpf: this.fluxoDados.documento,
      senha: this.fluxoDados.senha
    };
    await this.logar(auth);
  }

  async logar(auth: any) {
    await loading(this.authService.entrar(auth).subscribe({
      next: () => {
        this.router.navigate(['inicio']);
      },
      error: error => {
        const message = error.message ? error.message : error.msg;
        toast(message || 'Não foi possível realizar o login, tente novamente.');
      }
    }));
  }

  async abrirModalError(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: message ? 'Ocorreu um erro' : 'Erro inesperado',
        mensagem: message ? message : 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      if (this.fluxoDados.tipo == 'troca-dispositivo') {
        await this.capturarValidarSelfie();
      } else {
        await this.validarSelfieCaf();
      }
    } else if (data.role == 'secundaria') {
      if (this.fluxoDados.idObjetivo === TipoObjetivoEnum.TrocaSenhaLogin || this.fluxoDados.tipo === 'troca-dispositivo') {
        this.authService.clearToken();
        await this.router.navigate(['/login']);
      } else {
         await this.router.navigate(['/inicio']);
      }
    }
  }
}
