import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ExtratoPage} from './extrato.page';

const routes: Routes = [
  {
    path: '',
    component: ExtratoPage
  },
  {
    path: 'comprovante',
    loadChildren: () => import('./comprovante/comprovante.module').then(m => m.ComprovantePageModule)
  },
  {
    path: 'comprovante/:endToEnd',
    loadChildren: () => import('./comprovante/comprovante.module').then(m => m.ComprovantePageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ExtratoPageRoutingModule {
}
