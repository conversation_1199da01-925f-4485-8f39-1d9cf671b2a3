section {
  margin-top: 1.5rem;
}

.cartao {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  ion-icon {
    font-size: 24px;
    margin-right: 1rem;
  }

  h1 {
    margin: 0;
    color: var(--ion-color-label);
    font-size: 14px;
    font-weight: var(--ion-font-weight-label);
    line-height: 1;
    margin-left: 10px;
  }
}

.meu-cartao {
  width: 100%;
  --background: var(--ion-background-login);
  --min-height: 40px;
  --padding-start: 0;
  --padding-top: 0;
  --border-radius: 8px;
  --inner-padding-end: 8px;
  --inner-padding-start: 8px;

  ion-label.right {
    margin-right: 0;
  }

  .itens {
    width: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  ion-icon {
    margin: 0;
  }
}

ion-segment {
  --background: var(--ion-color-background);
  //margin-top: 20px;

  ion-segment-button {
    --border-radius: 4px;
    --background: var(--ion-background-login);
    font-size: 12px;
    font-weight: 600;
    margin-right: 0.5rem;
    min-height: 22px;
    text-transform: none;
    --color: var(--ion-color-gray-50);
    --indicator-color: var(--ion-color-primary);
    --color-checked: var(--ion-color-primary-contrast)
  }

  ion-segment-button:last-child {
    margin-right: 0;
  }

  ion-segment-button::before {
    border: none;
  }
}

.section-baixar-extrato {
  //margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;

  h2 {
    margin: 0;
    color: var(--ion-color-text-subtitulo);
    font-weight: 600;
    font-size: 16px;
  }

  ion-button {
    --border-radius: 100%;
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    font-size: 14px;
    --background: var(--ion-background-login);
  }
}

.data-transacao {
  display: flex;
  align-items: center;
  justify-content: space-between;
  //margin-top: 20px;

  .dia {
    border-radius: 4px;
    background: var(--ion-color-primary);
    padding: 6px;

    p {
      color: var(--ion-color-secondary-contrast);
      font-size: 12px;
      font-weight: 600;
      margin: 0;
    }
  }

  ion-button {
    --border-radius: 100%;
    min-width: 32px;
  }
}

.transacao {
  //--background: var(--ion-background-login);
  --padding-start: 0;
  --border-radius: 8px;
  --inner-padding-end: 8px;
  //width: 100%;
  margin: 1rem 0;

  .tipo {
    background-color: #F0F0F0;
    border-radius: 100%;
    min-width: 32px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.5rem 0 0;
    position: relative;

    .status-nota-fiscal {
      position: absolute;
      bottom: 6px;
      right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 8px;
      height: 8px;
      border-radius: 50%;

      ion-icon {
        font-size: 7px;
        color: var(--ion-color-primary-contrast);
      }

      &.aprovada {
        background: #00A562;
      }

      &.reprovada {
        background: #E21B1B;
      }

      &.pendente {
        background: #FBBC04;
      }
    }
  }

  p {
    font-size: 14px;
    font-weight: 400;
    line-height: 119.5%;
    margin: 0;
  }

  ion-label {
    white-space: normal;
    margin: 0;

    h3 {
      color: var(--ion-color-gray-50);
      font-size: 12px;
      font-weight: 600;
      margin: 0 0 0.25rem 0;
    }

    p {
      font-size: 10px;
      font-weight: 400;
      line-height: 119.5%;
      margin: 0;
    }
  }

  .comprovante {
    margin-top: 6px;
    width: 20px;
    height: 15px;
    border-radius: 4px;
    background: var(--ion-background-login);
    display: flex;
    align-items: center;
    justify-content: center;

    ion-icon {
      font-size: 11px;
      margin-right: 1px;
    }
  }

  .valor-comprovante {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
}

ion-label {
  white-space: normal;

  h3 {
    color: var(--ion-color-gray-50);
    font-size: 12px;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
  }

  p {
    font-size: 10px;
    font-weight: 400;
    line-height: 119.5%;
    margin: 0;
  }
}

.estado-vazio {
  margin: 2.5rem 0;
}

ion-footer {
  padding: 1rem 1.5rem;

  .legenda {
    display: flex;
    align-items: center;
    gap: 1rem;

    .nota {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;

      div {
        width: 8px;
        height: 8px;
        background: #00A562;
        border-radius: 50%;
      }

      p {
        font-weight: 400;
        font-size: 10px;
        line-height: 120%;
        color: var(--ion-color-gray-50);
        margin: 0;
      }

      &:nth-child(2) {
        div {
          background: #E21B1B;
        }
      }

      &:nth-child(3) {
        div {
          background: #FBBC04;
        }
      }
    }
  }

  .aviso {
    display: flex;
    color: var(--ion-color-gray-50);
    align-items: center;
    gap: 0.5rem;

    p {
      font-weight: 600;
      font-size: 12px;
      line-height: 120%;
      margin: 1rem 0 0 0;
    }
  }

}

.icon-aviso {
  display: flex;
  margin-top: 2px;
}

ion-toolbar {
  --border-width: 0 !important;
}

ion-item {
  --background: transparent;
}
