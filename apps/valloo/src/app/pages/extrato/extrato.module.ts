import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {ExtratoPage} from './extrato.page';
import {ExtratoPageRoutingModule} from './extrato-routing.module';
import {
    CartoesModule,
    EstadoVazioModule,
    SearchbarModule,
    SkeletonModule,
    TitleToolbarModule,
    TituloSecaoModule
} from '@corporativo/components';
import {SaldoSimplesModule} from '@corporativo/saldo';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        ExtratoPageRoutingModule,
        TitleToolbarModule,
        TituloSecaoModule,
        SearchbarModule,
        EstadoVazioModule,
        SkeletonModule,
        SaldoSimplesModule,
        CartoesModule
    ],
  declarations: [ExtratoPage]
})
export class ExtratoPageModule {}
