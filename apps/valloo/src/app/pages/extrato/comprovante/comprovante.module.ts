import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {ComprovantePageRoutingModule} from './comprovante-routing.module';
import {ComprovantePage} from './comprovante.page';
import {NgxCaptureModule} from 'ngx-capture';
import {EstadoVazioModule, SkeletonModule, TitleToolbarModule} from '@corporativo/components';
import {PipesModule} from '@corporativo/shared';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ComprovantePageRoutingModule,
    TitleToolbarModule,
    NgxCaptureModule,
    PipesModule,
    EstadoVazioModule,
    SkeletonModule
  ],
  declarations: [ComprovantePage]
})
export class ComprovantePageModule {
}
