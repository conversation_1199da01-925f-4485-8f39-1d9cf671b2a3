section {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.linha {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.titulo {
  @media (max-height: 570px) {
    font-size: 9px;
  }
  @media (min-height: 570px) and (max-height: 736px) {
    font-size: 11px;
  }
  @media (min-height: 736px) {
    font-size: 14px;
  }

  font-weight: bold;
  color: var(--ion-color-gray-50);
  text-align: left;
  padding-left: 0;
}

.descricao {
  @media (max-height: 570px) {
    font-size: 9px;
  }
  @media (min-height: 570px) and (max-height: 736px) {
    font-size: 11px;
  }
  @media (min-height: 736px) {
    font-size: 14px;
  }

  text-align: right;
  padding-right: 0;
}

ion-text {
  color: var(--ion-color-text-subtitulo);
  font-weight: var(--ion-font-weight-subtitulo);
  font-size: 16px;
}

ion-grid {
  margin: 0;
  padding-left: 0;
  padding-right: 0;
}

.image {
  margin-top: 50px;
}

//ion-row{
//  margin-top: 5px;
//}

p {
  font-size: 14px;
  font-weight: 500;
  line-height: 19px;
  margin: 0 0 1rem 0;

  b {
    color: var(--ion-color-gray-50);
  }
}

img {
  width: 30%;
}

hr {
  border-bottom: 1px solid;
  opacity: 0.2;
}

h1 {
  font-size: var(--ion-font-size-h1);
  font-weight: var(--ion-font-weight-h1);
}

h3 {
  font-weight: var(--ion-font-weight-h1);
  font-size: 20px;
}

ion-footer {
  background: var(--ion-color-background);
  padding: 1rem 1.5rem 1rem 1.5rem;
}

.estado-vazio {
  margin: 2.5rem 0;
}

ion-modal {
  --border-radius: 1.5rem;
}

.opcoes {
  background: transparent;
  margin-top: 1rem;

  ion-item {
    --background: transparent;
    margin-bottom: 1rem;
    --padding-start: 0;

    ion-label {
      color: var(--ion-color-gray-50);
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-left: 1rem;
    }

    ion-button {
      --border-radius: 50%;
      width: 30px;
      height: 30px;
      min-height: 0;
    }
  }
}

.endToEnd {
  padding-top: 20px;
}

.titulo-compartilhar {
  text-align: center;
  margin-bottom: 65px;
  font-size: 21px;
  transform: translateY(40px);
}

.div-image {
  display: flex;
  justify-content: center;
  transform: translateY(-9px);
}

.imagem-valloo {
  width: 27%;
}

