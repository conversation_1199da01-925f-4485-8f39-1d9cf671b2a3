import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {Observable, of} from 'rxjs';
import {tap} from 'rxjs/operators';
import {Platform} from '@ionic/angular';
import {Directory, Filesystem} from '@capacitor/filesystem';
import {FileOpener} from '@capacitor-community/file-opener';
import {NgxCaptureService} from 'ngx-capture';
import {Share} from '@capacitor/share';
import {environment} from '../../../../environments/environment';
import {
  AuthService,
  Credencial,
  Extrato,
  ExtratoService, LogoInstituicaoEnum,
  PixService,
  TipoTransacaoEnum,
  Usuario
} from '@corporativo/shared';
import {loading} from '@utils/loading.util';
import {toast} from '@utils/toast.util';

@Component({
  selector: 'app-comprovante',
  templateUrl: './comprovante.page.html',
  styleUrls: ['./comprovante.page.scss'],
  standalone: false
})
export class ComprovantePage implements OnInit {
  transacao: any;
  qrcode: any;
  recarga: any;
  ted: any;
  comprovante$!: Observable<any>;
  usuario!: Usuario;
  endToEnd!: string | null;
  operacao: any;
  @ViewChild('comprovantePrint', {static: true}) screen!: ElementRef;
  idConta: any;
  conta: any;
  possuiComprovante = true;
  credencial: Credencial;
  isModalComprovanteOpen = false;
  isPrint = false;
  logo = LogoInstituicaoEnum[environment.app as keyof typeof LogoInstituicaoEnum];
  movimentoPix: any;
  apresentarBotao = false;
  temNotaFiscal: any;

  constructor(
    private router: Router,
    private authService: AuthService,
    private extratoService: ExtratoService,
    private platform: Platform,
    private activatedRoute: ActivatedRoute,
    private pixService: PixService,
    private captureService: NgxCaptureService
  ) {
    this.usuario = this.authService.getUser();
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
    // @ts-ignore
    this.credencial = this.usuario.credenciais.find((x: Credencial) => {
      const conta = x.contas.find(c => c.idConta == this.idConta);
      return conta ? x : null;
    });
    const conta = this.credencial.contas.find(c => c.idConta == this.idConta);
    if (conta) {
      this.conta = conta;
      this.conta.agencia = conta.idInstituicao;
    }
    this.temNotaFiscal = this.router.getCurrentNavigation()?.extras?.state?.['temNotaFiscal'];
    this.transacao = this.router.getCurrentNavigation()?.extras?.state?.['transacao'];
    if (this.transacao) {
      this.buscarComprovante(this.transacao);
    }
    this.qrcode = this.router.getCurrentNavigation()?.extras?.state?.['qrcode'];
    if (this.qrcode) {
      this.buscarPorTransactionId(this.qrcode);
    }
    this.recarga = this.router.getCurrentNavigation()?.extras?.state?.['recarga'];
    if (this.recarga) {
      this.comprovante$ = of(this.recarga);
    }
    const boleto = this.router.getCurrentNavigation()?.extras?.state?.['boleto'];
    if (boleto) {
      boleto.status = 200;
      this.apresentarBotao = true;
      this.comprovante$ = of(boleto);
    }
    this.endToEnd = this.activatedRoute.snapshot.paramMap.get('endToEnd');
    if (this.endToEnd) {
      this.buscarPorEndToEnd();
    }
    this.ted = this.router.getCurrentNavigation()?.extras?.state?.['ted'];
    if (this.ted) {
      this.comprovante$ = of(this.ted);
    }
  }

  ngOnInit() {
  }

  buscarComprovante(transacao: Extrato) {
    const operacao: any = {
      idConta: this.idConta,
      rrn: transacao.idTransacao,
      idTranlog: transacao.idTranlog
    };
    const tipo: any = {
      '172': TipoTransacaoEnum.QrcodeElo, // a vista
      '178': TipoTransacaoEnum.QrcodeElo, // debito a vista
      '180': TipoTransacaoEnum.QrcodeElo, // voucher
      '360': TipoTransacaoEnum.QrcodeElo, // credito internacional
      '556': TipoTransacaoEnum.TransferenciaInternaPortador,
      '558': TipoTransacaoEnum.Ted,
      '666': TipoTransacaoEnum.TransferenciaInternaPortador,
      '670': TipoTransacaoEnum.Boleto,
      '680': TipoTransacaoEnum.Recarga,
      '698': TipoTransacaoEnum.Recarga,
      '876': TipoTransacaoEnum.TransferenciaInternaPortador,
      '878': TipoTransacaoEnum.TransferenciaInternaPortador,
      '880': TipoTransacaoEnum.Pix,
      '884': TipoTransacaoEnum.Pix,
      '888': TipoTransacaoEnum.Pix, // devolucao parcial
      '890': TipoTransacaoEnum.Pix, // devolucao total
      '934': TipoTransacaoEnum.Pix,
      '981': TipoTransacaoEnum.Boleto
    };
    operacao.tipoTransacao = tipo[transacao.statusTransacao];

    if (!operacao.tipoTransacao) {
      this.possuiComprovante = false;
      this.comprovante$ = of({status: 0});
      return;
    }
    this.comprovante$ = this.extratoService.buscarComprovante(operacao).pipe(
      tap((x: any) => {
        this.apresentarBotao = true;
        if (x.status == 404) {
          this.possuiComprovante = false;
          this.apresentarBotao = false;
        }
        if (tipo[transacao.statusTransacao] == TipoTransacaoEnum.Pix) {
          this.operacao = x.pix;
        }
      })
    );
  }

  buscarPorEndToEnd() {
    this.comprovante$ = this.pixService.buscarComprovante({
      idConta: this.idConta,
      agencia: this.conta.idInstituicao
    }, this.endToEnd).pipe(
      tap((x: any) => {
        this.apresentarBotao = true;
        this.movimentoPix = x;
      })
    );

  }

  buscarPorTransactionId(qrcode: any) {
    const operacao: any = {
      idConta: this.idConta,
      transactionId: qrcode.transactionId,
      tipoTransacao: TipoTransacaoEnum.QrcodeElo
    };
    this.comprovante$ = this.extratoService.buscarComprovante(operacao).pipe();
  }

  buscarPorProtocoloId(protocoloId: any) {
    const operacao: any = {
      idConta: this.idConta,
      protocoloId: protocoloId,
      transactionId: protocoloId,
      tipoTransacao: TipoTransacaoEnum.Recarga
    };
    this.comprovante$ = this.extratoService.buscarComprovante(operacao).pipe();
  }

  async compartilhar() {
    if (!this.platform.is('hybrid')) {
      return;
    }
    this.isPrint = true;
    setTimeout(async () => {
      await loading(
        this.captureService.getImage(this.screen.nativeElement, true).subscribe(
          {
            next: async (imageBase64) => {
              this.isPrint = false;
              const image = imageBase64.replace('data:image/png;base64,', '');
              const currentDate = new Date().toLocaleString().replace(/[,:\s\/]/g, '-');
              const result = await Filesystem.writeFile({
                path: `comprovante-${currentDate}.png`,
                data: image,
                directory: Directory.Documents
              });
              await Share.share({
                title: 'Valloo comprovante',
                text: `Comprovante de operação`,
                dialogTitle: 'Compartilhar',
                url: result.uri
              });
            }, error: async () => {
              await toast('Houve um erro inesperado ao compartilhar. Tente novamente!');
            }
          }
        )
      );
    }, 100);
  }

  async salvar() {
    if (!this.platform.is('hybrid')) {
      return;
    }
    this.isPrint = true;
    setTimeout(async () => {
      await loading(
        this.captureService.getImage(this.screen.nativeElement, true).subscribe(
          {
            next: async (imageBase64) => {
              this.isPrint = false;
              const image = imageBase64.replace('data:image/png;base64,', '');
              const currentDate = new Date().toLocaleString().replace(/[,:\s\/]/g, '-');
              const result = await Filesystem.writeFile({
                path: `comprovante-${currentDate}.png`,
                data: image,
                directory: Directory.Documents
              });
              FileOpener
                .open({filePath: result.uri})
                .then(() => console.log('Arquivo foi aberto'))
                .catch((e: any) => console.log('Erro ao abrir arquivo', e));
              await toast('Imagem salva com sucesso!');
            }, error: async () => {
              await toast('Houve um erro inesperado ao salvar a imagem. Tente novamente!');
            }
          }
        )
      );
    }, 100);
  }

  devolver() {
    return this.router.navigate([`/pix/${this.idConta}/enviar/devolver`], {state: {operacao: this.operacao}});
  }

  setOpenModalComprovante() {
    this.isModalComprovanteOpen = true;
  }

  ionModalDidDismiss() {
    this.isModalComprovanteOpen = false;
  }

  anexarNotaFiscal() {
    const operacao: any = {
      idConta: this.idConta,
      idTranlog: this.movimentoPix ? this.movimentoPix.pix.idTranLog : this.transacao.idTranlog,
      valor: this.movimentoPix ? this.movimentoPix.pix.valor : this.transacao.valorTransacao,
      descricaoTransacao: this.movimentoPix ? this.movimentoPix.pix.descricaoTransacao : this.transacao.descTransacao,
      dataHoraTransacao: this.movimentoPix ? this.movimentoPix.pix.data : this.transacao.dataTransacao
    };
    this.router.navigate([`/nota-fiscal/${this.idConta}/anexar`], {state: {operacao}});
  }

  verNotaFiscal() {
    this.router.navigate([`/nota-fiscal/${this.idConta}/detalhe/${this.transacao.idNotaFiscal}`]);
  }

  voltar() {
    return this.router.navigate(['/extrato/' + this.idConta]);
  }
}
