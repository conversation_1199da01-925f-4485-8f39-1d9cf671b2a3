<ion-header class="ion-no-border">
  <ion-toolbar olor="transparent">
    <mobile-title-toolbar *ngIf="!isPrint" [mostrarVoltar]="!endToEnd && !ted" [rotaPadrao]="'/extrato/' + idConta"
                    [mostrarBotaoFim]="false">Comprovante
    </mobile-title-toolbar>
    <div class="div-image">
      <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <section id="comprovantePrint" #comprovantePrint>

    <ng-container *ngIf="comprovante$ | async as comprovante; else carregando">
      <ng-container *ngIf="(comprovante.status && comprovante.status !== 404) else estadoVazio">
        <article class="mensagem-retorno" *ngIf="comprovante.mensagem">
          <h2>{{ comprovante.mensagem.titulo }}</h2>
          <p>{{ comprovante.mensagem.descricao }}</p>
        </article>
        <article class="comprovante" *ngIf="comprovante.pix">
          <h1 *ngIf="isPrint" class="titulo-compartilhar">Comprovante Pix</h1>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Realizado em</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.data | date: 'dd/MM/yyyy' }} às
                {{ comprovante.pix.data | date: 'HH:mm' }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Tipo transação</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">Pix</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Valor</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.valor | currency:'BRL' }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid class="ion-margin-bottom">
            <ion-row>
              <ion-col
                class="titulo">{{ comprovante.pix.descricao === "01988" || comprovante.pix.descricao === "01990" ? 'Motivo da devolução' : 'Descrição' }}
              </ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col
                class="descricao">{{ comprovante.pix.descricao === "01988" ? 'Pix não aceito pelo recebedor' : comprovante.pix.campoLivre === '' ? '-' : (comprovante.pix.campoLivre || '-') }}
                {{ comprovante.pix.descricao === "01990" ? 'Pix devolvido' : '' }}
              </ion-col>
            </ion-row>
          </ion-grid>

          <div class="ion-margin-top" *ngIf="comprovante.pix.natureza === 'D'">
            <ion-text>Destino</ion-text>
          </div>
          <div class="ion-margin-top" *ngIf="comprovante.pix.natureza !== 'D'">
            <ion-text>Origem</ion-text>
          </div>
          <hr/>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Nome</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.origemMovimento.nome | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">CPF</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.origemMovimento.inscricaoNacional | maskHideCpfCnpj }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Instituição</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.origemMovimento.nomeInstituicao | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Agência</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.origemMovimento.agencia }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid class="ion-margin-bottom">
            <ion-row>
              <ion-col class="titulo">Conta</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.origemMovimento.numero }}</ion-col>
            </ion-row>
          </ion-grid>

          <div class="ion-margin-top" *ngIf="comprovante.pix.natureza !== 'D'">
            <ion-text>Destino</ion-text>
          </div>
          <div class="ion-margin-top" *ngIf="comprovante.pix.natureza === 'D'">
            <ion-text>Origem</ion-text>
          </div>
          <hr/>

          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Nome</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.conta.nome | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">CPF/CNPJ</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.conta.inscricaoNacional | maskHideCpfCnpj }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Instituição</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.conta.nomeInstituicao | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid class="ion-margin-bottom">
            <ion-row>
              <ion-col class="titulo">Conta</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.pix.conta.numero }}</ion-col>
            </ion-row>
          </ion-grid>
          <p class="endToEnd"><b>ID da transação</b></p>
          <p>{{ comprovante.pix.endToEnd }}</p>
        </article>
        <article class="comprovante" *ngIf="comprovante.ted">
          <h1 *ngIf="isPrint" class="titulo-compartilhar">Comprovante TED</h1>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Realizado em</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.ted.dataTransacao }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Tipo transação</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">TED</ion-col>
            </ion-row>
          </ion-grid>
          <p *ngIf="false">Transferência em processamento. o comprovante não é válido até confirmação. um
            novo comprovante será emitido.</p>
          <ion-grid class="ion-margin-bottom">
            <ion-row>
              <ion-col class="titulo">Valor</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.ted.valor }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-text>Origem</ion-text>
          <hr/>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Nome</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ usuario.nomeCompleto | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Instituição</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ conta.agencia }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Conta</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ conta.idConta }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid class="ion-margin-bottom">
            <ion-row>
              <ion-col class="titulo">CPF</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ usuario.documento | maskHideCpfCnpj }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-text>Destino</ion-text>
          <hr/>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Nome</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.ted.destinatario | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Instituição</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.ted.banco | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Agência</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.ted.agencia }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Conta</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.ted.conta }}</ion-col>
            </ion-row>
          </ion-grid>
        </article>
        <article class="comprovante" *ngIf="comprovante.boleto">
          <h1 *ngIf="isPrint" class="titulo-compartilhar">Comprovante Boleto</h1>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Realizado em</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.dataOperacao }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Tipo transação</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">Pagamento</ion-col>
            </ion-row>
          </ion-grid>
          <p *ngIf="false">Pagamento em processamento. o comprovante não é válido até confirmação. um
            novo comprovante será emitido.</p>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Valor</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.valorCobrado }}</ion-col>
            </ion-row>
          </ion-grid>
          <hr/>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Protocolo</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.protocoloId || '-' }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Código de barras</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.codigoDeBarras }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Beneficiário</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ (comprovante.boleto.destinatario | titlecase) || '-' }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Pagador</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ usuario.nomeCompleto | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Conta</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.idConta }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Data do pagamento</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.dataOperacao }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Valor título</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.valorTitulo }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Valor cobrado</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.boleto.valorCobrado }}</ion-col>
            </ion-row>
          </ion-grid>
          <p><b>Autenticação</b></p>
          <p>{{ comprovante.boleto.autenticacao }}</p>
        </article>
        <article class="comprovante" *ngIf="comprovante.compraElo">
          <h1 *ngIf="isPrint" class="titulo-compartilhar">Comprovante Compra Elo</h1>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Realizado em</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.compraElo.dataOperacao | date: 'dd/MM/yyyy' }}
                às {{ comprovante.compraElo.dataOperacao | date: 'HH:mm' }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Tipo transação</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.compraElo.isQrCodeElo ? 'Compra qrcode' : 'Compra cartão' }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <p *ngIf="false">Pagamento em processamento. o comprovante não é válido até confirmação. um
            novo comprovante será emitido.</p>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Valor</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.compraElo.valor | currency:'BRL' }}</ion-col>
            </ion-row>
          </ion-grid>
          <hr/>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Protocolo</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col
                class="descricao">{{ comprovante.compraElo.rrn || comprovante.compraElo.numeroAprovacao || '-' }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Estabelecimento</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ (comprovante.compraElo.estabelecimento | titlecase) || '-' }}</ion-col>
            </ion-row>
          </ion-grid>
        </article>
        <article class="comprovante" *ngIf="comprovante.recargaCelular">
          <h1 *ngIf="isPrint" class="titulo-compartilhar">Comprovante Recarga</h1>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Realizado em</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.recargaCelular.dataOperacao | date: 'dd/MM/yyyy' }}
                às {{ comprovante.recargaCelular.dataOperacao | date: 'HH:mm' }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Tipo transação</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ 'Recarga' }}</ion-col>
            </ion-row>
          </ion-grid>
          <p *ngIf="false">Pagamento em processamento. o comprovante não é válido até confirmação. um
            novo comprovante será emitido.</p>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Pagador</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.recargaCelular.pagador | titlecase }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Valor</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.recargaCelular.valor | currency:'BRL' }}</ion-col>
            </ion-row>
          </ion-grid>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Número</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.recargaCelular.ddd }} {{ comprovante.recargaCelular.numero }}
              </ion-col>
            </ion-row>
          </ion-grid>
          <hr/>
          <ion-grid>
            <ion-row>
              <ion-col class="titulo">Protocolo</ion-col>
              <ion-col size="auto"></ion-col>
              <ion-col class="descricao">{{ comprovante.recargaCelular.protocoloId || '-' }}</ion-col>
            </ion-row>
          </ion-grid>
        </article>
      </ng-container>
      <ng-template #estadoVazio>
        <article class="estado-vazio">
          <mobile-estado-vazio iconName="extrato"
                               message="Está transação ainda está em processamento ou não possui comprovante.">
          </mobile-estado-vazio>
        </article>
      </ng-template>
    </ng-container>
    <ng-template #carregando>
      <article class="comprovante">
        <div *ngFor="let i of [1, 2, 3, 4, 5, 6, 7, 8]">
          <p>
            <mobile-skeleton width="60%"></mobile-skeleton>
          </p>
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </div>
      </article>
    </ng-template>
    <div class="ion-text-center image" *ngIf="possuiComprovante">
      <img [src]="logo" alt="logo da instituição">
    </div>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn ion-margin-bottom" expand="block" (click)="setOpenModalComprovante()" *ngIf="possuiComprovante && apresentarBotao">
    Compartilhar
  </ion-button>
  <ion-button class="ion-margin-bottom" *ngIf="operacao && operacao.instrucoesDevolucao.permiteDevolucao"
              expand="block"
              (click)="devolver()">
    Devolver
  </ion-button>
  @if (temNotaFiscal && (apresentarBotao && transacao && transacao.idNotaFiscal)) {
    <ion-button fill="outline" expand="block" (click)="verNotaFiscal()">Ver nota fiscal</ion-button>
  } @else if (temNotaFiscal && (apresentarBotao && transacao != null ? transacao.sinal == -1 : apresentarBotao)) {
    <ion-button fill="outline" expand="block" (click)="anexarNotaFiscal()">Anexar nota fiscal</ion-button>
  }
  <ion-button *ngIf="temNotaFiscal && !possuiComprovante && transacao.idNotaFiscal" fill="outline" expand="block" (click)="verNotaFiscal()">Ver nota fiscal</ion-button>
  <ion-button *ngIf="temNotaFiscal && !possuiComprovante && !transacao.idNotaFiscal" fill="outline" expand="block" (click)="anexarNotaFiscal()">Anexar nota fiscal</ion-button>
  <ion-button fill="outline" class="ion-margin-top" expand="block" (click)="voltar()">Voltar</ion-button>
</ion-footer>
<ion-modal [isOpen]="isModalComprovanteOpen" [initialBreakpoint]="0.3" [breakpoints]="[0, 0.3]"
           (ionModalDidDismiss)="ionModalDidDismiss()">
  <ng-template>
    <ion-content class="ion-padding-top">
      <section class="ion-margin-top">
        <ion-list lines="none" class="opcoes">
          <ion-item button="true" detail="true" (click)="compartilhar()" class="ion-margin-bottom">
            <ion-button slot="start" color="secondary">
              <ion-icon name="arrow-redo-outline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-label>Compartilhar</ion-label>
          </ion-item>
          <ion-item button="true" detail="true" (click)="salvar()">
            <ion-button slot="start" color="secondary">
              <ion-icon name="download-outline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-label>Salvar</ion-label>
          </ion-item>
        </ion-list>
      </section>
    </ion-content>
  </ng-template>
</ion-modal>
