<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar [buttonModal]="true" (closeEmitter)="irParaInicio()">Extrato</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content class="content-default">
    <section>
        <div class="subsection">
            <mobile-titulo-secao>Meu cartão</mobile-titulo-secao>
        </div>
        <mobile-cartoes (trocar)="pegarCartao($event)" [idCredencial]="idCredencial" [refresh]="false"></mobile-cartoes>
    </section>
  <section>
      <mobile-saldo-simples (trocarConta)="trocarConta($event)" [contas]="contas" [idConta]="idConta"></mobile-saldo-simples>
  </section>
    <section>
        <mobile-searchbar (changeInput)="filtrarTransacoes($event)" [clear]="limpar"></mobile-searchbar>
    </section>
    <section>
        <ion-segment [scrollable]="true" [value]="periodo" (ionChange)="alterarPeriodo($event)">
            <ion-segment-button value="15">
                15 dias
            </ion-segment-button>
            <ion-segment-button value="30">
                30 dias
            </ion-segment-button>
            <ion-segment-button value="45">
                45 dias
            </ion-segment-button>
            <ion-segment-button value="outro">
                Outro
            </ion-segment-button>
        </ion-segment>
    </section>
  <section class="section-baixar-extrato">
    <h2>Transações</h2>
    <ion-button size="small" (click)="baixarExtrato()">
      <ion-icon name="baixar" slot="icon-only"></ion-icon>
    </ion-button>
  </section>
  <ng-container *ngIf="transacoes$ | async as transacoes; else carregando">
    <ng-container *ngIf="transacoes.length; else estadoVazio">
      <ng-container *ngFor="let transacao of transacoes; let i = index">
        <div class="data-transacao">
          <div class="dia">
            <p>{{ transacao.data | date:'dd/MM/yyyy' }}</p>
          </div>
        </div>
        <ion-item lines="none" class="transacao" [button]="true" [detail]="false"
                  *ngFor="let operacao of transacao.operacoes" (click)="verComprovante(operacao)">
          <div class="tipo">
            <ion-icon name="document-text-outline" color="secondary"></ion-icon>
            @if (operacao.notaFiscalSituacao) {
              @switch (operacao.notaFiscalSituacao) {
                @case (APROVADA_PARCIALMENTE) {
                  <div class="status-nota-fiscal pendente">
                    <ion-icon name="alert"></ion-icon>
                  </div>
                }
                @case (APROVADA) {
                  <div class="status-nota-fiscal aprovada">
                    <ion-icon name="checkmark-sharp"></ion-icon>
                  </div>
                } @case (REPROVADA) {
                  <div class="status-nota-fiscal reprovada">
                    <ion-icon name="close-sharp"></ion-icon>
                  </div>
                }
                @default {
                  <div class="status-nota-fiscal pendente">
                    <ion-icon name="alert"></ion-icon>
                  </div>
                }
              }
            }
          </div>
          <ion-label>
            <h3>{{ operacao.descFunctionCode }}</h3>
            @if (operacao.ss) {
              <p>{{ operacao.descLocal | titlecase }} às {{ operacao.dataTransacao | date:'HH:mm' }}</p>
            } @else {
              <p>{{ operacao.descTransacaoMinima === '-' ? (operacao.descTransacao | titlecase) : (operacao.descTransacaoMinima | titlecase) }}
                às {{ operacao.dataTransacao | date:'HH:mm' }}</p>
            }
          </ion-label>
          <div slot="end" class="valor-comprovante">
            <p>{{ operacao.sinal === 1 ? '+' : '-' }} {{ operacao.valorTransacao | currency:'BRL' }}</p>
            <div class="comprovante" *ngIf="operacao.possuiComprovante">
              <ion-icon name="comprovante"></ion-icon>
            </div>
          </div>
        </ion-item>
      </ng-container>
    </ng-container>
    <ng-template #estadoVazio>
      <div class="estado-vazio">
        <mobile-estado-vazio [message]="'Nenhuma transação encontrada no período selecionado.'"
                             [iconName]="'extrato'"></mobile-estado-vazio>
      </div>
    </ng-template>
  </ng-container>
  <ng-template #carregando>
    <ng-container *ngFor="let i of [1,2,3]">
      <div class="data-transacao">
        <div class="dia">
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </div>
      </div>
      <ion-item lines="none" class="transacao">
        <div class="tipo" slot="start"></div>
        <ion-label>
          <h3>
            <mobile-skeleton></mobile-skeleton>
          </h3>
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </ion-label>
        <p slot="end">
          <mobile-skeleton></mobile-skeleton>
        </p>
      </ion-item>
    </ng-container>
  </ng-template>
</ion-content>
<ion-footer *ngIf="temNotaFiscal" class="ion-no-border">
  <div class="legenda">
    <div class="nota">
      <div></div>
      <p>Nota aprovada</p>
    </div>
    <div class="nota">
      <div></div>
      <p>Nota reprovada</p>
    </div>
    <div class="nota">
      <div></div>
      <p>Nota pendente</p>
    </div>
  </div>
  <div class="aviso">
    <div class="icon-aviso">
      <ion-icon name="alert-circle-outline"></ion-icon>
    </div>
    <p><b>AVISO:</b> para anexar sua nota, selecione o comprovante e clique em "<b>Anexar nota fiscal</b>".</p>
  </div>
</ion-footer>
