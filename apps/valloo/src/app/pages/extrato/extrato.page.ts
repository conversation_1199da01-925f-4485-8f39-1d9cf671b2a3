import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {format, parseISO} from 'date-fns';
import {ModalController} from '@ionic/angular';
import {
  AuthService,
  Conta,
  Credencial,
  ExtratoService,
  StorageService,
  TipoProdutoEnum,
  Transacao,
  Usuario
} from '@corporativo/shared';
import {merge, Observable} from 'rxjs';
import {NotaFiscalSituacaoEnum} from '../../../../../../libs/shared/src/lib/enums/nota-fiscal-situacao.enum';
import {ModalPeriodoDataComponent} from '@corporativo/modals';

@Component({
  selector: 'mobile-extrato',
  templateUrl: './extrato.page.html',
  styleUrls: ['./extrato.page.scss'],
  standalone: false
})
export class ExtratoPage implements OnInit {
  transacoes$!: Observable<Transacao[]>;
  dados: any;
  periodo = '15';
  usuario!: Usuario;
  numeroCartao: string;
  limpar = false;
  idCredencial!: number;
  credencial: any;
  APROVADA = NotaFiscalSituacaoEnum.Aprovada;
  REPROVADA = NotaFiscalSituacaoEnum.Reprovada;
  PENDENTE = NotaFiscalSituacaoEnum.Pendente;
  APROVADA_PARCIALMENTE = NotaFiscalSituacaoEnum.AprovadaParcialmente;
  temNotaFiscal: boolean = false;
  contas: Conta[];
  conta: any;
  idConta!: number;
  emPontos = false;

  constructor(
    private router: Router,
    private extratoService: ExtratoService,
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private modalController: ModalController,
    private storageService: StorageService
  ) {
    this.usuario = this.authService.getUser();
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      for (const credencial of this.usuario.credenciais) {
        const conta = credencial.contas.find(c => c.idConta == this.idConta);
        if (conta) {
          this.idCredencial = credencial.idCredencial;
          this.contas = credencial.contas;
          this.conta = conta;
          this.emPontos = this.conta.tipoProduto == TipoProdutoEnum.Moedeiro;
        }
      }
    }
  }

  ngOnInit() {
  }

  ionViewDidEnter() {
    this.buscarExtratoPeriodo(this.periodo);
  }

  buscarExtratoPorPeriodo(periodo: string) {
    const {dtInicio, dtFim} = this.converterPeriodo(periodo);
    this.buscarExtrato(dtInicio, dtFim);
  }

  private converterPeriodo(periodo: string) {
    const dataInicio = new Date();
    const dataFim = new Date();
    dataInicio.setDate(dataFim.getDate() - parseInt(periodo, 10));
    const dtInicio = format(dataInicio, 'yyyy-MM-dd');
    const dtFim = format(dataFim, 'yyyy-MM-dd');
    return {dtInicio, dtFim};
  }

  buscarExtrato(dtInicio: string, dtFim: string) {
    const idConta = this.conta.idConta;
    const permissoes = this.storageService.getProdutosPermissoes();
    permissoes.filter((x: any) => {
      if (x.idConta == idConta && x.temNotaFiscal) {
        this.temNotaFiscal = true;
        this.transacoes$ = merge(
          this.extratoService.buscarExtratoComNotaFiscal(idConta, dtInicio, dtFim),
          this.extratoService.getTransacoesFiltradas()
        );
      } else if (x.idConta == idConta && !x.temNotaFiscal) {
        this.temNotaFiscal = false;
        this.transacoes$ = merge(
          this.extratoService.buscarExtrato(idConta, dtInicio, dtFim),
          this.extratoService.getTransacoesFiltradas()
        );
      }
    })

  }

  alterarPeriodo(customEvent: CustomEvent) {
    this.limparPesquisa();
    if (customEvent.detail.value != 'outro') {
      this.buscarExtratoPorPeriodo(customEvent.detail.value);
    } else {
      this.selecionarPeriodo();
    }
  }

  async selecionarPeriodo() {
    const modal = await this.modalController.create({
      component: ModalPeriodoDataComponent,
    });
    modal.present();

    const {data} = await modal.onWillDismiss();
    if (data) {
      const dataInicio = format(parseISO(data.dataInicio), 'yyyy-MM-dd');
      const dataFim = format(parseISO(data.dataFim), 'yyyy-MM-dd');
      this.buscarExtrato(dataInicio, dataFim);
    }

  }

  limparPesquisa() {
    this.limpar = !this.limpar;
  }

  filtrarTransacoes(valor: string) {
    this.extratoService.filtrarTransacoes(valor);
  }

  baixarExtrato() {
    this.router.navigate(['/baixar-extrato'], {state: {idConta: this.usuario.credencial.idConta}});
  }

  irParaInicio() {
    return this.router.navigate(['/inicio']);
  }

  verComprovante(transacao: any) {
    if (!transacao.possuiComprovante) {
      return;
    }
    this.router.navigate([`/extrato/${this.credencial.idConta}/comprovante`], {
      state: {
        transacao,
        idConta: this.conta.idConta,
        temNotaFiscal: this.temNotaFiscal
      }
    });
  }

  pegarCartao(credencial: Credencial) {
    this.credencial = credencial;
    this.buscarExtratoPorPeriodo(this.periodo);
  }

  trocarConta(conta: Conta) {
    this.conta = conta;
    this.emPontos = this.conta.tipoProduto == TipoProdutoEnum.Moedeiro;
    this.buscarExtratoPeriodo(this.periodo);
  }

  buscarExtratoPeriodo(periodo: string) {
    const { dtInicio, dtFim } = this.converterPeriodo(periodo);
    this.buscarExtrato(dtInicio, dtFim);
  }
}
