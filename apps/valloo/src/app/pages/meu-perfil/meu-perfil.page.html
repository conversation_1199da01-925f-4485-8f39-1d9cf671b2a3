<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar>Meu perfil</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <section *ngIf="usuario">
    <article class="avatar">
      <ion-avatar class="ion-activatable ripple-parent" (click)="abrirOpcoes()">
        <ion-img *ngIf="!usuario.imagemPerfil" [src]="'/assets/images/avatar.png'"/>
        <ion-img *ngIf="usuario.imagemPerfil" [src]="usuario.imagemPerfil" alt="avatar"/>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-avatar>
      <ion-icon class="icon-color" name="camera-outline"></ion-icon>
    </article>
    <article>
      <div>
        <p>Nome</p>
        <p><b>{{ usuario.nomeCompleto | titlecase }}</b></p>
      </div>
      <div>
        <p>Documento</p>
        <p><b>{{ usuario.documento }}</b></p>
      </div>
      <div>
        <p>E-mail</p>
        <p><b>{{ usuario.email | lowercase }}</b></p>
      </div>
    </article>
  </section>
</ion-content>
