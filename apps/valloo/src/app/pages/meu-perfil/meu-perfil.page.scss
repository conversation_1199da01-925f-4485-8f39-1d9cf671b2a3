section {
  article:first-child {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15% 20%;

    ion-thumbnail {
      width: 147px;
      height: 139px;
    }
  }

  article:last-child {
    div {
      margin-bottom: 1.5rem;

      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 19px;
        color: var(--ion-color-quartenary);
        margin: 0 0 0.5rem 0;

        b {
          font-weight: 600;
          color: var(--ion-color-gray-50)
        }
      }
    }
  }
}

.avatar {

  text-align: center;

  ion-avatar {
    width: 120px;
    height: 120px;
  }

  .btn-alterar-imagem {
    text-decoration: underline;
    font-size: .9rem;
  }

  ion-icon {
    background: var(--ion-color-secondary);
    border-radius: 25px;
    margin-top: 88px;
    transform: translateX(-35px);
    padding: 7px;
    color: white;
  }

}
