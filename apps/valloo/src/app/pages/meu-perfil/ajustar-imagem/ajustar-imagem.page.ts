import { Component } from '@angular/core';
import { ImageCroppedEvent, LoadedImage } from 'ngx-image-cropper';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import {StorageService} from '@corporativo/shared';
import {ModalErroComponent} from '@corporativo/modals';

@Component({
  selector: 'app-ajustar-imagem',
  templateUrl: './ajustar-imagem.page.html',
  styleUrls: ['./ajustar-imagem.page.scss'],
  standalone: false
})
export class AjustarImagemPage {
  imagemCortada: any = '';
  imagem: any;
  usuario: any;

  constructor(
    private storageService: StorageService,
    private modalController: ModalController,
    private router: Router
  ) {
    this.usuario = this.storageService.getUser();
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.imagem = state.imagem;
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCortada = event.base64;
  }

  imageLoaded(image: LoadedImage) {
    console.log('imageLoaded', image);
  }

  cropperReady() {
    console.log('cropperReady');
  }

  async loadImageFailed() {
    await this.abrirModalError();
  }

  async salvar() {
    this.storageService.setAvatarImage(this.imagemCortada, this.usuario.documento);
    this.usuario.imagemPerfil = this.imagemCortada;
    this.storageService.setUser(this.usuario);
    await this.abrirMeuPerfil();
  }

  abrirMeuPerfil() {
    return this.router.navigate(['/meu-perfil'], { state: { reload: true } });
  }

  async abrirModalError() {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Ajuste imagem',
        mensagem: 'Não foi possível abrir a imagem. Por favor, carregue apenas arquivos nos formatos PNG, GIF ou JPG.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.router.navigate(['/meu-perfil']);
    } else {
      await this.router.navigate(['/inicio']);
    }
  }
}
