import { Component } from '@angular/core';
import { ActionSheetController } from '@ionic/angular';
import { Router } from '@angular/router';
import { Camera, CameraDirection, CameraResultType, CameraSource } from '@capacitor/camera';
import {AuthService, Usuario} from '@corporativo/shared';

@Component({
  selector: 'app-meu-perfil',
  templateUrl: './meu-perfil.page.html',
  styleUrls: ['./meu-perfil.page.scss'],
  standalone: false
})
export class MeuPerfilPage {
  usuario: Usuario;
  imagemPerfil: any;

  constructor(
    private authService: AuthService,
    public router: Router,
    public actionSheetController: ActionSheetController,
  ) {
  }

  ionViewDidEnter() {
    this.usuario = this.authService.getUser();
  }

  async abrirOpcoes() {
    const actionSheet = await this.actionSheetController.create({
      header: 'Alterar Imagem',
      cssClass: 'custom-actionsheet',
      buttons: [
        {
          text: 'Tirar uma Foto',
          icon: 'camera',
          handler: () => {
            this.abrirCamera();
          }
        },
        {
          text: 'Escolher da Galeria',
          icon: 'image',
          handler: () => {
            this.escolherFoto();
          }
        },
        {
          text: 'Cancelar',
          icon: 'close',
          role: 'cancel'
        }]
    });
    await actionSheet.present();
  }

  async abrirCamera() {
    await this.tirarFoto();
  }

  async escolherFoto() {
    await this.abrirGaleria();
  }

  async tirarFoto() {
    const image = await Camera.getPhoto({
      quality: 70,
      allowEditing: false,
      resultType: CameraResultType.Base64,
      source: CameraSource.Camera,
      direction: CameraDirection.Front
    });
    const base64: any = `data:image/jpeg;base64,${image.base64String}`;
    await this.ajustar(base64);
  }

  async abrirGaleria() {
    const image = await Camera.getPhoto({
      quality: 90,
      allowEditing: false,
      resultType: CameraResultType.Base64,
      source: CameraSource.Photos
    });
    const base64: any = `data:image/jpeg;base64,${image.base64String}`;
    await this.ajustar(base64);
  }

  ajustar(imagemSelecionada: any) {
   return this.router.navigate(['/ajustar-imagem'], {state: {imagem: imagemSelecionada}});
  }
}
