import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController, Platform } from '@ionic/angular';
import {AuthService, Usuario} from '@corporativo/shared';
import {NotaFiscalService} from '../../../../../../../libs/shared/src/lib/services/nota-fiscal.service';
import {CameraService} from '../../../../../../../libs/shared/src/lib/services/camera.service';
import {loading} from '@utils/loading.util';
import {toast} from '@utils/toast.util';
import {base64toBlob, getExtension} from '@utils/file.util';
import {ModalErroComponent, ModalSucessoComponent} from '@corporativo/modals';

@Component({
  selector: 'app-anexar',
  templateUrl: './anexar.page.html',
  styleUrls: ['./anexar.page.scss'],
  standalone: false
})
export class AnexarPage implements OnInit {
  usuario: Usuario;
  idConta: number;
  maxSizeBytes = 2 * 1024 * 1024;
  arquivo: File;
  @ViewChild('fileInput') fileInput: ElementRef;
  operacao: any;

  constructor(
    private router: Router,
    private platform: Platform,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController,
    private cameraService: CameraService,
    private notaFiscalService: NotaFiscalService,
  ) {
    this.usuario = this.authService.getUser();
    const {operacao}: any = this.router.getCurrentNavigation()?.extras?.state;
    this.operacao = operacao;
    console.log('==> operacao', operacao);
  }

  ionViewDidEnter() {
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }

  ngOnInit() {
  }

  upload() {
    this.fileInput?.nativeElement.click();
  }

  async setFile(event: any) {
    const file: File = event.target.files[0];
    if (file.size > this.maxSizeBytes) {
      await toast('Tamanho do arquivo está fora do padrão. Escolha um arquivo até 2MB.');
      return;
    }
    if (!['png', 'jpg', 'jpeg', 'pdf'].includes(getExtension(file.name.toLowerCase()))) {
      await toast('Formato do arquivo está fora de padrão. Escolha um arquivo no formato: PDF, PNG, JPG ou JPEG.');
      return;
    }
    this.arquivo = file;
    event.value = null;
    await this.anexar(file, file.name);
  }


  async tirarFoto() {
    try {
      const image = await this.cameraService.takePicture();
      // const image:any = {base64String: cuponFiscalMock, format: '.png'}
      const blob = base64toBlob(image.base64String || '', image.format);
      await this.anexar(blob, 'image' + image.format);
    } catch (e: any) {
      console.log('==> e', e);
      await this.apresentarErro(e.message || 'Houve um erro ao tirar a foto.');
    }
  }

  async anexar(file: any, filename: string) {
    await loading(
      this.notaFiscalService.anexar(this.operacao, file, filename).subscribe({
        next: () => {
          this.apresentarSucesso();
        },
        error: (error: any) => {
          console.log('==> error', error);
          this.apresentarErro(error?.message || 'Houve um erro ao tirar a foto.');
        }
      })
    );
  }

  async apresentarSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Nota fiscal',
        tituloTexto: 'Anexo da nota fiscal',
        mensagem: 'O documento foi anexado com sucesso!',
        urlRetorno: '/inicio',
        classeImagem: 'sucesso-3',
        tituloBotaoSecundario: 'Minhas notas',
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      this.router.navigate([`/nota-fiscal/${this.idConta}`]);
    }
    return data;
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Nota fiscal',
        tituloTexto: 'Anexo da nota fiscal',
        mensagem: message,
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Ir para o início',
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      this.router.navigate([`/inicio`]);
    }
    return data;
  }
}
