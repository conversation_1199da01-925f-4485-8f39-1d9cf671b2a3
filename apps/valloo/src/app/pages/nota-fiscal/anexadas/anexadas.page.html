<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar>{{ situacoes[status] }}</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <section>
    <mobile-searchbar (changeInput)="filtrarNotasFiscais($event)" [clear]="false"></mobile-searchbar>
  </section>
  <section>
    <ion-segment [scrollable]="true" [value]="periodo" (ionChange)="alterarPeriodo($event)">
      <ion-segment-button value="15">
        15 dias
      </ion-segment-button>
      <ion-segment-button value="30">
        30 dias
      </ion-segment-button>
      <ion-segment-button value="45">
        45 dias
      </ion-segment-button>
      <ion-segment-button value="outro">
        Outro
      </ion-segment-button>
    </ion-segment>
  </section>
  <section>
    @if (notasFiscais$ | async; as notasPorDia) {
      @for (dia of notasPorDia; track dia.dataFmt) {
        <div class="dia-nota-fiscal">
          <div class="dia">
            <p>{{ dia.data | date:'dd/MM/yyyy' }}</p>
          </div>
        </div>
        @for (notaFiscal of dia.notasFiscais; track notaFiscal.id) {
          <ion-item lines="none" class="ion-margin-top nota-fiscal" [button]="true" [detail]="true"
                    (click)="abrir(notaFiscal)">
            <div class="tipo">
              <ion-icon name="reader-outline" color="secondary"></ion-icon>
            </div>
            <ion-label>
              <h3>{{ notaFiscal.descricaoTransacao || '-' }}</h3>
              <p>{{ enumSituacoes[notaFiscal.idSituacao] }}</p>
            </ion-label>
            <div slot="end" class="valor">
              <p>- {{ notaFiscal.valor | currency:'BRL' }}</p>
            </div>
          </ion-item>
        }
      } @empty {
        <div class="estado-vazio">
          <mobile-estado-vazio
            [message]="'Nenhuma nota fiscal encontrada.'"></mobile-estado-vazio>
        </div>
      }
    } @else {
      <div class="dia-nota-fiscal">
        <div class="dia">
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </div>
      </div>
      <ion-item lines="none" class="ion-margin-top nota-fiscal">
        <div class="tipo" slot="start"></div>
        <ion-label>
          <h3>
            <mobile-skeleton></mobile-skeleton>
          </h3>
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </ion-label>
        <p slot="end">
          <mobile-skeleton></mobile-skeleton>
        </p>
      </ion-item>
      <ion-item lines="none" class="nota-fiscal">
        <div class="tipo" slot="start"></div>
        <ion-label>
          <h3>
            <mobile-skeleton></mobile-skeleton>
          </h3>
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </ion-label>
        <p slot="end">
          <mobile-skeleton></mobile-skeleton>
        </p>
      </ion-item>
    }
  </section>
</ion-content>
