section {
  margin-top: 1.5rem;
}

ion-segment {
  --background: var(--ion-color-background);
  margin-top: 20px;

  ion-segment-button {
    --border-radius: 4px;
    --background: var(--ion-background-login);
    font-size: 12px;
    font-weight: 600;
    margin-right: 0.5rem;
    min-height: 22px;
    text-transform: none;
    --color: var(--ion-color-gray-50);
    --indicator-color: var(--ion-color-primary);
    --color-checked: var(--ion-color-primary-contrast)
  }

  ion-segment-button:last-child {
    margin-right: 0;
  }

  ion-segment-button::before {
    border: none;
  }
}

.dia-nota-fiscal {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;

  .dia {
    border-radius: 4px;
    background: var(--ion-color-primary);
    padding: 6px;

    p {
      color: var(--ion-color-secondary-contrast);
      font-size: 12px;
      font-weight: 600;
      margin: 0;
    }
  }

  ion-button {
    --border-radius: 100%;
    min-width: 32px;
  }
}

.nota-fiscal {
  --padding-start: 0;
  --padding-end: 0;
  --border-radius: 8px;
  --inner-padding-end: 0;

  .tipo {
    background-color: var(--ion-color-medium);
    border-radius: 100%;
    min-width: 32px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.5rem 0 0;
    position: relative;

    p {
      font-size: 14px;
      font-weight: 400;
      line-height: 119.5%;
      margin: 0;
    }
  }

  ion-label {
    white-space: normal;
    margin: 0;

    h3 {
      color: var(--ion-color-gray-50);
      font-size: 12px;
      font-weight: 600;
      margin: 0 0 0.25rem 0;
    }

    p {
      font-size: 10px;
      font-weight: 400;
      line-height: 119.5%;
      margin: 0;
    }
  }

  .valor {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    p {
      margin: 0 8px 0 0;
      font-size: 14px;
    }
  }
}

.estado-vazio {
  margin: 2.5rem 0;
}

ion-toolbar {
  --border-width: 0 !important;
}

ion-item {
  --background:  transparent;
}
