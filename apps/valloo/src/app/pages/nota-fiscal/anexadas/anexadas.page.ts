import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {format, parseISO} from 'date-fns';
import {NotaFiscalSituacaoEnum} from '../../../../../../../libs/shared/src/lib/enums/nota-fiscal-situacao.enum';
import {merge, Observable} from 'rxjs';
import {NotaFiscalService} from '../../../../../../../libs/shared/src/lib/services/nota-fiscal.service';
import {ModalController} from '@ionic/angular';
import {NotaFiscal} from '../../../../../../../libs/shared/src/lib/interfaces/nota-fiscal.interface';
import {ModalPeriodoDataComponent} from '@corporativo/modals';

@Component({
  selector: 'app-anexadas',
  templateUrl: './anexadas.page.html',
  styleUrls: ['./anexadas.page.scss'],
  standalone: false
})
export class AnexadasPage implements OnInit {
  idConta: number;
  status: string;
  situacoes: any = {
    aprovadas: 'Aprovadas',
    parcialmente: 'Parcialmente aprovadas',
    reprovadas: 'Reprovadas',
    pendentes: 'Pendentes de análise',
  };
  situacoesEnum: any = {
    aprovadas: NotaFiscalSituacaoEnum.Aprovada,
    parcialmente: NotaFiscalSituacaoEnum.AprovadaParcialmente,
    reprovadas: NotaFiscalSituacaoEnum.Reprovada,
    pendentes: NotaFiscalSituacaoEnum.Pendente,
  };
  enumSituacoes: any = {
    [NotaFiscalSituacaoEnum.Aprovada]: 'Aprovada',
    [NotaFiscalSituacaoEnum.Reprovada]: 'Reprovada',
    [NotaFiscalSituacaoEnum.AprovadaParcialmente]: 'Aprovado parcialmente',
    [NotaFiscalSituacaoEnum.Pendente]: 'Pendente',
  };
  idSituacao: number;
  limpar = false;
  periodo = '15';
  notasFiscais$!: Observable<any[]>;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController,
    private notaFiscalService: NotaFiscalService
  ) {
  }

  ionViewDidEnter() {
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    this.status = this.activatedRoute.snapshot.paramMap.get('status') || '';
    if (idConta) {
      this.idConta = +idConta;
    }
    this.idSituacao = +this.situacoesEnum[this.status];
    this.buscarPorPeriodo(this.periodo);
  }

  ngOnInit() {
  }

  filtrarNotasFiscais(valor: string) {
    this.notaFiscalService.filtrarNotasFiscais(valor);
  }

  async alterarPeriodo(customEvent: CustomEvent) {
    this.limparPesquisa();
    if (customEvent.detail.value != 'outro') {
      this.buscarPorPeriodo(customEvent.detail.value);
    } else {
      await this.selecionarPeriodo();
    }
  }

  limparPesquisa() {
    this.limpar = !this.limpar;
  }

  async selecionarPeriodo() {
    const modal = await this.modalController.create({
      component: ModalPeriodoDataComponent,
    });
    modal.present();

    const {data} = await modal.onWillDismiss();
    if (data) {
      const dataInicio = format(parseISO(data.dataInicio), 'yyyy-MM-dd');
      const dataFim = format(parseISO(data.dataFim), 'yyyy-MM-dd');
      this.buscarNotasFiscais(dataInicio, dataFim);
    }
  }

  buscarPorPeriodo(periodo: string) {
    const {dtInicio, dtFim} = this.converterPeriodo(periodo);
    this.buscarNotasFiscais(dtInicio, dtFim);
  }

  private converterPeriodo(periodo: string) {
    const dataInicio = new Date();
    const dataFim = new Date();
    dataInicio.setDate(dataFim.getDate() - parseInt(periodo, 10));
    const dtInicio = format(dataInicio, 'yyyy-MM-dd');
    const dtFim = format(dataFim, 'yyyy-MM-dd');
    return {dtInicio, dtFim};
  }

  buscarNotasFiscais(dtInicio: string, dtFim: string) {
    const idConta = this.idConta;
    this.notasFiscais$ = merge(
      this.notaFiscalService.buscarPor(idConta, this.idSituacao, dtInicio, dtFim),
      this.notaFiscalService.getNotasFiscaisFiltradas()
    );
  }

  abrir(notaFiscal: NotaFiscal) {
    return this.router.navigate([`/nota-fiscal/${this.idConta}/detalhe/${notaFiscal.id}`]);
  }
}
