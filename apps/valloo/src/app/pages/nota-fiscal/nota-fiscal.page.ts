import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-nota-fiscal',
  templateUrl: './nota-fiscal.page.html',
  styleUrls: ['./nota-fiscal.page.scss'],
  standalone: false
})
export class NotaFiscalPage implements OnInit {
  idConta: number;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
  ) {
  }

  ionViewDidEnter() {
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }

  ngOnInit() {
  }

  adicionar() {
    return this.router.navigate([`extrato/${this.idConta}`]);
  }

}
