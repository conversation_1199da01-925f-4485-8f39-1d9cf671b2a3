import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {DetalhePageRoutingModule} from './detalhe-routing.module';
import {DetalhePage} from './detalhe.page';
import {SkeletonModule, TitleToolbarModule} from '@corporativo/components';
import {SafePipe} from '../../../../../../../libs/shared/src/lib/pipes/safe.pipe';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    DetalhePageRoutingModule,
    TitleToolbarModule,
    SkeletonModule,
    SafePipe
  ],
  declarations: [DetalhePage]
})
export class DetalhePageModule {}
