import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {ModalController, Platform} from '@ionic/angular';
import {AuthService} from '@corporativo/shared';
import {NotaFiscalService} from '../../../../../../../libs/shared/src/lib/services/nota-fiscal.service';
import {NotaFiscalSituacaoEnum} from '../../../../../../../libs/shared/src/lib/enums/nota-fiscal-situacao.enum';
import {getExtension} from '@utils/file.util';
import {NotaFiscal} from '../../../../../../../libs/shared/src/lib/interfaces/nota-fiscal.interface';
import {ModalAtencaoComponent} from '@corporativo/modals';

@Component({
  selector: 'app-detalhe',
  templateUrl: './detalhe.page.html',
  styleUrls: ['./detalhe.page.scss'],
  standalone: false,
})
export class DetalhePage implements OnInit {
  idConta: number;
  notaFiscal: NotaFiscal;
  id: number;
  isPdf = false;
  APROVADA = NotaFiscalSituacaoEnum.Aprovada;
  REPROVADA = NotaFiscalSituacaoEnum.Reprovada;
  PENDENTE = NotaFiscalSituacaoEnum.Pendente;
  APROVADA_PARCIALMENTE = NotaFiscalSituacaoEnum.AprovadaParcialmente;
  situacoes: any = {
    [NotaFiscalSituacaoEnum.Aprovada]: 'Aprovada',
    [NotaFiscalSituacaoEnum.Reprovada]: 'Reprovada',
    [NotaFiscalSituacaoEnum.AprovadaParcialmente]: 'Aprovado parcialmente',
    [NotaFiscalSituacaoEnum.Pendente]: 'Pendente',
  };
  // situacoes[NotaFiscalSituacaoEnum.Pendente] = 'Pendente';
  // situacoes[NotaFiscalSituacaoEnum.Aprovada] = 'Aprovada';
  // situacoes[NotaFiscalSituacaoEnum.Reprovada] = 'Reprovada';
  // situacoes[NotaFiscalSituacaoEnum.AprovadaParcialmente] = 'Aprovado parcialmente';


  constructor(
    private router: Router,
    private platform: Platform,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController,
    private notaFiscalService: NotaFiscalService,
  ) {
  }

  ionViewDidEnter() {
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    const id = this.activatedRoute.snapshot.paramMap.get('id');
    if (idConta) {
      this.idConta = +idConta;
    }
    if (id) {
      this.id = +id;
    }
    this.getNotaFiscal();
  }

  ngOnInit() {
  }

  getNotaFiscal() {
    this.notaFiscalService.getById(this.id).subscribe((x: any) => {
      this.notaFiscal = x;
      this.isPdf = getExtension(this.notaFiscal.caminhoImagem) === 'pdf';
    });
  }

  async excluirReenviar() {
    const operacao: any = {
      idConta: this.idConta,
      idTranlog: this.notaFiscal.idTranlog,
      valor: this.notaFiscal.valor,
    };
    await this.router.navigate([`nota-fiscal/${this.idConta}/anexar`], {state: {operacao}});
  }

  async confirmarExcluirReenviar() {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        texto: 'Deseja reenviar a nota fiscal?',
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.excluirReenviar();
    }
    return data;
  }
}
