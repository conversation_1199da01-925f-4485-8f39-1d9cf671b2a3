<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar>Nota fiscal</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  @if (notaFiscal) {
    <section>
      <ion-grid>
        <ion-row>
          <ion-col>
            <p>Data da transação</p>
            <p><b>{{ notaFiscal.dataTransacao ? (notaFiscal.dataTransacao | date:'dd/MM/yyyy') : '-' }}</b></p>
          </ion-col>
          <ion-col>
            <p>Descrição</p>
            <p><b>{{ notaFiscal.descricaoTransacao || '-'}}</b></p>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <p>Data do anexo</p>
            <p><b>{{ notaFiscal.dataHoraInclusao | date:'dd/MM/yyyy' }}</b></p>
          </ion-col>
          <ion-col>
            <p>Situação</p>
            <p><b>{{ situacoes[notaFiscal.idSituacao] }}</b></p>
          </ion-col>
        </ion-row>
        @if (notaFiscal.idSituacao != PENDENTE) {
          <ion-row>
            <ion-col>
              <p>Valor</p>
              <p><b> {{ notaFiscal.valor | currency: 'BRL' }} </b></p>
            </ion-col>
            <ion-col>
              @if (notaFiscal.idSituacao == REPROVADA) {
                <p>Data da reprovação</p>
              } @else {
                <p>Data da aprovação</p>
              }
              <p><b>{{ notaFiscal.dataHoraManutencao | date:'dd/MM/yyyy' }}</b></p>
            </ion-col>
          </ion-row>
          @if (notaFiscal.idSituacao == APROVADA_PARCIALMENTE) {
            <ion-row>
              <ion-col>
                <p>Valor reprovado</p>
                <p><b> {{ notaFiscal.valorReprovado | currency: 'BRL' }} </b></p>
              </ion-col>
              <ion-col></ion-col>
            </ion-row>
          }
          @if (notaFiscal.idSituacao == REPROVADA) {
            <ion-row>
              <ion-col>
                <p>Motivo</p>
                <p><b>{{ notaFiscal.motivo }}</b></p>
              </ion-col>
            </ion-row>
          }
        }
      </ion-grid>
    </section>
    <section>
      @if (isPdf) {
        <iframe
          [src]="notaFiscal.linkImagem | safe"
          width="100%"
          height="450px">
        </iframe>
      } @else {
        <ion-img [src]="notaFiscal.linkImagem" alt="Imagem da nota fiscal"></ion-img>
      }
    </section>
  } @else {
    <section>
      <ion-grid>
        <ion-row>
          <ion-col>
            <p>
              <mobile-skeleton height="18px" width="100px"></mobile-skeleton>
            </p>
            <p>
              <mobile-skeleton height="18px" width="100px"></mobile-skeleton>
            </p>
          </ion-col>
          <ion-col>
            <p>
              <mobile-skeleton height="18px" width="100px"></mobile-skeleton>
            </p>
            <p>
              <mobile-skeleton height="18px" width="100px"></mobile-skeleton>
            </p>
          </ion-col>
          <ion-col>
            <p>
              <mobile-skeleton height="18px" width="100px"></mobile-skeleton>
            </p>
            <p>
              <mobile-skeleton height="18px" width="100px"></mobile-skeleton>
            </p>
          </ion-col>
        </ion-row>
      </ion-grid>
    </section>
    <section>
      <mobile-skeleton height="400px" width="100%"></mobile-skeleton>
    </section>
  }
</ion-content>
<ion-footer class="ion-no-border">
  @if (notaFiscal && notaFiscal.idSituacao == REPROVADA) {
    <ion-button expand="block" class="ion-margin-bottom" (click)="confirmarExcluirReenviar()">
      Reenviar
    </ion-button>
  }
  <ion-button expand="block" fill="outline" [routerLink]="'/nota-fiscal/' + idConta">Voltar ao início</ion-button>
</ion-footer>
