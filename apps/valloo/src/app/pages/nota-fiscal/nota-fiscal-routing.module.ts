import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { NotaFiscalPage } from './nota-fiscal.page';

const routes: Routes = [
  {
    path: '',
    component: NotaFiscalPage
  },
  {
    path: 'anexar',
    loadChildren: () => import('./anexar/anexar.module').then( m => m.AnexarPageModule)
  },
  {
    path: 'anexadas/:status',
    loadChildren: () => import('./anexadas/anexadas.module').then( m => m.AnexadasPageModule)
  },
  {
    path: 'detalhe/:id',
    loadChildren: () => import('./detalhe/detalhe.module').then( m => m.DetalhePageModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class NotaFiscalPageRoutingModule {}
