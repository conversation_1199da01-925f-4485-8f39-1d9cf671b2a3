import { Component } from '@angular/core';
import { ModalController } from "@ionic/angular";
import { Router } from "@angular/router";
import {MetodoSegurancaEnum, StorageService, TipoObjetivoEnum} from '@corporativo/shared';
import {ModalSegurancaComponent} from '@corporativo/modals';

@Component({
  selector: 'app-alterar-senha',
  templateUrl: './alterar-senha.page.html',
  styleUrls: ['./alterar-senha.page.scss'],
  standalone: false
})
export class AlterarSenhaPage {

  cafNecessario: boolean;
  usuarioSelecionado: any;
  credencialBuscada: any;

  constructor(
    public router: Router,
    private storageService: StorageService,
    private modalController: ModalController
  ) {
    this.usuarioSelecionado = this.storageService.getUser();
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.cafNecessario = state.cafNecessario;
    this.credencialBuscada = state.credencialBuscada;
  }

  async alterarSenha() {
    console.log('==> this.cafNecessario: ', this.cafNecessario);
    if (this.cafNecessario) {
      const fluxoDados: any = {};
      fluxoDados.documento = this.usuarioSelecionado.documento;
      fluxoDados.documentoRepresentante = this.usuarioSelecionado.documentoRepresentante == null ? null : this.usuarioSelecionado.documentoRepresentante;
      fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaCartao;
      fluxoDados.tipo = 'troca-senha-cartao';
      await this.router.navigate(['/selfie'], {state: {fluxoDados: fluxoDados, dados: this.credencialBuscada}});
    } else {
      const valido = await this.verificarSeguranca(this.credencialBuscada);
      if (!valido) {
        return;
      }
      this.alterarSenhaCartao();
    }
  }

  async verificarSeguranca(usuario: any) {
    if (usuario.metodoSegurancaTransacao == MetodoSegurancaEnum.NaoVerificar) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: 2,
        idCredencial: usuario.idCredencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    return data;
  }

  alterarSenhaCartao() {
    const fluxoDados: any = {};
    fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaCartao;
    fluxoDados.documento = this.usuarioSelecionado.documento;
    fluxoDados.documentoRepresentante = this.usuarioSelecionado.documentoRepresentante;
    fluxoDados.tipo = 'troca-senha-cartao';
    this.router.navigate(['/senha-cartao'], {state: {fluxoDados: fluxoDados, dados: this.credencialBuscada}});
  }

  alterarDepois() {
    this.router.navigate(['/cartao-fisico']);
  }

}
