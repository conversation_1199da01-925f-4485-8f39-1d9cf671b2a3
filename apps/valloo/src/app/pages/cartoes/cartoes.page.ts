import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, tap } from 'rxjs';
import { ModalController } from '@ionic/angular';
import {
  AuthService,
  Credencial,
  CredencialService, FuncionalidadeCartaoEnum,
  FuncionalidadeService,
  ProdutoService,
  Usuario
} from '@corporativo/shared';

@Component({
  selector: 'mobile-cartoes',
  templateUrl: './cartoes.page.html',
  styleUrls: ['./cartoes.page.scss'],
  standalone: false
})
export class CartoesPage {
  credencialVirtual$: Observable<Credencial[]>;
  usuario!: Usuario;
  credencialSelecionada: Credencial | undefined;
  cartaoVirtual: any;
  virtual = false;
  idCredencial!: number;
  idCredencialVirtual!: number;
  idConta!: number;
  habilitarCartao = false;
  mostrarFuncionalidadeCartaoFisico: boolean = false;
  mostrarFuncionalidadeCartaoVirtual: boolean = false;
  funcionalidades: any = [];
  loading = true;

  constructor(
    private authService: AuthService,
    public router: Router,
    private modalController: ModalController,
    private credencialService: CredencialService,
    private activatedRoute: ActivatedRoute,
    private funcionalidadeService: FuncionalidadeService,
    private produtoService: ProdutoService
  ) {
  }

  ionViewDidEnter() {
    this.usuario = this.authService.getUser();
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      for (const credencial of this.usuario.credenciais) {
        const conta = credencial.contas.find((c: any) => c.idConta == this.idConta);
        if (!conta) {
          continue;
        }
        if (credencial.virtual) {
          this.idCredencialVirtual = credencial.idCredencial;
        } else {
          this.idCredencial = credencial.idCredencial;
        }
      }
    }
    const credencial: any = this.usuario.credenciais.find((x: Credencial) => {
      const conta = x.contas.find((c: any) => c.idConta == this.idConta);
      return conta ? x : null;
    });

    this.habilitarCartao = credencial.permiteCartaoFisico || credencial.permiteCartaoFisico == null;
    this.buscarFuncionalidadesCartoes();
    this.buscarCredencialFisica(this.idCredencial);
    this.buscarCredencialVirtual(this.idCredencialVirtual);
  }

  buscarCredencialFisica(idCredencial: number) {
    this.credencialSelecionada = this.usuario.credenciais.find((x: any) => x.idCredencial == idCredencial && !x.virtual);
  }

  buscarCredencialVirtual(idCredencialVirtual: number) {
    const credencialSelecionada: any = this.usuario.credenciais.find((x: any) => x.idCredencial == idCredencialVirtual);
    const idConta = idCredencialVirtual == null ? this.credencialSelecionada?.idConta : credencialSelecionada.idConta;
    this.credencialVirtual$ = this.credencialService.buscarCredencialVirtual(idConta)
      .pipe(tap((cartoes: any[]) => {
        this.cartaoVirtual = cartoes.length ? cartoes[0] : null;
        this.virtual = cartoes.length ? cartoes[0].virtual : null;
      }));
  }

  irParaRota() {
    return this.router.navigate(['/cartao-fisico'], {
      state: {
        credencial: this.credencialSelecionada,
        credencialVirtual: this.virtual,
        funcionalidades: this.funcionalidades
      }
    });
  }

  irVirtual() {
    return this.router.navigate(['/cartao-virtual'], {
      state: {
        credencialVirtual: this.virtual,
        cartaoSelecionado: this.cartaoVirtual,
        existeCartaoFisico: this.credencialSelecionada != null,
        cartaoFisico: this.credencialSelecionada,
        funcionalidades: this.funcionalidades
      }
    });
  }

  solicitarPrimeiraVia() {
    return this.router.navigate(['/segunda-via'], {
      state: {
        credencial: this.cartaoVirtual,
        primeiraVia: true
      }
    });
  }

  buscarFuncionalidadesCartoes() {
    const funcionalidadesCartao = [
      { nome: 'cartao-fisico', ativo: false, indice: FuncionalidadeCartaoEnum.CartaoFisico },
      { nome: 'cartao-virtual', ativo: false, indice: FuncionalidadeCartaoEnum.CartaoVirtualRecorrente }
    ];
    const funcionalidades = [
      ...funcionalidadesCartao,
      ...this.produtoService.getFuncionalidadesCartaoFisico(),
      ...this.produtoService.getFuncionalidadesCartaoVirtual(),
    ];
    this.funcionalidadeService.buscarFuncionalidesCartao(funcionalidades).subscribe({
      next: (funcionalidades: any) => {
        this.mostrarFuncionalidadeCartaoVirtual = funcionalidades.find((x: any) => x.nome == 'cartao-virtual').ativo;
        this.mostrarFuncionalidadeCartaoFisico = funcionalidades.find((x: any) => x.nome == 'cartao-fisico').ativo;
        this.funcionalidades = funcionalidades.filter((x: any) => x.nome != 'cartao-fisico' && x.nome != 'cartao-virtual');
        this.loading = false;
      }
    });
  }

}
