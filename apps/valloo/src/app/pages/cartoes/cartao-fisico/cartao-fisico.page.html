<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar>Cartão</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content class="content-default" [fullscreen]="true">
  <component-plastico-cartao [credencial]="credencial"></component-plastico-cartao>
  <article>
    <section class="botao">
      <ng-container *ngIf="credencialSelecionado | async as cred; else carregando">
        <ng-container *ngIf="cred.status === 1; else estadoVazio">
          <section class="dados-credencial">
            <ion-text>
              <ion-text class="nome">Nome do titular:</ion-text>
              {{ cred.nomeImpresso | titlecase }}
            </ion-text>
            <ion-text>
              <ion-text class="nome">Número do cartão:</ion-text>
              {{ cred.credencialMascarada | titlecase }}
            </ion-text>
          </section>
          <article class="funcionalidades buttons-grid cartao-fisico">
            <ng-container *ngIf="produtos$ | async as produtos; else carregando">
              <ng-container *ngFor="let produto of produtos">
                <mobile-button class="rounded" *ngIf="produto.ativo" [icon]="produto.icone"
                                       (click)="irParaRota(produto)"
                                       [nome]="produto.nome" [color]="produto.color"></mobile-button>
              </ng-container>
            </ng-container>
            <ng-template #carregando>
              <ng-container *ngFor="let i of [1, 2, 3]">
                <mobile-button class="rounded" [carregando]="true"></mobile-button>
              </ng-container>
            </ng-template>
          </article>
        </ng-container>
        <ng-template #estadoVazio>
          <section class="desbloquear-cartao">
            <article appDynamicClass class="carregar" *ngIf="cred.status === 0">
              <mobile-button class="rounded" [nome]="'Desbloquear cartão'" [icon]="'desbloqueio'"
                             (click)="desbloquear()" [color]="'secondary'"></mobile-button>
            </article>
          </section>
          <ng-container *ngIf="cred.status === 5">
            <section class="dados-credencial">
              <ion-text>
                <ion-text class="nome">Nome do titular:</ion-text>
                {{ cred.nomeImpresso | titlecase }}
              </ion-text>
              <ion-text>
                <ion-text class="nome">Número do cartão:</ion-text>
                {{ cred.credencialMascarada | titlecase }}
              </ion-text>
            </section>
            <section class="desbloquear-cartao">
              <article class="desbloqueio">
                <mobile-button [nome]="'Desbloquear cartão'" [icon]="'desbloqueio'"
                               (click)="desbloquearTemporario()" [color]="'secondary'"></mobile-button>
              </article>
            </section>
          </ng-container>
        </ng-template>
      </ng-container>
      <ng-template #carregando>
        <ng-container>
          <section>
            <mobile-skeleton class="carregar-dados" width="100%" height="60px"></mobile-skeleton>
          </section>
          <article class="funcionalidades buttons-grid cartao-fisico">
            <ng-container>
              <ng-container *ngFor="let i of [1, 2, 3]">
                <mobile-button class="rounded" [carregando]="true"></mobile-button>
              </ng-container>
            </ng-container>
          </article>
        </ng-container>
      </ng-template>
    </section>
  </article>
</ion-content>
