
.carregar-skeleton {
  display: flex;
  justify-content: center;
}

.dados-credencial {
  display: flex;
  flex-direction: column;
  margin-top: 60px;
  border-radius: 16px;
  padding: 15px;
  background: var(--ion-color-medium);
  font-size: 14px;
}

.funcionalidades {
  margin-top: 30px;

  component-button-rounded {
    margin-top: 1rem;
    margin-right: 6px;
  }
}

.rounded {
  flex: 1 1 calc(33.333% - 20px);
  box-sizing: border-box;
  padding: 10px;
  text-align: center;
}

.carregar {
  margin-top: 140px;
}

.desbloqueio {
  margin-top: 80px;
}

.carregar-dados {
  display: flex;
  flex-direction: column;
  margin-top: 60px;
  border-radius: 16px;
  padding: 15px;
}
