import {Component} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {delay, Observable, of} from 'rxjs';
import {ModalController} from '@ionic/angular';
import {loading} from '@utils/loading.util';
import {
  AuthService,
  CredencialService,
  MetodoSegurancaEnum,
  OnboardingService, ProdutoService,
  TipoObjetivoEnum,
  Usuario
} from '@corporativo/shared';
import {
  ModalAtencaoComponent,
  ModalErroComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent
} from '@corporativo/modals';

@Component({
  selector: 'mobile-cartao-fisisco',
  templateUrl: './cartao-fisico.page.html',
  styleUrls: ['./cartao-fisico.page.scss'],
  standalone: false
})
export class CartaoFisicoPage {
  credencialSelecionado: Observable<any>;
  produtos$: Observable<any>;
  credencial: any;
  credencialVirtual: boolean;
  cafNecessario: boolean;
  usuario!: Usuario;
  funcionalidades: any = [];
  produtosCartao: any[];

  constructor(
    public router: Router,
    private modalController: ModalController,
    private credencialService: CredencialService,
    public activatedRoute: ActivatedRoute,
    private onboardingService: OnboardingService,
    private authService: AuthService,
    private produtoService: ProdutoService
  ) {
    this.usuario = this.authService.getUser();
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.credencial = state.credencial;
    this.credencialVirtual = state.credencialVirtual;
    this.funcionalidades = state.funcionalidades;
    this.produtosCartao = this.produtoService.getFuncionalidadesCartaoFisico();
    this.activatedRoute.params.subscribe((val) => {
      if (this.router.getCurrentNavigation()?.extras.state) {
        const reload: any = this.router.getCurrentNavigation()?.extras.state;
        if (reload) {
          this.buscarStatusCredencial();
        }
      }
    });
    this.produtosCartao = this.funcionalidades.filter((x: any) => !x.virtual);
  }

  ngOnInit() {
    this.buscarStatusCredencial();
    this.buscarFuncionalidades();
  }

  buscarFuncionalidades() {
    this.produtos$ = of(this.produtosCartao).pipe(delay(200));
    this.buscarCafNecessarioPorConta();
  }

  buscarStatusCredencial() {
    this.credencialSelecionado = of(this.credencial).pipe(delay(200));
  }

  buscarCafNecessarioPorConta() {
    this.onboardingService.encontraCafNecessarioPorConta(this.credencial.idConta).subscribe({
      next: (value: any) => {
        console.log('==> value: ', value);
        this.cafNecessario = value;
      }
    });
  }

  desbloquear() {
    this.abrirModalAtencao();
  }

  desbloquearTemporario() {
    this.abrirModalTemporario();
  }

  async abrirModalAtencao() {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Desbloqueio de cartão ',
        mensagem: 'Para realizar o desbloqueio, tenha o cartão físico em mãos. As informações necessárias para o desbloqueio estão localizadas no verso do cartão.',
        tituloBotaoPrimario: 'Desbloquear o cartão',
        tituloBotaoSecundario: 'Desbloquear depois',
        nomeIcon: 'desbloqueio',
        colorIcon: 'primary'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.router.navigate(['/desbloqueio-fisico'], {
        state: {
          credencial: this.credencial,
          credencialVirtual: this.credencialVirtual
        }
      });
    }
  }

  irParaRota(produto: any) {
    if (produto.codigo == 'senha') {
      return this.router.navigate(['alterar-senha'], {
        state: {
          cafNecessario: this.cafNecessario,
          credencialBuscada: this.credencial
        }
      });
    }
    if (produto.codigo == 'bloquear') {
      return this.router.navigate(['bloquear-cartao'], { state: { credencial: this.credencial } });
    }
    if (produto.codigo == 'segunda-via') {
      return this.router.navigate(['segunda-via'], { state: { credencial: this.credencial } });
    }
    if (produto.codigo == 'onde-aceita') {
      return this.router.navigate(['onde-aceita'], { state: { credencial: this.credencial } });
    }
  }

  async abrirModalError(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: message ? 'Ocorreu um erro' : 'Erro inesperado',
        mensagem: message ? message : 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.router.navigate(['/inicio']);
    }
  }

  async abrirModalTemporario() {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Desbloquear cartão',
        mensagem: 'Deseja realmente desbloquear o cartão?',
        tituloBotaoPrimario: 'Desbloquear o cartão',
        tituloBotaoSecundario: 'Desbloquear depois',
        nomeIcon: 'desbloqueio',
        colorIcon: 'primary'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      if (this.cafNecessario) {
        const fluxoDados: any = {};
        fluxoDados.tipo = 'desbloqueio-temporario';
        fluxoDados.tipoObjetivo = TipoObjetivoEnum.DesbloquearCartaoVirtual;
        fluxoDados.documento = this.usuario.documento;
        fluxoDados.documentoRepresentante = this.usuario.documentoRepresentante == null ? null : this.usuario.documentoRepresentante;
        fluxoDados.credencialFisica = this.credencial.status != 0;
        await this.router.navigate(['/selfie'], {
          state: {
            credencial: this.credencial,
            fluxoDados: fluxoDados,
            credencialVirtual: this.credencialVirtual
          }
        });
      } else {
        const valido = await this.verificarSeguranca(this.credencial);
        if (!valido) {
          return;
        }
        this.desbloquearCredencialTemporario();
      }
    }
  }

  async verificarSeguranca(usuario: any) {
    if (usuario.metodoSegurancaTransacao == MetodoSegurancaEnum.NaoVerificar) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: 2,
        idCredencial: usuario.idCredencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  desbloquearCredencialTemporario() {
    const response$ = this.credencialService.desbloquearCredencialTemporario(this.credencial.idCredencial);
    loading(response$.subscribe({
      next: () => {
        this.abrirModalSucessoDesbloqueio();
      }
    }));
  }

  async abrirModalSucessoDesbloqueio() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Cartão Desbloqueado',
        mensagem: 'O seu cartão foi desbloqueado!'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }
}
