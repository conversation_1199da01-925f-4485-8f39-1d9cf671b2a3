import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {CartaoFisicoPage} from './cartao-fisico.page';
import {CartaoFisicoRoutingModule} from './cartao-fisico-routing.module';
import {ButtonModule, PlasticoCartaoModule, SkeletonModule, TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    CartaoFisicoRoutingModule,
    PlasticoCartaoModule,
    ButtonModule,
    SkeletonModule,
    TitleToolbarModule,
  ],
  declarations: [CartaoFisicoPage]
})
export class CartaoFisicoModule {
}
