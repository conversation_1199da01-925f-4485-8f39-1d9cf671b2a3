import { Component } from '@angular/core';
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { concatMap } from "rxjs";
import { ModalController } from "@ionic/angular";
import {AuthService, CredencialService, OnboardingService, TipoObjetivoEnum, Usuario} from '@corporativo/shared';
import {Router} from '@angular/router';
import {loading} from '@utils/loading.util';
import {toast} from '@utils/toast.util';
import {ModalErroComponent, ModalSucessoComponent} from '@corporativo/modals';

@Component({
  selector: 'mobile-senha-cartao',
  templateUrl: './senha-cartao.page.html',
  styleUrls: ['./senha-cartao.page.scss'],
  standalone: false
})
export class SenhaCartaoPage {

  fluxoDados: any;
  dados: any;
  credencial: any;
  usuario!: Usuario;
  jwt: string;
  verSenha = false;
  verSenhaConfirmacao = false;
  form = new FormGroup(
    {
      senha: new FormControl('', [Validators.required]),
      novaSenha: new FormControl('', [Validators.required])
    }
  );

  constructor(
    public router: Router,
    private credencialService: CredencialService,
    private modalController: ModalController,
    private onboardingService: OnboardingService,
    private authService: AuthService
  ) {
    this.usuario = this.authService.getUser();
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
    this.dados = state.dados;
    this.credencial = state.credencial;
    this.jwt = state.jwt;
    if (this.fluxoDados.fecharModal) {
      this.modalController.dismiss();
    }
  }

  alterar() {
    if (this.fluxoDados.tipoObjetivo == TipoObjetivoEnum.TrocaSenhaCartao) {
      return this.alterarSenha(this.dados.idCredencial);
    }
    if (this.fluxoDados.tipoObjetivo == TipoObjetivoEnum.DesbloquearCartaoVirtual) {
      return this.validarEtapasCredencialVirtual(this.credencial);
    }
    return this.validarEtapasCredencial(this.credencial.idCredencial);
  }

  async validarEtapasCredencial(idCredencial: number) {
    const senha = this.form.value.novaSenha || 0;
    const response$ = this.credencialService.validarEtapasCredencial(idCredencial, senha, this.authService.getToken() || '');
    loading(response$.pipe(
        concatMap((dados: any) => {
          if (dados.confirmacao) {
            return this.credencialService.desbloquearCredencial(this.credencial, this.dados);
          } else {
            return toast('Aconteceu algum erro ao tentar cadastrar sua senha');
          }
        })).subscribe({
        next: (data: any) => {
          if (!this.fluxoDados.cafNecessario) {
            this.credencialService.salvarSenha(idCredencial, senha, this.authService.getToken() || '').subscribe({
              next: () => {
                this.buscarCredenciais();
                return this.abrirModalSucesso();
              }, error: (error: any) => {
                const message = error.error.msg ? error.error.msg : error.error.message;
                this.abrirModalError(message);
              }
            });
          } else {
            this.onboardingService.registraValidacaoFacial(this.usuario.documento, TipoObjetivoEnum.TrocaSenhaCartao, this.jwt).subscribe({
              next: () => {
                return this.alterarSenha(idCredencial)
              }, error: (error: any) => {
                const message = error.error.msg ? error.error.msg : error.error.message;
                this.abrirModalError(message);
              }
            });
          }

        }, error: (err: any) => {
          const message = err.error.msg ? err.error.msg : err.error.message;
          this.abrirModalError(message);
        }
      })
    )
  }

  async validarEtapasCredencialVirtual(credencial: any) {
    const senha = this.form.value.novaSenha || 0;
    const response$ = this.credencialService.validarEtapasCredencial(credencial.idCredencial, senha, this.authService.getToken() || '');
    await loading(response$.pipe(concatMap((dados: any) => {
      if (dados.confirmacao) {
        return this.credencialService.requisitarCartaoVirtual(credencial);
      } else {
        return toast('Aconteceu algum erro ao tentar cadastrar sua senha');
      }
    })).subscribe({
      next: () => {
        this.onboardingService.registraValidacaoFacial(this.usuario.documento, TipoObjetivoEnum.TrocaSenhaCartao, this.jwt).subscribe({
          next: () => {
            return this.alterarSenha(credencial.idCredencial);
          }, error: (error: any) => {
            const message = error.error.msg ? error.error.msg : error.error.message;
            this.abrirModalError(message);
          }
        });
      }, error: (error: any) => {
        const message = error.error.msg ? error.error.msg : error.error.message;
        return this.abrirModalError(message);
      }
    }))
  }

  async abrirModalSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: this.fluxoDados.tipoObjetivo == TipoObjetivoEnum.TrocaSenhaCartao ? 'Parabéns' : 'Cartão Desbloqueado',
        mensagem: this.fluxoDados.tipoObjetivo == TipoObjetivoEnum.TrocaSenhaCartao ? 'A senha definida foi validada e esta será a mesma para seu cartão físico ou virtual.' : 'O seu cartão foi desbloqueado!'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  buscarCredenciais() {
    this.authService.buscarDados().subscribe();
  }

  async abrirModalError(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: message ? 'Ocorreu um erro' : 'Erro inesperado',
        mensagem: message ? message : 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.router.navigate(['/inicio']);
    }
  }

  async abrirModalSucessoCartaoRecorrente() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Cartão virtual',
        mensagem: 'Cartão gerado com sucesso!'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  async alterarSenha(idCredencial: number) {
    const senha = this.form.value.novaSenha || 0;
    const response$ = this.credencialService.salvarSenha(idCredencial, senha, this.authService.getToken() || '');
    await loading(response$.subscribe({
      next: () => {
        if (this.fluxoDados.tipoObjetivo == TipoObjetivoEnum.DesbloquearCartaoVirtual) {
          return this.abrirModalSucessoCartaoRecorrente();
        }
        return this.abrirModalSucesso();
      }, error: (error: any) => {
        let mensagem = null;
        const regex = /-\s(.+)$/;
        const resultado = error.error.msg.match(regex);
        if (resultado != null && resultado[1]) {
          mensagem = resultado[1];
        }
        const message = mensagem != null ? mensagem : error.error.msg ? error.error.msg : error.error.message;
        this.abrirModalError(message);
      }
    }));
  }
}
