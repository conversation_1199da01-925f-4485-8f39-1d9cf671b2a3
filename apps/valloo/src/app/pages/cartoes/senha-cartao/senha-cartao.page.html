<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [mostrarVoltar]="false"><PERSON><PERSON> cartão</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content class="content-default" [fullscreen]="true">

  <article>
    <form class="senha" [formGroup]="form">
      <ion-item lines="none">
        <ion-input type="tel" labelPlacement="stacked" formControlName="senha" maxlength="4" inputmode="numeric"
                   errorText="Digite sua nova senha" placeholder="●●●●" [type]="verSenha ? 'text' : 'password'">
          <div slot="label" class="custom-label">Digite a senha do cartão</div>
        </ion-input>
        <ion-icon size="small" slot="end" [name]="verSenha ? 'eye-outline' : 'eye-off-outline'"
                  (click)="verSenha = !verSenha"></ion-icon>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input type="tel" labelPlacement="stacked" formControlName="novaSenha" maxlength="4" inputmode="numeric"
                   errorText="Confirme sua nova senha" placeholder="●●●●" [type]="verSenhaConfirmacao ? 'text' : 'password'">
          <div slot="label" class="custom-label">Confirme a senha do cartão</div>
        </ion-input>
        <ion-icon size="small" slot="end" [name]="verSenhaConfirmacao ? 'eye-outline' : 'eye-off-outline'"
                  (click)="verSenhaConfirmacao = !verSenhaConfirmacao"></ion-icon>
      </ion-item>
    </form>
  </article>

  <article class="validacao">
    <ion-row>
      <ion-col size="1">
        <ion-icon name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Sua senha deve conter 4 dígitos (numéricos)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Não utilize números repetidos (6699)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Não utilize sequencias (1234)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Confirme se os dados estão preenchidos</ion-text>
      </ion-col>
    </ion-row>
  </article>
</ion-content>
<ion-footer>
  <ion-button class="btn ion-margin-top" expand="block" (click)="alterar()">Confirmar senha</ion-button>
</ion-footer>
