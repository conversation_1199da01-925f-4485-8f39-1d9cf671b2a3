<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar>Cartões</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content class="content-default" [fullscreen]="true">
  @if (habilitarCartao && mostrarFuncionalidadeCartaoFisico) {
    <mobile-titulo-secao>Cartão físico</mobile-titulo-secao>
    <section class="cartoes">
      @if (credencialSelecionada) {
        <ion-item class="informacao-cartao" lines="none" button [detail]="true" (click)="irParaRota()">
          <ion-icon slot="start" class="icon-cartao" name="cartoes"></ion-icon>
          <ion-label>
            <h2>
              {{ credencialSelecionada.nomeImpresso | titlecase }}
            </h2>
            <p>
              {{ credencialSelecionada.credencialMascarada }}
            </p>
          </ion-label>
        </ion-item>
      } @else {
        <ion-item lines="none" button [detail]="true" class="bloqueado" (click)="solicitarPrimeiraVia()">
          <ion-icon slot="start" class="icon-cartao" name="cartoes"></ion-icon>
          <ion-label>
            <h2>
              Não possui cartão físico
            </h2>
            <p>
              Clique para solicitar
            </p>
          </ion-label>
        </ion-item>
      }
    </section>
  }
  @if (loading) {
    <mobile-titulo-secao>
      <mobile-skeleton></mobile-skeleton>
    </mobile-titulo-secao>
    <section class="cartoes">
      <ion-item class="informacao-cartao" lines="none" button [detail]="true">
        <ion-avatar slot="start" class="icon-cartao">
          <ion-skeleton-text></ion-skeleton-text>
        </ion-avatar>
        <ion-label>
          <h2>
            <mobile-skeleton></mobile-skeleton>
          </h2>
          <p>
            <mobile-skeleton></mobile-skeleton>
          </p>
        </ion-label>
      </ion-item>
    </section>
  }
</ion-content>
