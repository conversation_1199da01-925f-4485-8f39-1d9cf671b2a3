section {
  margin-bottom: 1.5rem;
}

.cartoes {
  margin-top: 1rem;

  ion-item {
    --border-radius: 8px;
    //--inner-padding-end: 0;
    //--padding-start: 0;
    --padding-end: 0;

    ion-label {
      h2 {
        font-weight: 700;
        font-size: 16px;
      }
    }

    ion-icon {
      color: var(--ion-color-primary);
    }
  }
}

.bloqueado {
  --background: #fbe6e2;
}

.cartoes {
  .informacao-cartao {
    color: var(--ion-color-medium-contrast);

    p {
      color: var(--ion-color-medium-contrast);
    }
  }
}

.carregar-cartao {
  background: var(--ion-color-medium-cartao);
  border-radius: 8px;
}
