<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar [mostrarVoltar]="false"><PERSON><PERSON><PERSON> se<PERSON><PERSON></mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content class="content-default">
    <article>
      <p>Digite a nova senha do cartão:</p>

      <form [formGroup]="form">
        <ion-item lines="none" class="ion-margin-top">
          <ion-input labelPlacement="stacked" label="Crie uma senha" formControlName="novaSenha" placeholder="senha" type="password"
                     inputmode="numeric" maxlength="4" errorText="Informe a nova senha">
            <ion-input-password-toggle slot="end"></ion-input-password-toggle>
          </ion-input>
        </ion-item>

        <ion-item lines="none">
          <ion-input labelPlacement="stacked" label="Repita a senha" formControlName="confirmarSenha" placeholder="senha" type="password"
                     inputmode="numeric" maxlength="4" errorText="Confirme a nova senha">
            <ion-input-password-toggle slot="end"></ion-input-password-toggle>
          </ion-input>
        </ion-item>
      </form>
    </article>

    <article class="validacao">
      <ion-row>
        <ion-col size="1">
          <ion-icon *ngIf="!senhaContemQuatroDigitos" name="close-outline" color="danger"></ion-icon>
          <ion-icon *ngIf="senhaContemQuatroDigitos" name="checkmark-outline" color="success"></ion-icon>
        </ion-col>
        <ion-col>
          <ion-text>Sua senha deve conter 4 dígitos (númericos)</ion-text>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="1">
          <ion-icon *ngIf="!senhaNaoPossuiNumerosRepetidos" name="close-outline" color="danger"></ion-icon>
          <ion-icon *ngIf="senhaNaoPossuiNumerosRepetidos" name="checkmark-outline" color="success"></ion-icon>
        </ion-col>
        <ion-col>
          <ion-text>Não utilize números repetidos (6699)</ion-text>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="1">
          <ion-icon *ngIf="!senhaNaoPossuiSequenciaNumerica" name="close-outline" color="danger"></ion-icon>
          <ion-icon *ngIf="senhaNaoPossuiSequenciaNumerica" name="checkmark-outline" color="success"></ion-icon>
        </ion-col>
        <ion-col>
          <ion-text>Não utilize sequenciais (1234)</ion-text>
        </ion-col>
      </ion-row>

      <ion-row>
        <ion-col size="1">
          <ion-icon name="checkmark-outline" color="success"></ion-icon>
        </ion-col>
        <ion-col>
          <ion-text>Confirme se os dados estão preenchidos</ion-text>
        </ion-col>
      </ion-row>
    </article>
</ion-content>
<ion-footer>
  <ion-button class="btn ion-margin-bottom" expand="block" [disabled]="form.invalid || !senhaContemQuatroDigitos || !senhaNaoPossuiNumerosRepetidos ||
                !senhaNaoPossuiSequenciaNumerica" (click)="continuar()">Criar senha
  </ion-button>
</ion-footer>
