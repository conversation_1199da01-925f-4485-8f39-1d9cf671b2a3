import {Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {ModalController} from '@ionic/angular';
import {Router} from '@angular/router';
import {concatMap} from 'rxjs';
import {AuthService, CredencialService, MetodoSegurancaEnum, TipoObjetivoEnum, Usuario} from '@corporativo/shared';
import {FluxoService} from '../../../../../../../libs/shared/src/lib/services/fluxo.service';
import {toast} from '@utils/toast.util';
import {loading} from '@utils/loading.util';
import {ModalErroComponent, ModalSegurancaComponent, ModalSucessoComponent} from '@corporativo/modals';
import {ValidatorsApp} from '@utils/validators.util';


@Component({
  selector: 'mobile-criar-senha',
  templateUrl: './criar-senha.page.html',
  styleUrls: ['./criar-senha.page.scss'],
  standalone: false
})
export class CriarSenhaPage implements OnInit {

  form: FormGroup;
  usuario!: Usuario;
  senhaContemQuatroDigitos: boolean = false;
  senhaNaoPossuiNumerosRepetidos: boolean = false;
  senhaNaoPossuiSequenciaNumerica: boolean = false;
  confirmacao: boolean = false;
  credencial: any;
  jwt: any;
  fluxoDados: any = {};

  constructor(
    public formBuilder: FormBuilder,
    private credencialService: CredencialService,
    private authService: AuthService,
    private fluxoService: FluxoService,
    private modalController: ModalController,
    private router: Router
  ) {
    this.usuario = this.authService.getUser();
    this.form = this.formBuilder.group({
      novaSenha: ['', Validators.compose([Validators.required,])],
      confirmarSenha: ['', Validators.compose([Validators.required])]
    }, {
      validator: ValidatorsApp.senhasIguaisValidator
    });

    this.form?.get('novaSenha')?.valueChanges.subscribe((value: any) => {
      this.validarSenha(value)
    });
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.credencial = state.credencial;
  }

  async ngOnInit() {
    this.usuario = this.authService.getUser();
  }

  validarSenha(senha: string) {
    const numeros = senha.match(/[0-9]/g);
    const ehSequencial = (num: string): boolean => {
      const sequencialCrescente = '0123456789';
      const sequencialDecrescente = '9876543210';

      return (
        sequencialCrescente.includes(num) || sequencialDecrescente.includes(num)
      );
    };

    this.senhaContemQuatroDigitos = numeros !== null && numeros.length === 4;
    this.senhaNaoPossuiNumerosRepetidos = !/(.)\1{1,}/.test(numeros ? numeros.join('') : '');
    this.senhaNaoPossuiSequenciaNumerica = numeros ? !ehSequencial(numeros.join('')) : true;
  }

  alterar() {
    const idCredencial = this.credencial.idCredencial;
    const senha = this.form.value.novaSenha || 0;
    const response$ = this.credencialService.salvarSenha(idCredencial, senha, this.authService.getToken() || '');
    loading(response$.pipe(
      concatMap((dados: any) => {
        if (dados.confirmacao) {
          return this.credencialService.desbloquearPortador(this.credencial.idCredencial);
        } else {
          return toast('Aconteceu algum erro ao tentar cadastrar sua senha');
        }
      })).subscribe({
      next: (value: any) => {
        if (value) {
          for (const cred of this.usuario.credenciais) {
            if (this.credencial.idCredencial === cred.idCredencial) {
              cred.status = 1;
            }
          }
          this.authService.setCredenciais(this.usuario.credenciais);
          this.abrirModalSucesso();
        }
      }, error: (err: any) => {
        const message = err.error.msg ? err.error.msg : err.error.message;
        this.abrirModalErro(message);
      }
    }));
  }

  async continuar() {
    this.fluxoDados.cpf = this.usuario.documento;
    this.fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaCartao
    this.fluxoDados.criarSenhaCartao = true;

    this.fluxoService.set({
      fluxoDados: this.fluxoDados,
      credencial: this.credencial,
      senha: this.form.value.novaSenha || 0
    });
    return this.router.navigate(['selfie-caf']);
  }

  async verificarSeguranca() {
    if (this.credencial.metodoSegurancaTransacao == MetodoSegurancaEnum.NaoVerificar) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: this.credencial.metodoSegurancaTransacao,
        idCredencial: this.credencial.idCredencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async abrirModalErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Criar senha',
        tituloTexto: 'Criar senha',
        mensagem: message,
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Ir para o início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.router.navigate(['/inicio']);
    }
  }

  async abrirModalSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Criar senha',
        tituloTexto: 'Senha',
        mensagem: 'Senha criada com sucesso! ',
        tituloBotao: `Ir para o início`
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.router.navigate(['/inicio']);
    }
  }
}
