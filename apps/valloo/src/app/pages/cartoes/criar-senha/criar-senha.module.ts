import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {IonicModule} from '@ionic/angular';
import {ReactiveFormsModule} from '@angular/forms';
import {MaskitoDirective} from '@maskito/angular';
import {CriarSenhaPage} from './criar-senha.page';
import {CriarSenhaPageRoutingModule} from './criar-senha-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    IonicModule,
    CriarSenhaPageRoutingModule,
    ReactiveFormsModule,
    MaskitoDirective,
    TitleToolbarModule
  ],
  declarations: [CriarSenhaPage]
})
export class CriarSenhaPageModule {

}
