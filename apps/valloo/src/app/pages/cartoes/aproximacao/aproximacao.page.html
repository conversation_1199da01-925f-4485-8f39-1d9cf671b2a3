<ion-content class="content-default" [fullscreen]="true">
  <section>
    <article class="img">
      <ion-icon [name]="isNfcInactive ? 'nfc-ativado' : 'nfc-desativado'"></ion-icon>
    </article>

    <article class="info">
      <div>
        <div class="titulo">
          <h2>{{isNfcInactive ? 'Habilitar aproximação' : 'Desabilitar aproximação'}}</h2>
        </div>
        <p>{{isNfcInactive ? 'Deseja realmente habilitar a aproximação para este cartão?' : 'Deseja realmente desabilitar a aproximação para este cartão?'}}</p>
      </div>
      <div class="acoes">
        <ion-button class="btn" expand="block" (click)="continuar()">{{isNfcInactive ? 'Habilitar aproximação' : 'Desabilitar aproximação'}}</ion-button>
        <ion-button class="ion-margin-top" expand="block" fill="outline"
                    routerLink="/inicio">{{isNfcInactive ? 'Habilitar depois' : 'Desabilitar depois'}}</ion-button>
      </div>
    </article>
  </section>
</ion-content>
