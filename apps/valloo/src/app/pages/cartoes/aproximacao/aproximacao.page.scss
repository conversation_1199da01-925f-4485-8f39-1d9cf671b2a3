ion-content {
  section {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    width: 100%;

    .img {
      padding: 2rem;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 80px;
    }

    .info {
      .titulo {
        display: flex;
        align-items: center;
        justify-content: center;

        h2 {
          margin: 0;
          color: var(--ion-color-text-titulo);
          font-weight: var(--ion-font-weight-h1);
          font-size: var(--ion-font-size-h1);
        }

        img {
          margin-left: 0.5rem;
        }
      }

      background-color: #F0F0F0;
      height: 100%;
      width: 115%;
      border-radius: 24px 24px 0 0;
      padding: 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      margin-top: -2rem;
      margin-bottom: -24px;

      ion-button {
        width: 100%;
      }
    }
  }

  .acoes {
    width: 100%;
  }
}

p {
  text-align: center;
}
