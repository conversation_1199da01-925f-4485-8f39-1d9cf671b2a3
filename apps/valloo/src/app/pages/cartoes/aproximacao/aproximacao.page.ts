import { Component } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { ModalController } from "@ionic/angular";
import { loading } from "@utils/loading.util";
import { concatMap } from "rxjs";
import {AuthService, CredencialService, StorageService, Usuario} from '@corporativo/shared';
import {ModalAtencaoComponent, ModalSucessoComponent} from '@corporativo/modals';

@Component({
  selector: 'app-desabilitar-aproximacao',
  templateUrl: './aproximacao.page.html',
  styleUrls: ['./aproximacao.page.scss'],
  standalone: false
})
export class AproximacaoPage {

  credencialSelecionada: any;
  usuario!: Usuario;
  idCredencial!: number;
  idConta!: number;
  idCredencialVirtual!: number;

  constructor(
    public router: Router,
    private authService: AuthService,
    private storageService: StorageService,
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController,
    private credencialService: CredencialService
  ) {
    this.usuario = this.authService.getUser();
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      for (const credencial of this.usuario.credenciais) {
        const conta = credencial.contas.find((c: any) => c.idConta == this.idConta);
        if (conta) {
          if (credencial.virtual) {
            this.idCredencialVirtual = credencial.idCredencial;
          } else {
            this.idCredencial = credencial.idCredencial;
          }
        }
      }
    }
    this.buscarCredencialFisica(this.idCredencial == null ? this.idCredencialVirtual : this.idCredencial);
  }

  ionViewDidEnter() {}

  buscarCredencialFisica(idCredencial: number) {
    this.credencialSelecionada = this.usuario.credenciais.find((x: any) => x.idCredencial == idCredencial);
  }

  continuar(){
    const estado = this.credencialSelecionada.statusNfc == 1 ? 0 : 1;
    const response$ = this.credencialService.bloquearDesbloquearNFC(estado, this.credencialSelecionada.idCredencial)
    loading(response$.pipe(
      concatMap(() => {
        return this.credencialService.buscarCartaoFisico(this.credencialSelecionada.idCredencial);
      })).subscribe({
      next: (credencial: any) => {
        return this.modalSucesso(credencial);
      }, error: (err: any) => {
        const message = err.error.msg ? err.error.msg : err.error.message;
        return this.abrirModalAtencao(message);
      }
    }));
  }

  async modalSucesso(credencial: any) {
    const estado = credencial.statusNfc == 0 ? ' está desabilitado' : ' está habilitado';
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: credencial.statusNfc == 0 ? 'Desabilitar aproximação' : 'Habilitar aproximação',
        mensagem: 'O pagamento por aproximação do cartão ' + credencial.credencialMascarada + estado,
        tituloBotao: 'Ir para o início',
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  async abrirModalAtencao(message: string) {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        mensagem: message,
        tituloBotaoPrimario: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar para o inicio'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.router.navigate(['/inicio']);
    }
  }

  get isNfcInactive(): boolean {
    return !this.credencialSelecionada.statusNfc;
  }
}
