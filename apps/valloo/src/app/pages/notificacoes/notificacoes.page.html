<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar>Notificações</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  @if (isLoading) {
    @for (i of [1, 2, 3, 4]; track i) {
      <ion-item class="ion-margin-bottom" lines="full" [detail]="false">
        <ion-avatar slot="start">
          <ion-skeleton-text [animated]="true"></ion-skeleton-text>
        </ion-avatar>
        <ion-label>
          <h2>
            <mobile-skeleton [animated]="true" [width]="'150px'"></mobile-skeleton>
          </h2>
          <p>
            <mobile-skeleton [animated]="true" [width]="'200px'"></mobile-skeleton>
          </p>
          <p>
            <mobile-skeleton [animated]="true"></mobile-skeleton>
          </p>
        </ion-label>
      </ion-item>
    }
  } @else if (mensagens.length > 0) {
    @for (mensagem of mensagens; track mensagem.id) {
      @if (mensagem.aplicativoMensagem) {
        <ion-item class="ion-margin-bottom" lines="full" [button]="true" [detail]="false"
                  (click)="irParaRotas(mensagem)"
                  [class.nao-lido]="!mensagem.blLido">
          <ion-avatar slot="start">
            <ion-img src="{{mensagem.aplicativoMensagem.img}}"></ion-img>
          </ion-avatar>
          <ion-label>
            <h2>{{ mensagem.aplicativoMensagem.txTituloMensagem }}</h2>
            <p>{{ mensagem.aplicativoMensagem.txMensagem }}</p>
            <p>{{ mensagem.aplicativoMensagem.dtHrInclusao | date: 'dd/MM/yyyy HH:mm' }}</p>
          </ion-label>
        </ion-item>
      }
    }
  } @else {
    <mobile-estado-vazio [iconName]="'sino'" [message]="'Você ainda não possui notificações!'"></mobile-estado-vazio>
  }
  <ion-infinite-scroll (ionInfinite)="onIonInfinite($event)" *ngIf="!pageable?.last">
    <ion-infinite-scroll-content loadingText="Carregando mais..."
                                 loadingSpinner="bubbles"></ion-infinite-scroll-content>
  </ion-infinite-scroll>
</ion-content>
