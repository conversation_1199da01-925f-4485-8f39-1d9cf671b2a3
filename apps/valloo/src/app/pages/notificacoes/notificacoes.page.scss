ion-item {
  --padding-start: 32px;
  //margin-left: -8px;
  --background: var(--ion-color-background);
  --border-color: #f2f2f2;
  position: relative;

  ion-label {
    h2 {
      font-size: 16px;
      font-weight: 700;
      color: var(--ion-color-primary);
    }

    p {
      color: var(--ion-color-primary);
      margin-top: 6px;
      font-size: 16px;
    }

    p:last-child {
      color: var(--ion-color-gray-650);
      margin-top: 8px;
      font-size: 12px;
    }
  }

  &.nao-lido {
    --background: #f6f6f6;
  }

  &.nao-lido::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 16px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: var(--ion-color-tertiary);
    border-radius: 50%;
    z-index: 2;
    --background: var(--ion-color-gray-800);
  }
}


//ion-img {
//  background: #ebe9eb;
//  border-radius: 50%;
//  padding: 10px;
//}

//.titulo {
//  margin-top: 4px;
//}
//
//.mensagem {
//  font-size: 12px;
//  padding-top: 7px;
//  color: var(--ion-color-gray-50);
//}

//ion-segment {
//  --background: var(--ion-color-background);
//
//  ion-segment-button {
//    --border-radius: 4px;
//    --background: var(--ion-color-medium);
//    font-size: 12px;
//    font-weight: 600;
//    margin-right: 0.5rem;
//    min-height: 22px;
//    text-transform: none;
//    --color: var(--ion-color-gray-50);
//    --indicator-color: var(--ion-color-primary);
//    --color-checked: var(--ion-color-primary-contrast)
//  }
//
//  ion-segment-button:last-child {
//    margin-right: 0;
//  }
//
//  ion-segment-button::before {
//    border: none;
//  }
//}
