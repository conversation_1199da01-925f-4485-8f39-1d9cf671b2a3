import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { InfiniteScrollCustomEvent, ModalController } from '@ionic/angular';
import { lastValueFrom, Observable } from 'rxjs';
import {AuthService, Conta, Credencial, NotificacoesService, ProdutoService, Usuario} from '@corporativo/shared';
import {ModalNotificacaoDetalheComponent} from '@corporativo/modals';

const PRODUTO_MOEDEIRO = 280139;

@Component({
  selector: 'app-notificacoes',
  templateUrl: './notificacoes.page.html',
  styleUrls: ['./notificacoes.page.scss'],
  standalone: false
})
export class NotificacoesPage {
  produtos: any[];
  mensagens: any[] = [];
  idConta!: number;
  usuario!: Usuario;
  idCredencial!: number;
  contas!: Conta[];
  conta!: Conta;
  credencial: any;
  page = 1;
  pageable: any;
  isLoading = false;

  constructor(
    private notificacoesService: NotificacoesService,
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private modalController: ModalController,
    private produtoService: ProdutoService,
    private router: Router
  ) {
    this.usuario = this.authService.getUser();
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      const credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c: any) => c.idConta == this.idConta);
        return conta ? x : null;
      });
      if (credencial) {
        this.contas = credencial.contas;
      }

      for (const credencial of this.usuario.credenciais) {
        const conta = credencial.contas.find((c: any) => c.idConta == this.idConta);
        if (conta) {
          this.idCredencial = credencial.idCredencial;
          this.conta = conta;
        }
      }
    }
  }

  ionViewDidEnter() {
    // this.buscarProdutos();
    // this.buscarMensagens();
  }

  ionViewWillLeave() {
    const mensagensNaoLidas: any = this.mensagens.filter((m: any) => !m.blLido);
    if (mensagensNaoLidas.length) {
      lastValueFrom(this.notificacoesService.marcarMensagensComoLidas());
    }
    this.mensagens = [];
  }

  buscarProdutos() {
    this.credencial = this.usuario.credenciais.filter((x: any) => x.idCredencial == this.idCredencial);
    this.produtoService.getFuncionalidades(this.idConta).subscribe({
      next: (produto: any) => {
        this.produtos = [];
        this.produtos = produto;
        const maisServicos: any[] = this.produtoService.getMaisServicos();
        const funcionalidesCartao: any[] = this.produtoService.getFuncionalidadesCartao(this.credencial);
        this.produtos = this.produtos.concat(funcionalidesCartao, maisServicos);
      }
    });
  }

  buscarMensagens(ev: any = null) {
    this.isLoading = true; // Start loading
    this.notificacoesService.buscarNotificacoes(this.page).subscribe((m: any) => {
      this.pageable = m;
      this.mensagens = [...this.mensagens].concat(m.content);
      console.log('this.mensagens...', this.mensagens);

      this.isLoading = false; // End loading

      if (ev) {
        (ev as InfiniteScrollCustomEvent).target.complete();
      }
    }, () => {
      this.isLoading = false; // Ensure loading ends on error
    });
  }

  irParaRotas(mensagem: any) {
    if (!mensagem.aplicativoMensagem.blDetalhe) {
      this.marcarMensagensComoLidas(mensagem);
    } else if (mensagem.aplicativoMensagem.txTituloDetalhe != null && mensagem.aplicativoMensagem.txDescricaoDetalhe != null) {
      this.exibirDetalheNotificacao(mensagem);
    }
  }

  marcarMensagensComoLidas(mensagem: any) {
    if (!mensagem.blLido) {
      this.notificacoesService.marcarMensagemComoLida(mensagem.id).subscribe({
        next: () => {
          if (mensagem.aplicativoMensagem.aplicativoServico != null) {
            this.navegarParaRota(mensagem.aplicativoMensagem.aplicativoServico.nomeServico);
          }
        }
      });
    } else {
      if (mensagem.aplicativoMensagem.aplicativoServico != null) {
        this.navegarParaRota(mensagem.aplicativoMensagem.aplicativoServico.nomeServico);
      }
    }
  }

  navegarParaRota(nomeServico: string) {
    const produto = this.produtos.filter((x: any) => x.codigo == nomeServico);
    if (!produto.length) {
      this.router.navigate(['/inicio']);
      return;
    }
    if (produto[0].codigo == 'campanhas') {
      const conta = this.contas.filter((x: any) => x.idProdutoInstituicao == PRODUTO_MOEDEIRO);
      return this.router.navigate(['/' + produto[0].url + '/' + conta[0].idConta]);
    }
    if (produto[0].codigo == 'saque_fgts' || produto[0].codigo == 'parcelar_debitos') {
      return this.router.navigate(['/' + produto[0].url]);
    }
    this.router.navigate(['/' + produto[0].url + '/' + this.idConta]);
  }

  async exibirDetalheNotificacao(mensagem: any) {
    const modal = await this.modalController.create({
      component: ModalNotificacaoDetalheComponent,
      componentProps: {
        mensagem: mensagem,
        idConta: this.idConta,
        idCredencial: this.idCredencial,
        contas: this.contas,
        credencial: this.credencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
  }

  onIonInfinite(ev: InfiniteScrollCustomEvent) {
    if (this.pageable.last) {
      return;
    }
    this.page = this.page + 1;
    this.buscarMensagens(ev);
  }
}
