import {NgModule} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {NotificacoesPage} from './notificacoes.page';
import {NotificacoesPageRoutingModule} from './notificacoes-routing.module';
import {MaskitoDirective} from '@maskito/angular';
import {EstadoVazioModule, SkeletonModule, TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    NotificacoesPageRoutingModule,
    ReactiveFormsModule,
    NgOptimizedImage,
    MaskitoDirective,
    TitleToolbarModule,
    SkeletonModule,
    EstadoVazioModule
  ],
  declarations: [NotificacoesPage]
})
export class NotificacoesPageModule {
}
