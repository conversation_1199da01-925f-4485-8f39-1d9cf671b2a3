import {NgModule} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {RedefinirSenhaPage} from './redefinir-senha.page';
import {RedefinirSenhaRoutingModule} from './redefinir-senha-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  declarations: [RedefinirSenhaPage],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RedefinirSenhaRoutingModule,
    NgOptimizedImage,
    ReactiveFormsModule,
    MaskitoDirective,
    TitleToolbarModule,
    MaskitoDirective,
    TitleToolbarModule,
  ]
})
export class RedefinirSenhaModule {
}
