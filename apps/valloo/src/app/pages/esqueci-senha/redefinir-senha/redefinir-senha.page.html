<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="true"><PERSON><PERSON><PERSON> senha</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <article class="titulo">
    <ion-text>Vamos criar uma senha para o seu aplicativo. Siga os critérios de
      segurança para criá-la.
    </ion-text>
  </article>

  <article>
    <form [formGroup]="form">
      <ng-container>
        <ion-item *ngIf="validacaoCaf">
          <ion-input formControlName="token" labelPlacement="stacked" label="Token" placeholder="token" maxlength="8">
            <ion-button fill="clear" color="secondary" slot="end" size="small" class="btn-info-token"
                        (click)="informarSobreToken($event)">
              <ion-icon class="icon-info" name="information-circle-outline" slot="icon-only"></ion-icon>
            </ion-button>
          </ion-input>
        </ion-item>
        <ion-item lines="none" *ngIf="validacaoCaf">
          <ion-button color="primary" class="reenviar-token" fill="clear" (click)="reenviarToken()">Reenviar token</ion-button>
        </ion-item>
      </ng-container>

      <ion-item class="ion-margin-top">
        <ion-input formControlName="novaSenha" labelPlacement="stacked" [type]="verSenha ? 'text' : 'password'"
                   label="Crie uma senha para acessar o aplicativo"
                   placeholder="senha">
          <ion-icon size="small" slot="end" [name]="verSenha ? 'eye-outline' : 'eye-off-outline'"
                    (click)="verSenha = !verSenha"></ion-icon>
        </ion-input>

      </ion-item>

      <ion-item class="ion-margin-top">
        <ion-input formControlName="confirmarSenha" labelPlacement="stacked" label="Repita a senha"
                   placeholder="senha" [type]="verRepitaSenha ? 'text' : 'password'">
          <ion-icon size="small" slot="end" [name]="verRepitaSenha ? 'eye-outline' : 'eye-off-outline'"
                    (click)="verRepitaSenha = !verRepitaSenha"></ion-icon>
        </ion-input>
      </ion-item>
    </form>
  </article>

  <article class="validacao">
    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemLetraMaiuscula" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemLetraMaiuscula" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Uma letra MAIÚSCULA</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemLetraMinuscula" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemLetraMinuscula" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Uma letra minúscula</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemNumero" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemNumero" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Um numeral (123456)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemCaracterEspecial" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemCaracterEspecial" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Um caractere especial (# $ % *)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemMinimoCaracteres" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemMinimoCaracteres" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>No mínimo 8 caracteres</ion-text>
      </ion-col>
    </ion-row>
  </article>
</ion-content>

<ion-footer>
  <ion-button class="btn" expand="block" (click)="cadastrarSenha()"
              [disabled]="form.invalid || !senhaContemLetraMaiuscula || !senhaContemLetraMinuscula ||
                !senhaContemNumero || !senhaContemCaracterEspecial || !senhaContemMinimoCaracteres">
    Alterar senha
  </ion-button>
</ion-footer>
