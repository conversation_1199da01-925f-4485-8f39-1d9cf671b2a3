import {Component} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {ModalController, PopoverController} from '@ionic/angular';
import {environment} from '../../../../environments/environment';
import {AuthService, FuncionalidadeService, OnboardingService} from '@corporativo/shared';
import {loading} from '@utils/loading.util';
import {toast} from '@utils/toast.util';
import {
  InfoEnvioTokenComponent
} from '../../../../../../../libs/components/src/lib/info-envio-token/info-envio-token.component';
import {ModalErroComponent, ModalSucessoComponent} from '@corporativo/modals';
import {ValidatorsApp} from '@utils/validators.util';

@Component({
  selector: 'mobile-redefinir-senha',
  templateUrl: './redefinir-senha.page.html',
  styleUrls: ['./redefinir-senha.page.scss'],
  standalone: false
})
export class RedefinirSenhaPage {

  form: FormGroup;
  senhaContemLetraMinuscula = false;
  senhaContemLetraMaiuscula = false;
  senhaContemNumero = false;
  senhaContemCaracterEspecial = false;
  senhaContemMinimoCaracteres = false;
  fluxoDados: any = {};
  jwt: any;
  validacaoCaf: any;
  verSenha = false;
  verRepitaSenha = false;

  constructor(
    public formBuilder: FormBuilder,
    public router: Router,
    public onboardingService: OnboardingService,
    public funcionalidadeService: FuncionalidadeService,
    public popoverController: PopoverController,
    private modalController: ModalController,
    private authService: AuthService
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
    this.validacaoCaf = this.fluxoDados.validacaoCaf;
    this.form = this.formBuilder.group({
      novaSenha: ['', Validators.compose([Validators.required,])],
      confirmarSenha: ['', Validators.compose([Validators.required])],
      token: [this.validacaoCaf == false ? 'validadoCaf' : '']
    }, {
      validator: ValidatorsApp.senhasIguaisValidator
    });

    this.form?.get('novaSenha')?.valueChanges.subscribe((value: any) => {
      this.validarSenha(value);
    });

  }

  reenviarToken() {
    return this.enviarSMS();
  }

  validarSenha(senha: string) {
    const regexLetraMinuscula = /[a-z]/;
    const regexLetraMaiusula = /[A-Z]/;
    const regexNumeros = /[0-9]/;
    const regexCaracterEspecial = /[^a-zA-Z0-9]/;

    this.senhaContemLetraMinuscula = regexLetraMinuscula.test(senha);
    this.senhaContemLetraMaiuscula = regexLetraMaiusula.test(senha);
    this.senhaContemNumero = regexNumeros.test(senha);
    this.senhaContemCaracterEspecial = regexCaracterEspecial.test(senha);
    this.senhaContemMinimoCaracteres = senha.length >= 8;
  }

  cadastrarSenha() {
    this.form.value.token = this.form.value.token.toUpperCase();
    this.fluxoDados.senha = this.form.value.novaSenha;
    const request = {
      cpf: this.fluxoDados.cpf,
      novaSenha: this.fluxoDados.senha,
      idProcessadora: environment.idProcessadora,
      idInstituicao: environment.idInstituicao,
      token: this.form.value.token
    }
    const retorno$ = this.onboardingService.alterarSenhaPortador(request);
    loading(retorno$.subscribe({
      next: (data: any) => {
        this.abrirModalSucesso();
      }, error: (error: any) => {
        const message = error.msg ? error.msg : error.error.DTL == null ? error.error.msg : error.error.DTL ;
        this.abrirModalErro(message);
      }
    }));
  }

  async abrirModalSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Senha alterada com sucesso',
        mensagem: 'Sua senha foi alterada.',
        tituloBotao: 'Ir para o início',
        urlRetorno: '/login'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      this.authService.clearToken();
      this.form.reset();
      await this.router.navigate(['/login']);
    }
  }

  async abrirModalErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: message ? 'Ocorreu um erro' : 'Erro inesperado',
        mensagem: message ? message : 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      this.authService.clearToken();
      await this.router.navigate(['/login']);
    }
  }

  async informarSobreToken(ev: any) {
    const popover = await this.popoverController.create({
      component: InfoEnvioTokenComponent,
      event: ev,
      translucent: true
    });
    return await popover.present();
  }

  async enviarSMS() {
    const response$ = this.funcionalidadeService.enviarTokenSenha(this.fluxoDados.cpf, this.fluxoDados.celular);
    loading(response$.subscribe({
      next: (data: any) => {
        if (!data.sucesso) {
          toast('Desculpe, não foi possível realizar a operação.');
        } else {
          toast('Token enviado com sucesso.');
        }
      }, error: (error: any) => {
        const message = error.error.msg ? error.error.msg : error.msg;
        this.abrirModalErro(message);
      }
    }));
  }
}
