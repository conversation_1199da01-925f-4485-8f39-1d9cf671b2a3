<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="true"><PERSON><PERSON><PERSON> senha</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <article class="titulo">
    <ion-text>Para alterar a senha do aplicativo, faça a validação facial e cadastre a nova senha. É bem simples!<br><br>
      Para começar, insira o documento:
    </ion-text>
  </article>

  <article>
    <form [formGroup]="form">
      <ion-item lines="none">
        <ion-input label="CPF" formControlName="documento" labelPlacement="stacked" [disabled]="habilitarTelefone"
                   type="tel" placeholder="Informe o CPF" [brmasker]="{mask:'000.000.000-00', len:14, type:'num'}" (ionInput)="verificarPj($event.detail.value)"
                   helperText="Digite apenas os números do CPF conta."
                   errorText="{{mensagemErro}}"></ion-input>
      </ion-item>

      <ion-item *ngIf="habilitarTelefone" class="ion-margin-top" lines="none">
        <ion-input type="tel" formControlName="celular" label="Celular" placeholder="(00)00000-0000"
                   labelPlacement="stacked"
                   [maskito]="phoneMaskOptions" [maskitoElement]="maskPredicate"
                   helperText="Digite seu celular."
                   errorText="Informe um celular válido"></ion-input>
      </ion-item>
    </form>
  </article>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" [disabled]="this.form.invalid" (click)="habilitarTelefone ? enviarSms() : encontrarCafNecessario()">
    Avançar
  </ion-button>
</ion-footer>
