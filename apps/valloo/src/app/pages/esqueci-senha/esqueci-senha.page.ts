import {Component} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {MaskitoElementPredicate, MaskitoOptions} from '@maskito/core';
import {environment} from '../../../environments/environment';
import {Router} from '@angular/router';
import {ModalController} from '@ionic/angular';
import {FuncionalidadeService, OnboardingService, TipoObjetivoEnum} from '@corporativo/shared';
import {FluxoService} from '../../../../../../libs/shared/src/lib/services/fluxo.service';
import {ModalErroComponent} from '@corporativo/modals';
import {loading} from '@utils/loading.util';
import {toast} from '@utils/toast.util';
import {phoneMask} from '@utils/masks.util';
import {TextUtil} from '@utils/text.util';
import {ValidatorsApp} from '@utils/validators.util';

@Component({
  selector: 'mobile-esqueci-senha',
  templateUrl: './esqueci-senha.page.html',
  styleUrls: ['./esqueci-senha.page.scss'],
  standalone: false
})
export class EsqueciSenhaPage {

  habilitarTelefone = false;
  mensagemErro = 'Informe um documento válido';

  form = new FormGroup(
    {
      documento: new FormControl('', [Validators.required]),
      celular: new FormControl('')
    }
  );

  readonly phoneMaskOptions: MaskitoOptions = phoneMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();

  constructor(
    private onboardingService: OnboardingService,
    public router: Router,
    private modalController: ModalController,
    private funcionalidadeService: FuncionalidadeService,
    private fluxoService: FluxoService,
  ) {
    this.form.reset();
    this.habilitarTelefone = false;
  }

  verificarPj(value: any) {
    const documento = TextUtil.removeNotDigit(value);
    this.form.get('documento')?.setValidators([Validators.required, ValidatorsApp.cpf()]);
    if (documento.length === 11) {
      this.mensagemErro = 'Informe um CPF válido';
    } else if (documento.length === 0) {
      this.mensagemErro = 'Informe um documento válido';
    }
  }

  async encontrarCafNecessario() {
    if (this.form.invalid) {
      await toast('Informações inválidas');
      return;
    }
    let documento = TextUtil.removeNotDigit(String(this.form.value.documento));

    await loading(this.onboardingService.encontrarCafNecessario(documento).subscribe({
        next: async (value: any) => {
          if (value) {
            const fluxoDados: any = {};
            fluxoDados.cpf = documento;
            fluxoDados.isPreCadastro = false;
            fluxoDados.validacaoCaf = false;
            fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaLogin;
            fluxoDados.tipoLogin = environment.tipoLoginPf;
            fluxoDados.tipo = 'troca-senha-login';
            this.fluxoService.set({
              fluxoDados: fluxoDados
            });
            await this.router.navigate(['/selfie-caf']);
          } else {
            this.habilitarTelefone = true;
          }
        }, error: (error: any) => {
          const message = error.error.msg ? error.error.msg : error.message;
          this.abrirModalError(message);
        }
      })
    );
  }

  async abrirModalError(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: message ? 'Ocorreu um erro' : 'Erro inesperado',
        tituloTexto: 'Esqueci senha',
        mensagem: message ? message : 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Ir para o início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      this.form.reset();
      this.habilitarTelefone = false
      await this.router.navigate(['/login']);
    } else {
      await this.router.navigate(['/esqueci-senha']);
    }
  }

  async enviarSms() {
    if (this.form.invalid || this.form.value.celular == '') {
      await toast('Informações inválidas');
      return;
    }

    const celular = TextUtil.removeNotDigit(String(this.form.value.celular));
    const documento = TextUtil.removeNotDigit(String(this.form.value.documento));
    const response$ = this.funcionalidadeService.enviarTokenSenha(documento, celular);
    loading(response$.subscribe({
      next: async (data: any) => {
        if (data.sucesso) {
          const fluxoDados: any = {};
          fluxoDados.cpf = TextUtil.removeNotDigit(String(this.form.value.documento));
          fluxoDados.celular = celular;
          fluxoDados.validacaoCaf = true;
          await this.router.navigate(['esqueci-senha/redefinir-senha'], { state: { fluxoDados: fluxoDados } });
        }
      }, error: (error: any) => {
        const message = error.error.DTL ? error.error.DTL : error.error.msg;
        this.abrirModalError(message);
      }
    }));
  }
}
