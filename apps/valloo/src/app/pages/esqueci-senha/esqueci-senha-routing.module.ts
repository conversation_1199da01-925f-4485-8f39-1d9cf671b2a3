import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {EsqueciSenhaPage} from './esqueci-senha.page';

const routes: Routes = [
  {
    path: '',
    component: EsqueciSenhaPage
  },
  {
    path: 'redefinir-senha',
    loadChildren: () => import('./redefinir-senha/redefinir-senha.module').then(m => m.RedefinirSenhaModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EsqueciSenhaRoutingModule {}
