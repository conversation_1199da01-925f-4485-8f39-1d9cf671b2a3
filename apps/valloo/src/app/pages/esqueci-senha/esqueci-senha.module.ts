import {NgModule} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {EsqueciSenhaPage} from './esqueci-senha.page';
import {EsqueciSenhaRoutingModule} from './esqueci-senha-routing.module';
import {TitleToolbarModule} from '@corporativo/components';
import {DirectivesModule} from '@corporativo/shared';

@NgModule({
  declarations: [EsqueciSenhaPage],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    EsqueciSenhaRoutingModule,
    NgOptimizedImage,
    ReactiveFormsModule,
    MaskitoDirective,
    TitleToolbarModule,
    MaskitoDirective,
    DirectivesModule,
    TitleToolbarModule,
    DirectivesModule,
  ]
})
export class EsqueciSenhaModule {
}
