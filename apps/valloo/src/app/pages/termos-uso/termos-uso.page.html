<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="true">Termos de uso</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content class="content-default">
  <ng-container *ngIf="termoUso$ | async as termo; else carregar">
    <ng-container *ngIf="termo; else termoNaoEncontrado">
      <div class="diretrizes" [innerHTML]="termo?.txDiretriz"></div>
    </ng-container>
    <ng-template #termoNaoEncontrado>
      <ion-text class="ion-text-center" color="medium">
        <p>
          Termos de uso não encontrado.
        </p>
      </ion-text>
    </ng-template>
  </ng-container>
  <ng-template #carregar>
    <h1>
      <ion-skeleton-text animated="true"></ion-skeleton-text>
    </h1>
    <p>
      <ion-skeleton-text animated="true"></ion-skeleton-text>
    </p>
    <p>
      <ion-skeleton-text animated="true"></ion-skeleton-text>
    </p>
    <p>
      <ion-skeleton-text animated="true"></ion-skeleton-text>
    </p>
    <p>
      <ion-skeleton-text animated="true"></ion-skeleton-text>
    </p>
  </ng-template>
</ion-content>
