import {Component, OnInit} from '@angular/core';
import {Observable} from 'rxjs';
import {DiretrizService} from '@corporativo/shared';

@Component({
  selector: 'mobile-termos-uso',
  templateUrl: './termos-uso.page.html',
  styleUrls: ['./termos-uso.page.scss'],
  standalone: false
})
export class TermosUsoPage implements OnInit {
  termoUso$!: Observable<any>;

  constructor(
    private diretrizService: DiretrizService
  ) {

  }

  ngOnInit() {
    this.termoUso$ = this.diretrizService.buscarTermosUso();
  }
}
