import {Component, OnInit} from '@angular/core';
import {forkJoin, Observable} from 'rxjs';
import {fromPromise} from 'rxjs/internal/observable/innerFrom';
import {Device} from '@capacitor/device';
import {App} from '@capacitor/app';
import {map} from 'rxjs/operators';
import {Platform} from '@ionic/angular';

@Component({
  selector: 'mobile-informacoes-aplicativo',
  templateUrl: './informacoes-aplicativo.page.html',
  styleUrls: ['./informacoes-aplicativo.page.scss'],
  standalone: false
})
export class InformacoesAplicativoPage implements OnInit {
  info$!: Observable<any>;

  constructor(
    private platform: Platform
  ) {

  }

  ngOnInit() {
    if (this.platform.is('hybrid')) {
      this.info$ = forkJoin({
        deviceInfo: fromPromise(Device.getInfo()),
        deviceId: fromPromise(Device.getId()),
        appInfo: fromPromise(App.getInfo()),
      }).pipe(
        map(x => {
          return {
            deviceId: x.deviceId.identifier,
            platformName: x.deviceInfo.platform,
            model: x.deviceInfo.model,
            version: x.appInfo.version,
            manufacturer: x.deviceInfo.manufacturer
          }
        })
      );
    }
  }
}
