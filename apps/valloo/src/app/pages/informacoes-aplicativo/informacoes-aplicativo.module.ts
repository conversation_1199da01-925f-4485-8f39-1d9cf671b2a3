import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {InformacoesAplicativoPageRoutingModule} from './informacoes-aplicativo-routing.module';
import {InformacoesAplicativoPage} from './informacoes-aplicativo.page';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    InformacoesAplicativoPageRoutingModule,
    ReactiveFormsModule,
    TitleToolbarModule
  ],
  declarations: [InformacoesAplicativoPage]
})
export class InformacoesAplicativoPageModule {
}
