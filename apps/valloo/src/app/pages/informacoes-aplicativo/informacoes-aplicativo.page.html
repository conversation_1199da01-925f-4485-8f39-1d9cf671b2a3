<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar>Informações do aplicativo</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content class="content-default">
  <ng-container *ngIf="info$ | async as info; else carregando">
    <ion-list>
      <ion-item lines="full">
        <ion-label slot="start">Modelo do aparelho</ion-label>
        <ion-label slot="end">{{ info.model }}</ion-label>
      </ion-item>
      <ion-item lines="full">
        <ion-label slot="start">Fabricante</ion-label>
        <ion-label slot="end">{{ info.manufacturer }}</ion-label>
      </ion-item>
      <ion-item lines="full">
        <ion-label slot="start">Plataforma</ion-label>
        <ion-label slot="end">{{ info.platformName }}</ion-label>
      </ion-item>
      <ion-item lines="full">
        <ion-label slot="start">Vers<PERSON></ion-label>
        <ion-label slot="end">{{ info.version }}</ion-label>
      </ion-item>
      <ion-item lines="full">
        <ion-label slot="start">Device ID</ion-label>
        <ion-label slot="end">{{ info.deviceId }}</ion-label>
      </ion-item>
    </ion-list>
  </ng-container>
  <ng-template #carregando>
    <ion-list>
      <ion-item lines="full" *ngFor="let i of [1,2,3]">
        <ion-label slot="start">
          <ion-skeleton-text></ion-skeleton-text>
        </ion-label>
        <ion-label slot="end">
          <ion-skeleton-text></ion-skeleton-text>
        </ion-label>
      </ion-item>
    </ion-list>
  </ng-template>
</ion-content>
