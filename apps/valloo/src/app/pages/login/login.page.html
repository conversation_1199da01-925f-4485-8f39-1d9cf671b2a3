<ion-content [fullscreen]="true">
  <section class="background-fundo">
    <article class="img">
      <div>
        <img src="assets/images/logo-branca.png" class="logo" alt="Logo" *ngIf="showLogo">
      </div>
      <div>
        <div class="chat" *ngIf="showChat">
          <ion-button color="quartenary" class="m-0" routerLink="/ajuda">
            <ion-icon name="c-chat" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </div>
    </article>
    <article *ngIf="!usarBiometria && !habilitarBiometria" class="info">
      <mobile-titulo>Login</mobile-titulo>
      <form [formGroup]="formLogin">
        <ion-item class="login-documento" lines="none">
          <ion-input labelPlacement="stacked" placeholder="000.000.000-00"
                     [maskito]="cpfMaskOptions" [maskitoElement]="maskPredicate" type="tel" formControlName="cpf"
                     [errorText]="mensagemErro">
            <div slot="label" class="custom-label">CPF</div>
          </ion-input>
        </ion-item>
        <ion-item class="login-documento" lines="none" *ngIf="isRepresentanteLogin">
          <ion-input labelPlacement="stacked" placeholder="000.000.000-00"
                     [brmasker]="{mask: '000.000.000-00', len: 14, type: 'num'}" type="tel"
                     formControlName="documentoAcesso"
                     errorText="Informe um CPF válido">
            <div slot="label" class="custom-label">CPF do Representante</div>
          </ion-input>
        </ion-item>
        <ion-item class="login-senha" lines="none">
          <ion-input labelPlacement="stacked" placeholder="Senha" formControlName="senha"
                     errorText="Informe uma senha" [type]="verSenha ? 'text' : 'password'" maxlength="30">
            <div slot="label" class="custom-label">Senha</div>
          </ion-input>
          <ion-icon size="small" slot="end" [name]="verSenha ? 'eye-outline' : 'eye-off-outline'"
                    (click)="verSenha = !verSenha"></ion-icon>
        </ion-item>
        <div class="esqueci-senha mt-1">
          <ion-button fill="clear" class="m-0" routerLink="/esqueci-senha">
            Esqueci a senha
          </ion-button>
          <ion-img src="assets/svgs/destaque-titulo.svg" *ngIf="showTitleDots"></ion-img>
        </div>
      </form>
    </article>
    <article *ngIf="habilitarBiometria" class="habilitar-biometria">
      <div class="conteudo">
        <div class="explicacao">
          <p>Utilizar senha do celular para autenticar no aplicativo?</p>
          <img *ngIf="isFaceId" alt="" src="assets/svgs/face-id.svg" />
          <ion-icon *ngIf="!isFaceId" color="quartenary" name="finger-print-outline"></ion-icon>
        </div>
        <div class="acoes">
          <ion-button expand="block" (click)="performBiometricVerification()">
            Utilizar senha do celular
          </ion-button>
          <ion-button fill="clear" expand="block" (click)="usarForm()">
            Agora não
          </ion-button>
        </div>
      </div>
    </article>
    <article *ngIf="usuario && usarBiometria" class="biometria">
      <mobile-usuario-conta [mostrarAlterarImagem]="false" [mostrarNome]="true" [mostrarAtalhos]="true"
                            [usuario]="usuario" (irPara)="acessarPorAtalho($event)"></mobile-usuario-conta>
      <div>
        <ion-button expand="block" (click)="performBiometricVerification()">
          Entrar
        </ion-button>
        <ion-button fill="clear" expand="block" (click)="acessarOutraConta()">
          Acessar com outra conta
        </ion-button>
      </div>
    </article>
  </section>
</ion-content>
<ion-footer *ngIf="!usarBiometria && !habilitarBiometria" class="botao-logar ion-no-border">
  <ion-toolbar class="ion-no-margin ion-no-padding" color="transparent">
    <ion-button class="entrar" expand="block" (click)="entrar()">
      Entrar
    </ion-button>
    <ion-button class="ion-margin-top" expand="block" fill="outline" routerLink="/criar-conta">
      Criar conta
    </ion-button>
  </ion-toolbar>
</ion-footer>
