ion-content {

  section {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    background-blend-mode: overlay;
    width: 100%;

    .img {
      padding: 2rem 1.5rem;
      margin-top: 1rem;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      .chat {
        ion-button {
          --border-radius: 50%;
          width: 50px;
          height: 50px;

          ion-icon {
            font-size: 24px;
          }
        }
      }
    }

    .info {
      background-color: var(--ion-color-background);
      height: 100%;
      width: 100%;
      border-radius: 24px 24px 0 0;
      padding: 1.5rem 1.5rem 0 1.5rem;

      .esqueci-senha {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        ion-button {
          --color: var(--ion-color-label);
          --font-size: var(--ion-font-size-label);
          --font-weight: var(--ion-font-weight-label);
          --padding-end: 0;
          --padding-start: 0;
          --padding-bottom: 0;
          height: 30px;
        }
      }
    }

    .habilitar-biometria {
      background-color: var(--ion-color-background);
      height: 70%;
      width: 100%;
      border-radius: 24px 24px 0 0;
      padding: 1.5rem 1.5rem 0 1.5rem;

      .conteudo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        height: 100%;
        width: 100%;
      }

      .explicacao {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        p {
          color: var(--ion-color-label);
          font-size: 16px;
          font-weight: var(--ion-font-weight-label);
          text-align: center;
          margin: 0;
        }

        ion-icon {
          flex-grow: 1;
          font-size: 60px;
        }
      }

      .acoes {
        width: 100%;
        margin-bottom: 1rem;
      }
    }

    .biometria {
      background-color: var(--ion-color-background);
      height: 60%;
      width: 100%;
      border-radius: 24px 24px 0 0;
      padding: 1.5rem 1.5rem 0 1.5rem;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;

      mobile-usuario-conta {
        width: 100%;
      }

      div {
        width: 100%;
        margin-bottom: 2rem;
      }
    }
  }
}

ion-footer {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  background: var(--ion-color-background);
}
