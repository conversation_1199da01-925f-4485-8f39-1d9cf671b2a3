import { NgModule } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { LoginPageRoutingModule } from './login-routing.module';
import { LoginPage } from './login.page';
import { TituloModule, UsuarioContaModule } from '@corporativo/components';
import { DirectivesModule } from '@corporativo/shared';
import {MaskitoDirective} from '@maskito/angular';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        LoginPageRoutingModule,
        ReactiveFormsModule,
        TituloModule,
        NgOptimizedImage,
        UsuarioContaModule,
        DirectivesModule,
        MaskitoDirective
    ],
  declarations: [LoginPage]
})
export class LoginPageModule {
}
