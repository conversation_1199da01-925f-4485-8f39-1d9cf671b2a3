import {Component} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {Router} from '@angular/router';
import {MaskitoElementPredicate} from '@maskito/core';
import {cpfMask, phoneMask} from '@utils/masks.util';
import {environment} from '../../../environments/environment';
import {loading} from '../../../../../../libs/utils/loading.util';
import {SegurancaService, TipoObjetivoEnum} from '@corporativo/shared';
import {ModalController} from '@ionic/angular';
import {TextUtil} from '@utils/text.util';
import {toast} from '@utils/toast.util';
import {ModalErroComponent} from '@corporativo/modals';
import {ValidatorsApp} from '@utils/validators.util';
import {FluxoService} from '../../../../../../libs/shared/src/lib/services/fluxo.service';

@Component({
  selector: 'mobile-criar-conta',
  templateUrl: './criar-conta.page.html',
  styleUrls: ['./criar-conta.page.scss'],
  standalone: false
})
export class CriarContaPage {
  form: FormGroup;
  isPasswordStep: boolean = false;
  fluxoDados: any = {};

  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();

  constructor(private formBuilder: FormBuilder,
    private router: Router,
    private segurancaService: SegurancaService,
    private fluxoService: FluxoService,
    private modalController: ModalController
  ) {
    this.initializeForm();
  }

  initializeForm() {
    this.form = this.formBuilder.group({
      cpf: ['', Validators.compose([Validators.required, ValidatorsApp.cpf()])],
      telefone: ['', [Validators.required]],
      autorizacao: [null, [Validators.required]]
    });
  }

  async confirmar() {
    if (this.form.valid) {
      this.fluxoDados.cpf = TextUtil.removeNotDigit(this.form.get('cpf')?.value);
      this.fluxoDados.telefone = TextUtil.removeNotDigit(this.form.get('telefone')?.value);

      const request = {
        cpf: this.fluxoDados.cpf,
        telefone: this.fluxoDados.telefone,
        idInstituicao: environment.idInstituicao,
        idProcessadora: environment.idProcessadora
      }

      await loading(this.segurancaService.validarOnboardingCaf(request).subscribe({
        next: (response: any) => {
          this.fluxoDados.validacaoNecessaria = response.validacaoNecessaria;
          this.fluxoDados.dataNascimento = response.dataNascimento;

          if (response.validacaoNecessaria) {
            this.router.navigate(['/selecionar-tipo-documento'], {state: {fluxoDados: this.fluxoDados}});
            return;
          }

          if (!response.onboardRealizado) {
            this.router.navigate(['/selecionar-tipo-documento'], {state: {fluxoDados: this.fluxoDados}});
            return;
          }

          if (response.onboardRealizado && response.cafReprovado) {
            this.router.navigate(['/selecionar-tipo-documento'], {state: {fluxoDados: this.fluxoDados}});
            return;
          }

          if (response.onboardRealizado && !response.cafReprovado && response.encaminharAtendimento) {
            this.abrirModalErro();
            return;
          }

          if (response.onboardRealizado && !response.cafReprovado && !response.encaminharAtendimento) {
            this.fluxoDados.tipoObjetivo = environment.production ? 7 : TipoObjetivoEnum.SelfieOnboardingCaf;
            this.fluxoService.set({
              fluxoDados: this.fluxoDados
            });
            this.router.navigate(['/selfie-caf']);
            return;
          }
        },
        error: (error: any) => {
          const message = error.error.DTL ? error.error.DTL : error.error.msg;
          toast(message);
        }
      }));
    }
  }

  irParaTermos() {
    return this.router.navigate(['/termos-uso']);
  }

  irParaPolitica() {
    return this.router.navigate(['/politica-privacidade']);
  }

  setAutorizacao(event: CustomEvent) {
    this.form.get('autorizacao')?.setValue(event.detail.checked || null);
  }

  async abrirModalErro() {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Abrir conta',
        tituloTexto: 'Abertura de conta em análise',
        mensagem:  'Nossa equipe recebeu sua documentação e estamos validando seus dados. Essa análise será realizada em até 24 horas.<br><br> ' +
          'Entre em contato com a nossa equipe de relacionamento através dos números:',
        tituloBotao: '0800 021 7100',
        tituloBotaoSecundario: 'Ir para o início',
        iconName: 'atencao',
        corToolbar: 'none'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      this.abrirWhatsapp('08000217100');
    } else if (data.role == 'secundaria') {
      await this.router.navigate(['/login']);
    }
  }

  abrirWhatsapp(telefone: string){
    window.open(`https://api.whatsapp.com/send?phone=+55${telefone}&text=Ol%C3%A1%2C+vim+pelo+aplicativo+e+gostaria+de+solicitar+ajuda.`, '_system');
  }

  protected readonly cpfMaskOptions = cpfMask;
  protected readonly phoneMaskOptions = phoneMask;
}
