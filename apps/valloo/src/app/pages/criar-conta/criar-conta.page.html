<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="true">Abrir conta</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <article class="progress">
    <ion-progress-bar value=".20"></ion-progress-bar>
  </article>

  <article class="titulo">
    <ng-container>
      <ion-text>Para começar, precisamos validar algumas informações. Primeiro, digite seu CPF, Número do cartão e celular.</ion-text>
    </ng-container>
  </article>

  <article class="criar-conta">
    <form [formGroup]="form" (ngSubmit)="confirmar()">

      <ion-item lines="none" class="custom-input">
        <ion-input labelPlacement="stacked" label="CPF" placeholder="000.000.000-00" formControlName="cpf"
                   [maskito]="cpfMaskOptions" [maskitoElement]="maskPredicate" type="tel" errorText="Informe um CPF válido">
        </ion-input>
      </ion-item>

      <ion-item lines="none" class="custom-input">
        <ion-input labelPlacement="stacked" label="Telefone" placeholder="(00) 0 0000-0000" formControlName="telefone"
                   [maskito]="phoneMaskOptions" [maskitoElement]="maskPredicate" type="tel" errorText="Informe um telefone válido">
        </ion-input>
      </ion-item>

      <ion-item class="termos" lines="none" [button]="false">
        <ion-checkbox slot="start" (ionChange)="setAutorizacao($event)" formControlName="autorizacao"></ion-checkbox>
        <ion-label>
          Li e estou de acordo com o <u (click)="irParaTermos()">termo de uso</u>
          e <u (click)="irParaPolitica()">política de privacidade.</u>
        </ion-label>
      </ion-item>
    </form>
  </article>
</ion-content>

<ion-footer class="ion-no-border">
  <ion-button class="btn ion-margin-bottom" expand="block" type="button" [disabled]="form.invalid" (click)="confirmar()">Confirmar</ion-button>
  <ion-button *ngIf="!isPasswordStep" fill="outline" expand="block" type="button" [routerLink]="'/login'">Ir para o início</ion-button>
</ion-footer>
