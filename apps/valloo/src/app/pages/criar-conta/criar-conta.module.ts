import {NgModule} from '@angular/core';
import {CommonModule, NgOptimizedImage} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {SkeletonModule, TitleToolbarModule} from '@corporativo/components';
import {MaskitoDirective} from '@maskito/angular';
import {CriarContaPage} from './criar-conta.page';
import {CriarContaRoutingModule} from './criar-conta-routing.module';

@NgModule({
  declarations: [CriarContaPage],
    imports: [
      CommonModule,
      FormsModule,
      IonicModule,
      CriarContaRoutingModule,
      NgOptimizedImage,
      ReactiveFormsModule,
      MaskitoDirective,
      CriarContaRoutingModule,
      TitleToolbarModule,
      MaskitoDirective,
      SkeletonModule
    ]
})
export class CriarContaModule {
}
