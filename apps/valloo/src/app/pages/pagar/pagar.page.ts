import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController, Platform } from '@ionic/angular';
import { loading } from '@utils/loading.util';
import {
  CapacitorBarcodeScanner,
  CapacitorBarcodeScannerAndroidScanningLibrary,
  CapacitorBarcodeScannerCameraDirection,
  CapacitorBarcodeScannerScanOrientation,
  CapacitorBarcodeScannerTypeHint
} from '@capacitor/barcode-scanner';
import * as BoletoUtil from '@mrmgomes/boleto-utils';
import { ScreenOrientation } from '@capacitor/screen-orientation';
import {AuthService, Conta, Credencial, PagamentoService, Usuario} from '@corporativo/shared';
import {ModalAtencaoComponent} from '@corporativo/modals';

@Component({
  selector: 'app-pagar',
  templateUrl: './pagar.page.html',
  styleUrls: ['./pagar.page.scss'],
  standalone: false
})
export class PagarPage implements OnInit {
  usuario!: Usuario;
  idConta!: number;
  idCredencial!: number;
  contas!: Conta[];
  conta!: Conta;

  constructor(
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private platform: Platform,
    private pagamentoService: PagamentoService,
    private modalController: ModalController,
    private router: Router
  ) {
    this.usuario = this.authService.getUser();
  }

  async ngOnInit() {
    if (!this.verificarHorarioFuncionamento()) {
      return this.modalFuncionamento();
    }
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      const credenciais = this.getCredenciais(this.usuario.credenciais);
      for (const credencial of credenciais) {
        const conta = credencial.contas.find((c: any) => c.idConta == this.idConta);
        if (conta) {
          this.idCredencial = credencial.idCredencial;
          this.contas = credencial.contas.filter((x: any) => x.idConta == this.idConta);
          this.conta = conta;
          break;
        }
      }
    }
  }

  private getCredenciais(credenciais: Credencial[]) {
    let cartoes: Credencial[] = [];
    for (const credencial of credenciais) {
      cartoes = credenciais.filter((x: Credencial) => x.idConta == credencial.idConta);
      if (cartoes.length >= 2) {
        const cartaoVirtual = cartoes.find(x => x.virtual);
        if (cartaoVirtual) {
          cartaoVirtual.removerListaInicial = true;
        }
      }
    }
    return credenciais.filter((r: Credencial) => !r.removerListaInicial);
  }

  async ionViewDidEnter() {
    if (this.platform.is('hybrid')) {
      // Bloquear no modo paisagem
      await ScreenOrientation.unlock();
    }
  }

  async ionViewDidLeave() {
    if (this.platform.is('hybrid')) {
      // Bloquear no modo paisagem
      await ScreenOrientation.lock({ orientation: 'portrait' });
    }
  }

  async lerCodigoBarras() {
    if (!this.platform.is('hybrid')) {
      await this.consultar('858400000159708001402170242203048901727320001527');
    } else {
      try {
        let linhaDigitavel = '';
        const options: any = {
          hint: CapacitorBarcodeScannerTypeHint.PDF_417,
          scanOrientation: CapacitorBarcodeScannerScanOrientation.LANDSCAPE,
          cameraDirection: CapacitorBarcodeScannerCameraDirection.BACK,
          scanInstructions: 'Posicione o código de barras na marcação',
          android: {
            scanningLibrary: CapacitorBarcodeScannerAndroidScanningLibrary.ZXING
          }
        };

        const result = await CapacitorBarcodeScanner.scanBarcode(options);
        const validacao = await BoletoUtil.validarBoleto(result.ScanResult);
        if (validacao.sucesso) {
          linhaDigitavel = validacao.linhaDigitavel;
        } else {
          await this.apresentarAtencao(validacao.mensagem);
          return;
        }
        await this.consultar(linhaDigitavel);
      } catch (err: any) {
        if (err.errorMessage != 'Scanning cancelled' && err.message != 'Couldn\'t scan because the process was cancelled.') {
          await this.apresentarAtencao(err);
        }
      }
    }
  };

  async consultar(codigo: string) {
    const data = {
      linhaDigitavel: codigo,
      idConta: this.idConta
    };
    await loading(
      this.pagamentoService.consultarTitulo(data).subscribe((result: any) => {
        if (result.codigoRetorno == 0) {
          this.router.navigate([`pagar-contas/${this.idConta}/informar-valor`], { state: { data: result } });
        } else if (result.codigoRetorno == -1) {
          this.apresentarAtencao(result.mensagemErro);
        } else if (result.codigoRetorno == -99) {
          this.apresentarAtencao();
        }
      })
    );
  }

  async apresentarAtencao(mensagem?: string) {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Atenção!',
        mensagem: mensagem || 'Não conseguimos reconhecer o código do seu boleto. Por favor verifique se está correto e tente novamente.',
        tituloBotaoPrimario: 'Fechar'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  verificarHorarioFuncionamento(): boolean {
    const agora = new Date();
    const diaSemana = agora.getDay();

    if (diaSemana >= 1 && diaSemana <= 5) {
      const inicio = new Date();
      inicio.setHours(8, 0, 0);
      const fim = new Date();
      fim.setHours(21, 0, 0);
      return agora >= inicio && agora <= fim;
    }

    return false;
  }

  async modalFuncionamento() {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Horário de funcionamento',
        mensagem: 'O horário de funcionamento é de segunda a sexta-feira, das 8h às 21:00h (Horário de Brasília).'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.router.navigate(['inicio']);
    }
  }

}
