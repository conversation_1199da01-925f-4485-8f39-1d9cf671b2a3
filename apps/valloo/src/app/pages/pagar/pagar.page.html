<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar>Pagar contas</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <section>
    <div class="subsection ion-margin-bottom">
      <mobile-titulo-secao><PERSON><PERSON></mobile-titulo-secao>
    </div>
    <mobile-cartoes [idCredencial]="idCredencial" [showNext]="false" [refresh]="true"></mobile-cartoes>
  </section>
  <section *ngIf="contas">
    <mobile-saldo-simples [contas]="contas" [idConta]="idConta" [showNext]="false"></mobile-saldo-simples>
  </section>
  <section class="pagamentos">
    <mobile-titulo-secao>Formas de pagamento</mobile-titulo-secao>
    <ion-item lines="none" button detail="false" class="ion-margin-top" (click)="lerCodigoBarras()">
      <ion-icon slot="start" name="barcode"></ion-icon>
      <ion-label>Ler código de barras</ion-label>
    </ion-item>
    <ion-item lines="none" button detail="false" routerLink="digitar-codigo">
      <ion-icon slot="start" name="digitar-codigo-barras"></ion-icon>
      <ion-label>Digitar código de barras</ion-label>
    </ion-item>
  </section>
  <section>
    <mobile-titulo-secao>Configurações de pagamento</mobile-titulo-secao>
  </section>
  <section class="limite">
    <mobile-titulo-secao>Meus limites</mobile-titulo-secao>
    <ion-item detail button detail-icon="chevron-forward-outline" lines="none" routerLink="limites">
      <ion-label>Altere seus limites, você pode alterar seu limite por período ou por pagamento.</ion-label>
    </ion-item>
  </section>
</ion-content>
