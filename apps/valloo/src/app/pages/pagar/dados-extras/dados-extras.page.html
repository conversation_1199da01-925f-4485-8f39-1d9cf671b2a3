<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'pagar-contas/'+idConta">Dados extras</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section *ngIf="contas">
    <mobile-saldo-simples [contas]="contas" [idConta]="idConta" [showNext]="false"></mobile-saldo-simples>
  </section>
  <section>
    <mobile-titulo-secao>Informações complementares</mobile-titulo-secao>
  </section>
  <section>
    <form [formGroup]="formFgts" *ngIf="convenio === 'FGTS'">
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Inscrição"
                   type="tel" formControlName="inscricao" errorText="Informe um valor válido"
                   [brmasker]="{person: true}" counter>
          <div slot="label" class="custom-label">Inscrição</div>
        </ion-input>
      </ion-item>
    </form>
    <form [formGroup]="formDarf" *ngIf="convenio === 'DARF'">
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Nome"
                   type="text" formControlName="nomeDoContribuinte" errorText="Campo obrigatório">
          <div slot="label" class="custom-label">Nome do contribuinte</div>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="00/00/0000"
                   [brmasker]="{mask: '00/00/0000', type:'num', len:10}"
                   type="text" formControlName="periodoApuracao" errorText="Informe um valor válido">
          <div slot="label" class="custom-label">Período de apuração</div>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="00/00/0000"
                   [brmasker]="{mask: '00/00/0000', type:'num', len:10}"
                   type="text" formControlName="dataVencimento" errorText="Informe um valor válido">
          <div slot="label" class="custom-label">Data de vencimento</div>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="CPF ou CNPJ" [brmasker]="{person: true}"
                   type="text" formControlName="cpfcnpj" errorText="Informe um valor válido">
          <div slot="label" class="custom-label">CPF ou CNPJ</div>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Código da receita" [brmasker]="{len:4}"
                   type="num" formControlName="codigoReceita" errorText="Campo obrigatório">
          <div slot="label" class="custom-label">Código da receita</div>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Número de referência" [brmasker]="{type:'num', len:10}"
                   type="tel" formControlName="numeroDeReferencia" errorText="&nbsp;">
          <div slot="label" class="custom-label">Número de referência</div>
        </ion-input>
      </ion-item>
    </form>
  </section>
</ion-content>
<ion-footer class="ion-no-border">
  <ion-button class="btn" *ngIf="convenio === 'DARF'" expand="block" (click)="continuar()" [disabled]="formDarf.invalid">
    Continuar
  </ion-button>
  <ion-button class="btn" *ngIf="convenio === 'FGTS'" expand="block" (click)="continuar()" [disabled]="formFgts.invalid">
    Continuar
  </ion-button>
</ion-footer>
