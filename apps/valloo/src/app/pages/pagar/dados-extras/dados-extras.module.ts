import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DadosExtrasRoutingModule} from './dados-extras-routing.module';
import {DadosExtrasPage} from './dados-extras.page';
import {IonicModule} from '@ionic/angular';
import {ReactiveFormsModule} from '@angular/forms';
import {MaskitoDirective} from '@maskito/angular';
import {TitleToolbarModule, TituloSecaoModule} from '@corporativo/components';
import {DirectivesModule} from '@corporativo/shared';
import {SaldoSimplesModule} from '@corporativo/saldo';

@NgModule({
  declarations: [
    DadosExtrasPage
  ],
  imports: [
    CommonModule,
    DadosExtrasRoutingModule,
    IonicModule,
    ReactiveFormsModule,
    MaskitoDirective,
    TitleToolbarModule,
    TituloSecaoModule,
    DirectivesModule,
    SaldoSimplesModule
  ]
})
export class DadosExtrasModule {
}
