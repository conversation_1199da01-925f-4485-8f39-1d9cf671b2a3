import { Component } from '@angular/core';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { ValidatorsApp } from '@utils/validators.util';
import { dateBrToEn } from '@utils/date.util';
import { TextUtil } from '@utils/text.util';
import { loading } from '@utils/loading.util';
import { format, parseISO } from 'date-fns';
import {AuthService, Conta, Credencial, PagamentoService, Usuario} from '@corporativo/shared';
import {ModalAtencaoComponent} from '@corporativo/modals';

@Component({
  selector: 'vlo-dados-extras',
  templateUrl: './dados-extras.page.html',
  styleUrls: ['./dados-extras.page.scss'],
  standalone: false
})
export class DadosExtrasPage {
  usuario!: Usuario;
  formFgts = new FormGroup({
    inscricao: new FormControl('', [Validators.required])
  });
  formDarf = new FormGroup({
    nomeDoContribuinte: new FormControl('', [Validators.required]),
    periodoApuracao: new FormControl('', [Validators.required, ValidatorsApp.dateBr]),
    dataVencimento: new FormControl('', [Validators.required, ValidatorsApp.dateBr]),
    cpfcnpj: new FormControl('', [Validators.required, ValidatorsApp.cpfCnpj()]),
    codigoReceita: new FormControl('', [Validators.required]),
    numeroDeReferencia: new FormControl('')
  });
  idConta!: number;
  credencial!: Credencial | undefined;
  conta: Conta;
  idCredencial?: number;
  contas!: Conta[] | undefined;
  linhaDigitavel: string;
  convenio: string;

  constructor(
    private router: Router,
    private modalController: ModalController,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private pagamentoService: PagamentoService) {
    this.usuario = this.authService.getUser();
    this.linhaDigitavel = this.router.getCurrentNavigation()?.extras?.state?.['linhaDigitavel'];
  }

  async ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c: any) => c.idConta == this.idConta);
        return conta ? x : undefined;
      });
      this.idCredencial = this.credencial?.idCredencial;
      const conta = this.credencial?.contas.find((x: any) => x.idConta == this.idConta);
      if (conta) {
        this.contas = this.credencial?.contas.filter((x: any) => x.idConta == this.idConta);
        this.conta = conta;
      }
    }
    this.consultarConvenio();
  }

  consultarConvenio() {
    this.pagamentoService.consultarConvenio(this.linhaDigitavel).subscribe((x: any) => {
      this.convenio = x.msg;
    });
  }

  async continuar() {
    let dadosDarf: any;
    let dadosFgts: any;
    if (this.convenio == 'DARF') {
      const rawValue: any = this.formDarf.getRawValue();
      dadosDarf = {
        codigoDeBarrasOuLinhaDigitavel: this.linhaDigitavel,
        cpfcnpj: TextUtil.removeNotDigit(rawValue.cpfcnpj),
        codigoReceitaFederal_Id: rawValue.codigoReceita,
        identificacaoDaOperacaoNoExtrato: 'Tributo DARF COM BARRA',
        nome: rawValue.nomeDoContribuinte,
        numeroReferencia: rawValue.numeroDeReferencia,
        periodoApuracao: format(parseISO(dateBrToEn(rawValue.periodoApuracao)), 'yyyy-MM-dd'),
        dataVencimento: format(parseISO(dateBrToEn(rawValue.dataVencimento)), 'yyyy-MM-dd')
      };
    } else {
      const rawValue: any = this.formFgts.getRawValue();
      dadosFgts = {
        inscricao: TextUtil.removeNotDigit(rawValue.inscricao)
      };
    }

    const data = {
      linhaDigitavel: this.linhaDigitavel,
      idConta: this.idConta,
      tributoDarf: dadosDarf
    };

    await loading(
      this.pagamentoService.consultarTitulo(data).subscribe((result: any) => {
        if (result.codigoRetorno == 0) {
          if (this.convenio == 'DARF') {
            dadosDarf.valorTotal = result.valorPagamentoAtualizado;
            dadosDarf.valorMulta = result.valorMultaCalculado;
            dadosDarf.valorJuros = result.valorJurosCalculado;
          } else {
            dadosFgts.valorDoPagamento = result.valorPagamentoAtualizado;
            dadosFgts.codigoDeBarrasOuLinhaDigitavel = this.linhaDigitavel;
          }
          const navigationExtras: NavigationExtras = {
            state: {
              data: result,
              tributoDARF: dadosDarf,
              tributoFGTS: dadosFgts
            }
          };
          this.router.navigate([`pagar-contas/${this.idConta}/informar-valor`], navigationExtras);
        } else if (result.codigoRetorno == -1) { // erro previsto
          this.apresentarAtencao(result.mensagemErro);
        } else if (result.codigoRetorno == -99) { // erro imprevisto
          this.apresentarAtencao();
        }
      })
    );
  }

  async apresentarAtencao(mensagem?: string) {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Atenção!',
        mensagem: mensagem || 'Não conseguimos reconhecer o código do seu boleto. Por favor verifique se está correto e digite novamente.',
        tituloBotaoPrimario: 'Fechar'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

}
