import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {LimitesPage} from './limites.page';

const routes: Routes = [
  {
    path: '',
    component: LimitesPage
  },
  {
    path: 'alterar',
    loadChildren: () => import('./alterar/alterar.module').then(m => m.AlterarModule)
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LimitesRoutingModule {
}
