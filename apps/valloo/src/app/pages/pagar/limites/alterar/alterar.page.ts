import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ModalController } from '@ionic/angular';
import { loading } from '@utils/loading.util';
import { CurrencyPipe } from '@angular/common';
import { notEqualValidator } from '@utils/validators.util';
import {
  AuthService, Conta,
  Credencial,
  environment, LimitePagamento,
  LimiteService,
  MetodoSegurancaEnum,
  TipoTransacaoEnum, Usuario
} from '@corporativo/shared';
import {
  ModalAtivarCartaoComponent,
  ModalErroComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent
} from '@corporativo/modals';

@Component({
  selector: 'vlo-alterar',
  templateUrl: './alterar.page.html',
  styleUrls: ['./alterar.page.scss'],
  standalone: false
})
export class AlterarPage {
  subtitulo = '';
  label = '';
  tipo!: string;
  limites!: LimitePagamento;
  explicacao!: string;
  limite: any;
  usuario!: Usuario;
  formLimite = new FormGroup({
    valor: new FormControl(0, Validators.required),
    valorFormatado: new FormControl('', Validators.required)
  });
  idConta!: number;
  credencial!: Credencial | undefined;
  conta: Conta;

  constructor(
    private router: Router,
    private modalController: ModalController,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private limiteService: LimiteService) {
    this.usuario = this.authService.getUser();
    const data: any = router.getCurrentNavigation()?.extras?.state;
    if (data) {
      this.limites = data.limite;
      this.tipo = data.tipo;
      const currencyPipe = new CurrencyPipe('pt-BR');
      switch (this.tipo) {
        case 'pagamento': {
          this.subtitulo = 'pagamento';
          this.label = 'Por pagamento';
          this.explicacao = 'Esse é o valor máximo que você pode fazer por pagamento, o boleto não pode ultrapassar o limite selecionado.';
          this.limite = {
            atual: this.limites.valorLimiteUnitarioDia,
            maximo: this.limites.valorLimiteSomaDia,
            minimo: 0.01,
            tipo: 0
          };
          this.formLimite.get('valor')?.setValidators([Validators.min(0.01), Validators.max(this.limite.maximo), notEqualValidator(this.limite.atual)]);
          break;
        }
        case 'periodo': {
          this.subtitulo = 'período';
          this.label = 'Por período';
          this.explicacao = 'Esse é o valor máximo que você pode fazer por período, a soma dos pagamentos não pode ultrapassar o limite do período.';
          this.limite = {
            atual: this.limites.valorLimiteSomaDia,
            minimo: 0.01,
            tipo: 1
          };
          this.formLimite.get('valor')?.setValidators([Validators.min(0.01), notEqualValidator(this.limite.atual)]);
          break;
        }
      }
      this.formLimite.get('valorFormatado')?.setValue(currencyPipe.transform(this.limite.atual));
      this.formLimite.get('valorFormatado')?.valueChanges.subscribe(x => {
        if (!x) {
          return;
        }
        const valor = +x
          .replace('R$ ', '')
          .replace(/\./g, '')
          .replace(/,/g, '.');
        this.formLimite.get('valor')?.setValue(valor);
      });
    }
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c: any) => c.idConta == this.idConta);
        return conta ? x : undefined;
      });
      const conta = this.credencial?.contas.find((x: any) => x.idConta == this.idConta);
      if (conta) {
        this.conta = conta;
      }
    }
  }

  async alterar() {
    const cartaoAtivo = await this.verificarCartaoAtivo();
    if (!cartaoAtivo) {
      return;
    }
    const valido = await this.verificarSeguranca();
    if (valido) {
      await this.solicitarAlteracao();
    }
  }

  async solicitarAlteracao() {
    const valor = this.formLimite.getRawValue().valor;
    const dados: any = {};
    dados.idConta = this.idConta;
    dados.valorAtual = this.limite.atual;
    dados.valorSolicitado = valor;
    dados.idProdutoInstituicao = this.conta.idProdutoInstituicao;
    dados.nomeCampoLimite = this.limite.tipo;
    dados.idTipoTransacao = TipoTransacaoEnum.Boleto;
    await loading(
      this.limiteService.solicitarAlteracao(dados).subscribe({
        next: async () => {
          await this.apresentarSucesso();
        }, error: async (resposta: any) => {
          await this.apresentarErro(resposta?.msg);
        }
      })
    );
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c: any) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return credencialAtiva;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial
      }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async verificarSeguranca() {
    if (this.credencial?.metodoSegurancaTransacao == MetodoSegurancaEnum.NaoVerificar) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        metodoSegurancaTransacao: this.credencial?.metodoSegurancaTransacao,
        idCredencial: this.credencial?.idCredencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Solicitação enviada',
        mensagem: 'A solicitação foi enviada para análise, os pedidos de alteração dos limites têm um prazo entre 24 e 48 horas para serem analisados, e você será comunicado pelo aplicativo da ' + environment.appName + '.',
        urlRetorno: `pagar-contas/${this.idConta}/limites`
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Ocoreu um erro!',
        mensagem: message || 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.role == 'primaria') {
      return;
    }
    if (data && data.role == 'secundaria') {
      return this.router.navigate([`pagar-contas/${this.idConta}/limites`]);
    }
  }

}
