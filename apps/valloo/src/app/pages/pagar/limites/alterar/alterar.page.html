<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'pagar-contas/'+idConta+'/limites'">Limite por {{subtitulo}}</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <h6>{{explicacao}}</h6>
    <form [formGroup]="formLimite">
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="R$ 00,00" type="tel" formControlName="valorFormatado" maxlength="14"
                   [brmasker]="{money: true, thousand: '.',  decimalCaracter: ',', decimal: 2}" errorText="Informe um valor válido">
          <div slot="label" class="custom-label">{{label}}</div>
          <ion-text slot="start">R$</ion-text>
        </ion-input>
      </ion-item>
    </form>
    <article class="limite">
      <div>
        <p><b>Limite atual:</b></p>
        <p>{{limite.atual | currency:'BRL'}}</p>
      </div>
      <div *ngIf="tipo === 'pagamento'">
        <p><b>Valor máximo:</b></p>
        <p>{{limite.maximo | currency:'BRL'}}</p>
      </div>
      <div>
        <p><b>Valor mínimo:</b></p>
        <p>{{limite.minimo | currency:'BRL'}}</p>
      </div>
    </article>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="alterar()" [disabled]="formLimite.invalid">Alterar</ion-button>
</ion-footer>
