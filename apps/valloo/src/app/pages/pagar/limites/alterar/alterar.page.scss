ion-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
}

h6 {
  color: var(--ion-color-gray-600);
  font-size: var(--ion-font-size-label);
  font-weight: 400;
  margin: 1.5rem 0;
}

.limite {
  margin-top: 1rem;

  div {
    display: flex;
    align-items: center;
    justify-content: space-between;

    p {
      color: var(--ion-color-primary);
      font-weight: 500;
      margin: 0 0 1rem 0;

      b {
        color: var(--ion-color-gray-50);
        font-weight: 500;
      }
    }
  }
}
