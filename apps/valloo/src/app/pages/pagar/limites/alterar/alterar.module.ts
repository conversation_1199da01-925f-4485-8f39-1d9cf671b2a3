import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {AlterarRoutingModule} from './alterar-routing.module';
import {IonicModule} from '@ionic/angular';
import {ReactiveFormsModule} from '@angular/forms';
import {MaskitoDirective} from '@maskito/angular';
import {AlterarPage} from './alterar.page';
import {TitleToolbarModule} from '@corporativo/components';
import {DirectivesModule} from '@corporativo/shared';

@NgModule({
  declarations: [
    AlterarPage
  ],
  imports: [
    CommonModule,
    AlterarRoutingModule,
    IonicModule,
    ReactiveFormsModule,
    MaskitoDirective,
    TitleToolbarModule,
    DirectivesModule
  ]
})
export class AlterarModule {
}
