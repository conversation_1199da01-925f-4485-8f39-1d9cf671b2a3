import {Component} from '@angular/core';
import {BehaviorSubject, Observable} from 'rxjs';
import {ActivatedRoute, Router} from '@angular/router';
import {AuthService, Conta, LimitePagamento, LimiteService, TipoTransacaoEnum} from '@corporativo/shared';

@Component({
  selector: 'mobile-limites',
  templateUrl: './limites.page.html',
  styleUrl: './limites.page.scss',
  standalone: false
})
export class LimitesPage {
  limite$!: Observable<LimitePagamento>;
  usuario!: any;
  idConta!: number;
  conta: Conta;
  isLoading$ = new BehaviorSubject<boolean>(true);

  constructor(
    private router: Router,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private limiteService: LimiteService) {
  }

  ionViewWillEnter() {
    this.usuario = this.authService.getUser();
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      for (const credencial of this.usuario.credenciais) {
        this.conta = credencial.contas.find((c: any) => c.idConta == this.idConta);
        if (this.conta) {
          this.limite$ = this.limiteService.buscarAtual(
            this.idConta,
            this.conta.idProdutoInstituicao,
            TipoTransacaoEnum.Boleto
          );
          this.limite$.subscribe(() => this.isLoading$.next(false));
        }
      }
    }
  }

  alterar(tipo: string, limite: LimitePagamento) {
    this.router.navigate([`pagar-contas/${this.idConta}/limites/alterar`], {state: {limite, tipo}})
  }
}
