<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar>Meus limites</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <ng-container *ngIf="(isLoading$ | async) === false; else carregando">
    <ng-container *ngIf="limite$ | async as limite; else semLimites">
      <section class="limites">
        <mobile-titulo-secao>Por pagamento</mobile-titulo-secao>
        <article class="limite">
          <div>
            <p><b>Limite</b></p>
            <p>{{ limite.valorLimiteUnitarioDia | currency:'BRL' }}</p>
          </div>
          <ng-container *ngIf="limite.statusLimiteUnitarioDia; else alterarPagamento">
            <ion-chip color="primary">Em análise</ion-chip>
          </ng-container>
          <ng-template #alterarPagamento>
            <ion-button color="secondary" size="small" (click)="alterar('pagamento', limite)">
              Alterar
            </ion-button>
          </ng-template>
        </article>
      </section>
      <section class="limites">
        <mobile-titulo-secao>Por período</mobile-titulo-secao>
        <article class="limite">
          <div>
            <p><b>Limite</b></p>
            <p>{{ limite.valorLimiteSomaDia | currency:'BRL' }}</p>
          </div>
          <ng-container *ngIf="limite.statusLimiteSomaDia; else alterarPeriodo">
            <ion-chip color="primary">Em análise</ion-chip>
          </ng-container>
          <ng-template #alterarPeriodo>
            <ion-button color="secondary" size="small" (click)="alterar('periodo', limite)">
              Alterar
            </ion-button>
          </ng-template>
        </article>
      </section>
    </ng-container>
    <ng-template #semLimites>
      <mobile-estado-vazio [iconName]="'sino'" [message]="'Você ainda não possui limites!'"></mobile-estado-vazio>
    </ng-template>
  </ng-container>
  <ng-template #carregando>
    <section>
      <mobile-titulo-secao>Por pagamento</mobile-titulo-secao>
      <article class="limite">
        <div style="width: 100%">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
        <div style="width: 100%" class="ion-margin-start">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
      </article>
    </section>
    <section>
      <mobile-titulo-secao>Por período</mobile-titulo-secao>
      <article class="limite">
        <div style="width: 100%">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
        <div style="width: 100%" class="ion-margin-start">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
      </article>
    </section>
  </ng-template>
</ion-content>
