import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {DigitarCodigoRoutingModule} from './digitar-codigo-routing.module';
import {DigitarCodigoPage} from './digitar-codigo.page';
import {IonicModule} from '@ionic/angular';
import {ReactiveFormsModule} from '@angular/forms';
import {MaskitoDirective} from '@maskito/angular';
import {TitleToolbarModule, TituloSecaoModule} from '@corporativo/components';
import {SaldoSimplesModule} from '@corporativo/saldo';

@NgModule({
  declarations: [
    DigitarCodigoPage
  ],
  imports: [
    CommonModule,
    DigitarCodigoRoutingModule,
    IonicModule,
    ReactiveFormsModule,
    MaskitoDirective,
    TitleToolbarModule,
    TituloSecaoModule,
    SaldoSimplesModule
  ]
})
export class DigitarCodigoModule {
}
