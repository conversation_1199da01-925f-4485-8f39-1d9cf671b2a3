import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { IonTextarea, ModalController } from '@ionic/angular';
import { numberMask } from '@utils/masks.util';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { loading } from '@utils/loading.util';
import { Clipboard } from '@capacitor/clipboard';
import { TextUtil } from '@utils/text.util';
import { lastValueFrom, map } from 'rxjs';
import {AuthService, Conta, Credencial, PagamentoService, TipoTransacaoEnum, Usuario} from '@corporativo/shared';
import {ModalAtencaoComponent} from '@corporativo/modals';

@Component({
  selector: 'vlo-digitar-codigo',
  templateUrl: './digitar-codigo.page.html',
  styleUrls: ['./digitar-codigo.page.scss'],
  standalone: false
})
export class DigitarCodigoPage implements OnInit {
  usuario!: Usuario;
  formPagamento = new FormGroup({
    codigo: new FormControl('', [Validators.required, Validators.minLength(47)])
  });
  idConta!: number;
  credencial!: Credencial | undefined;
  conta: Conta;
  idCredencial?: number;
  contas!: Conta[] | undefined;
  numberMaskOptions: MaskitoOptions = numberMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();
  maxLength = 48;
  @ViewChild('fieldBarcode', { static: false }) fieldBarcode!: IonTextarea;
  contrato: string;

  constructor(
    private router: Router,
    private modalController: ModalController,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private pagamentoService: PagamentoService) {
    this.usuario = this.authService.getUser();
  }

  async ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c: any) => c.idConta == this.idConta);
        return conta ? x : undefined;
      });
      this.idCredencial = this.credencial?.idCredencial;
      const conta = this.credencial?.contas.find((x: any) => x.idConta == this.idConta);
      if (conta) {
        this.contas = this.credencial?.contas.filter((x: any) => x.idConta == this.idConta);
        this.conta = conta;
      }
    }
    this.formPagamento.get('codigo')?.valueChanges.subscribe(valor => {
      if (!valor) {
        return;
      }
      if (valor.substring(0, 1) === '8') {
        this.maxLength = 48;
      } else {
        this.maxLength = 47;
      }
    });
    const textAreaElement = await this.fieldBarcode.getInputElement();
    textAreaElement.addEventListener('paste', this.handlePaste.bind(this));
  }

  async ngOnInit() {
    this.contrato = await lastValueFrom(this.pagamentoService.buscarContratoAtivo(TipoTransacaoEnum.Boleto)
      .pipe(map((x: any) => x.msg))
    );
  }

  async handlePaste(event: ClipboardEvent) {
    event.preventDefault();
    const area = await Clipboard.read();
    if (area.type != 'text/plain') {
      return;
    }
    let value = TextUtil.removeNotDigit(area.value.trim());
    if (value.length > 48) {
      value = value.substring(0, 48);
    }
    this.formPagamento.get('codigo')?.setValue(value);
  }

  async continuar() {
    const codigo: string = this.formPagamento.getRawValue().codigo || '';
    const data = {
      linhaDigitavel: codigo,
      idConta: this.idConta
    };

    if (this.contrato == 'Rendimento' && codigo.split('')[1] == '5') {
      await this.router.navigate([`/pagar-contas/${this.idConta}/dados-extras`], { state: { linhaDigitavel: codigo } });
      return;
    }

    await loading(
      this.pagamentoService.consultarTitulo(data).subscribe((result: any) => {
        if (result.codigoRetorno == 0) {
          this.router.navigate([`pagar-contas/${this.idConta}/informar-valor`], { state: { data: result } });
        } else if (result.codigoRetorno == -1) { // erro previsto
          this.apresentarAtencao(result.mensagemErro);
        } else if (result.codigoRetorno == -99) { // erro imprevisto
          this.apresentarAtencao();
        }
      })
    );
  }

  async apresentarAtencao(mensagem?: string) {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Atenção!',
        mensagem: mensagem || 'Não conseguimos reconhecer o código do seu boleto. Por favor verifique se está correto e digite novamente.',
        tituloBotaoPrimario: 'Fechar'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

}
