<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'pagar-contas/'+idConta">Digitar código</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section *ngIf="contas">
    <mobile-saldo-simples [contas]="contas" [idConta]="idConta" [showNext]="false"></mobile-saldo-simples>
  </section>
  <section>
    <mobile-titulo-secao>Informações de pagamento</mobile-titulo-secao>
  </section>
  <section>
    <form [formGroup]="formPagamento">
      <ion-item lines="none">
        <ion-textarea labelPlacement="stacked" placeholder="000000000000 000000000000 000000000000 000000000000"
                      type="tel" formControlName="codigo" [maxlength]="maxLength" errorText="Informe um valor válido"
                      [maskito]="numberMaskOptions" [maskitoElement]="maskPredicate" counter #fieldBarcode>
          <div slot="label" class="custom-label">Código de barras</div>
        </ion-textarea>
      </ion-item>
    </form>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="continuar()" [disabled]="formPagamento.invalid">Continuar</ion-button>
</ion-footer>
