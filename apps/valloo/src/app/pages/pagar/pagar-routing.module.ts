import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PagarPage } from './pagar.page';

const routes: Routes = [
  {
    path: '',
    component: PagarPage
  },
  {
    path: 'limites',
    loadChildren: () => import('./limites/limites.module').then(m => m.LimitesModule)
  },
  {
    path: 'digitar-codigo',
    loadChildren: () => import('./digitar-codigo/digitar-codigo.module').then(m => m.DigitarCodigoModule)
  },
  {
    path: 'informar-valor',
    loadChildren: () => import('./informar-valor/informar-valor.module').then(m => m.InformarValorModule)
  },
  {
    path: 'dados-extras',
    loadChildren: () => import('./dados-extras/dados-extras.module').then(m => m.DadosExtrasModule)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PagarPageRoutingModule {
}
