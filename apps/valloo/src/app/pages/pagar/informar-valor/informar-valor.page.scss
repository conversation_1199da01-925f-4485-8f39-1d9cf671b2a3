ion-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
  padding-top: 1rem;
  background-color: var(--ion-color-background);
}

section {
  margin-bottom: 1.5rem;
}

article {
  div {
    margin-bottom: 1rem;

    p {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      line-height: 19px;

      b {
        font-weight: 600;
        color: var(--ion-color-gray-50);
      }
    }

    p:last-child {
      margin-top: 8px;
    }
  }
}

form {
  margin-bottom: 1rem;
  margin-top: -1rem;
}
