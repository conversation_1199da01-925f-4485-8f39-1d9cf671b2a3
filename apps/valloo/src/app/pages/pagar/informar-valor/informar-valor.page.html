<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'pagar-contas/'+idConta">Pagar conta</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section *ngIf="contas">
    <mobile-saldo-simples [contas]="contas" [idConta]="idConta" [showNext]="false"></mobile-saldo-simples>
  </section>
  <section>
    <mobile-titulo-secao>Informações do pagamento</mobile-titulo-secao>
  </section>
  <section>
    <article>
      <div>
        <p>Beneficiário</p>
        <p><b>{{ pagamento.nomeBeneficiario | titlecase }}</b></p>
      </div>
      <div>
        <p>Valor do documento</p>
        <p><b>{{ pagamento.valor | currency: 'BRL' }}</b></p>
      </div>
      <div *ngIf="pagamento.tipoAutorizacaoRecebimentoValorDivergente > tipoPagamentoEnum.entreMinimoMaximo">
        <p>Valor a ser pago</p>
        <p><b>{{ pagamento.valorPagamentoAtualizado | currency: 'BRL' }}</b></p>
      </div>
      <form [formGroup]="formValor" *ngIf="pagamento.tipoAutorizacaoRecebimentoValorDivergente === tipoPagamentoEnum.entreMinimoMaximo ||
      pagamento.tipoAutorizacaoRecebimentoValorDivergente === tipoPagamentoEnum.qualquerValor">
        <ion-item lines="none">
          <ion-input labelPlacement="stacked" placeholder="00,00" type="tel" formControlName="valorFormatado"
                     maxlength="14"
                     [brmasker]="{money: true, thousand: '.',  decimalCaracter: ',', decimal: 2}"
                     errorText="Informe um valor válido"
                     helperText="Valor entre {{pagamento.valorMinimoPagamento | currency:'BRL'}} até {{pagamento.valorMaximoPagamento | currency:'BRL'}}">
            <div slot="label" class="custom-label">Valor a ser pago</div>
            <ion-text slot="start">R$</ion-text>
          </ion-input>
        </ion-item>
      </form>
      <div *ngIf="pagamento.valorDescontoCalculado">
        <p>Valor de desconto</p>
        <p><b>{{ pagamento.valorDescontoCalculado | currency: 'BRL' }}</b></p>
      </div>
      <div *ngIf="pagamento.valorJurosCalculado">
        <p>Valor de juros</p>
        <p><b>{{ pagamento.valorJurosCalculado | currency: 'BRL' }}</b></p>
      </div>
      <div *ngIf="pagamento.valorMultaCalculado">
        <p>Valor de multa</p>
        <p><b>{{ pagamento.valorMultaCalculado | currency: 'BRL' }}</b></p>
      </div>
      <div>
        <p>Cedente</p>
        <p><b>{{ pagamento.cedente }}</b></p>
      </div>
      <div>
        <p>Data do vencimento</p>
        <p><b>{{ pagamento.dataVencimento ? (pagamento.dataVencimento | date: 'dd/MM/yyyy') : '-'}}</b></p>
      </div>
      <div>
        <p>Data do pagamento</p>
        <p><b>{{ dataPagamento | date: 'dd/MM/yyyy' }}</b></p>
      </div>
    </article>
  </section>
</ion-content>
<ion-footer class="ion-no-border">
  <ion-button class="btn" expand="block" (click)="confirmar()" [disabled]="formValor.invalid">Confirmar</ion-button>
</ion-footer>
