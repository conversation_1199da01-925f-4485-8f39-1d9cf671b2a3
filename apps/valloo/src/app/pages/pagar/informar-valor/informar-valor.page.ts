import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { format, parseISO } from 'date-fns';
import { loading } from '@utils/loading.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CurrencyPipe } from '@angular/common';
import {AuthService, Conta, Credencial, MetodoSegurancaEnum, PagamentoService, Usuario} from '@corporativo/shared';
import {
  ModalAtencaoComponent,
  ModalAtivarCartaoComponent,
  ModalErroComponent, ModalSegurancaComponent,
  ModalSucessoComponent
} from '@corporativo/modals';

@Component({
  selector: 'vlo-informar-valor',
  templateUrl: './informar-valor.page.html',
  styleUrls: ['./informar-valor.page.scss'],
  standalone: false
})
export class InformarValorPage {
  usuario!: Usuario;
  idConta!: number;
  credencial!: Credencial;
  conta: Conta;
  idCredencial?: number;
  contas!: Conta[] | undefined;
  pagamento: any;
  dataPagamento: Date;
  tipoPagamentoEnum = {
    qualquerValor: 1,
    entreMinimoMaximo: 2,
    naoAceitarDivergente: 3,
    somenteMinimo: 4
  };

  formValor = new FormGroup({
    valor: new FormControl(0),
    valorFormatado: new FormControl('')
  });

  tributoDARF!: any | undefined;
  tributoFGTS: any | undefined;

  constructor(
    private router: Router,
    private modalController: ModalController,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private pagamentoService: PagamentoService
  ) {
    this.usuario = this.authService.getUser();
    this.pagamento = this.router.getCurrentNavigation()?.extras?.state?.['data'];
    this.tributoDARF = this.router.getCurrentNavigation()?.extras?.state?.['tributoDARF'];
    this.tributoFGTS = this.router.getCurrentNavigation()?.extras?.state?.['tributoFGTS'];
    this.pagamento.tipoAutorizacaoRecebimentoValorDivergente = this.pagamento.tipoAutorizacaoRecebimentoValorDivergente || this.tipoPagamentoEnum.naoAceitarDivergente;
    switch (this.pagamento.tipoAutorizacaoRecebimentoValorDivergente) {
      case this.tipoPagamentoEnum.qualquerValor: {
        this.setValorForm();
        break;
      }
      case this.tipoPagamentoEnum.entreMinimoMaximo: {
        this.setValorForm();
        break;
      }
      case this.tipoPagamentoEnum.somenteMinimo: {
        this.pagamento.valorPagamentoAtualizado = this.pagamento.valorMinimoPagamento;
        break;
      }
    }
    this.dataPagamento = new Date();
  }

  private setValorForm() {
    const currencyPipe = new CurrencyPipe('pt-BR');
    this.formValor.get('valorFormatado')?.setValue(currencyPipe.transform(this.pagamento.valorPagamentoAtualizado));
    this.formValor.get('valor')?.setValidators(
      [
        Validators.required,
        Validators.min(this.pagamento.valorMinimoPagamento),
        Validators.max(this.pagamento.valorMaximoPagamento)
      ]);
    this.formValor.get('valorFormatado')?.valueChanges.subscribe(x => {
      if (!x) {
        return;
      }
      const valor = +x
        .replace(/\./g, '')
        .replace(/,/g, '.');
      this.formValor.get('valor')?.setValue(valor);
      this.pagamento.valorPagamentoAtualizado = valor;
    });
    this.formValor.updateValueAndValidity();
  }

  async ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      const credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c: any) => c.idConta == this.idConta);
        return conta ? x : undefined;
      });
      if (credencial) {
        this.credencial = credencial;
      }
      this.idCredencial = this.credencial?.idCredencial;
      const conta = this.credencial?.contas.find((x: any) => x.idConta == this.idConta);
      if (conta) {
        this.contas = this.credencial?.contas.filter((x: any) => x.idConta == this.idConta);
        this.conta = conta;
      }
    }
  }

  async confirmar() {
    if (this.pagamento.valorPagamentoAtualizado > this.conta.saldoConta.saldoDisponivel) {
      await this.apresentarAtencao('Seu saldo é insuficiente. Realize uma transferência para essa conta e prossiga com o pagamento.');
      return;
    }
    const cartaoAtivo = await this.verificarCartaoAtivo();
    if (!cartaoAtivo) {
      return;
    }
    this.credencial = cartaoAtivo;
    const valido = await this.verificarSeguranca();
    if (!valido) {
      return;
    }
    await loading(
      this.pagar().subscribe({
        next: async (retorno: any) => {
          if (!retorno) {
            return;
          }
          const acao = await this.apresentarSucesso();
          if (acao.role == 'secundaria') {
            await this.router.navigate([`/extrato/${this.idConta}/comprovante`], { state: { boleto: retorno } });
            return;
          }
        },
        error: (erro: any) => {
          const message = erro?.error?.msg || erro?.mensagemErro || erro?.erros || erro?.mensagem;
          this.apresentarErro(message);
        }
      })
    );
  }

  pagar() {
    if (!this.pagamento.dataVencimento) {
      this.pagamento.dataVencimento = format(new Date(), 'yyyy-MM-dd');
    }
    const data: any = {
      protocoloInterno: this.pagamento.protocoloInterno,
      valor: this.pagamento.valorPagamentoAtualizado,
      dataVencimento: format(parseISO(this.pagamento.dataVencimento), 'yyyy-MM-dd'),
      darfRendimentoRequest: this.tributoDARF,
      fgtsRendimentoRequest: this.tributoFGTS
    };
    return this.pagamentoService.pagarTitulo(data);
  }

  async apresentarAtencao(mensagem: string) {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Atenção!',
        mensagem: mensagem,
        tituloBotaoPrimario: 'Fechar'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
  }

  async verificarSeguranca() {
    if (this.credencial?.metodoSegurancaTransacao == MetodoSegurancaEnum.NaoVerificar) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        metodoSegurancaTransacao: this.credencial?.metodoSegurancaTransacao,
        idCredencial: this.credencial?.idCredencial
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Sucesso!',
        mensagem: 'Pagamento efetuado com sucesso.',
        tituloBotao: 'Voltar ao início',
        tituloBotaoSecundario: 'Ver comprovante',
        urlRetorno: `pagar-contas/${this.idConta}`
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Ocoreu um erro!',
        mensagem: message || 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.role == 'primaria') {
      return;
    }
    if (data && data.role == 'secundaria') {
      return this.router.navigate([`pagar-contas/${this.idConta}`]);
    }
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c: any) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return credencialAtiva;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial
      }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

}
