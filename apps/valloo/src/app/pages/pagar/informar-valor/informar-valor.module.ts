import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {InformarValorRoutingModule} from './informar-valor-routing.module';
import {IonicModule} from '@ionic/angular';
import {InformarValorPage} from './informar-valor.page';
import {ReactiveFormsModule} from '@angular/forms';
import {TitleToolbarModule, TituloSecaoModule} from '@corporativo/components';
import {DirectivesModule} from '@corporativo/shared';
import {SaldoSimplesModule} from '@corporativo/saldo';

@NgModule({
  declarations: [
    InformarValorPage
  ],
  imports: [
    CommonModule,
    IonicModule,
    InformarValorRoutingModule,
    ReactiveFormsModule,
    TitleToolbarModule,
    TituloSecaoModule,
    DirectivesModule,
    SaldoSimplesModule
  ]
})
export class InformarValorModule {
}
