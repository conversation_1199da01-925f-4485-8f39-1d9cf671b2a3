import {Component} from '@angular/core';
import {Router} from '@angular/router';
import {interval, Subscription, take} from 'rxjs';
import {OnboardingService} from '@corporativo/shared';

@Component({
  selector: 'app-validar-caf-loading',
  templateUrl: './validar-caf-loading.page.html',
  styleUrls: ['./validar-caf-loading.page.scss'],
  standalone: false
})
export class ValidarCafLoadingPage {

  subscription: Subscription;
  jwt: any;
  fluxoDados: any = {};

  constructor(
    public router: Router,
    public onboardingService: OnboardingService
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
    this.jwt = state.jwt;
  }

  ngOnInit() {
    this.setIntervalo();
  }

  ionViewDidLeave() {
    this.subscription.unsubscribe();
  }

  setIntervalo() {
    const intervalo = interval(2000);
    const takeTime = intervalo.pipe(take(21));
    this.subscription = takeTime.subscribe((x: any) => {
      if (x === 20) {
        this.router.navigate(['/erro-validacao'], {state: {retornoCaf: true}});
      }
      this.buscarStatusCaf();
    });
  }

  buscarStatusCaf() {
    const request = {
      jwt: this.jwt
    };
    this.onboardingService.buscarStatusCaf(this.fluxoDados.cpf, request).subscribe({
      next: (x: any) => {
        console.log(x);
        if (x.status === 'PROCESSING') {
          return;
        } else if (x.status === 'APPROVED') {
          return this.router.navigate(['/cadastro-sucesso'], {state: {fluxoDados: this.fluxoDados}});
        } else {
          return this.router.navigate(['/erro-validacao'], {state: {retornoCaf: true}});
        }
      }, error: () => {
        return this.router.navigate(['/erro-validacao'], {state: {retornoCaf: true}});
      }
    });
  }

}
