import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {ValidarCafLoadingPage} from './validar-caf-loading.page';
import {ValidarCafLoadingPageRoutingModule} from './validar-caf-loading-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MaskitoDirective,
    ReactiveFormsModule,
    ValidarCafLoadingPageRoutingModule,
    TitleToolbarModule
  ],
  declarations: [ValidarCafLoadingPage]
})
export class ValidarCafLoadingPageModule {
}
