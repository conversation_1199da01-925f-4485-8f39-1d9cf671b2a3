ion-content {
  --padding-start: 2rem;
  --padding-end: 2rem;
}

.progress {
  margin-top: 24px;
}

ion-progress-bar::part(progress) {
  background: var(--ion-color-primary);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40px;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;

  ion-icon {
    font-size: 110px;
    margin-top: 5rem;
  }
}

ion-toolbar {
  --border-width: 0 !important;
}

ion-text {
  margin-bottom: 20px;
}

.toolbar{
  color: var(--ion-color-text-default);
}
