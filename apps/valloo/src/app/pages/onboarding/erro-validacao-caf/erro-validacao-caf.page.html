<ion-header>
  <ion-toolbar color="none">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="false">Atenção</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content>
  <section>
    <article class="img">
      <ion-icon name="atencao" color="warning"></ion-icon>
    </article>
    <h2 class="ion-text-center">Validações de segurança não processada</h2>
  </section>
  <div class="mensagem">
    <p>
      Para facilitar a identificação, lembre-se de tirar a foto em um ambiente bem iluminado. <br><br>
      Evite o uso de bonés, toucas ou acessórios que possam obstruir a visão do seu rosto. <br><br>
      Essas medidas ajudarão a garantir uma identificação precisa.
    </p>
  </div>
</ion-content>
<ion-footer>
  <ion-button expand="block" (click)="tentarNovamente()">Tentar novamente</ion-button>
  <ion-button class="ion-margin-top" expand="block" *ngIf="fluxoDados.tipo !== 'troca-senha-login'" fill="outline" (click)="voltarInicio()">Ir para o início</ion-button>
  <ion-button class="ion-margin-top" expand="block" *ngIf="fluxoDados.tipo === 'troca-senha-login'" fill="outline" (click)="voltarLogin()">Ir para o início</ion-button>
</ion-footer>
