import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {ErroValidacaoCafPage} from './erro-validacao-caf.page';
import {ErroValidacaoCafPageRoutingModule} from './erro-validacao-caf-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MaskitoDirective,
    ReactiveFormsModule,
    ErroValidacaoCafPageRoutingModule,
    TitleToolbarModule
  ],
  declarations: [ErroValidacaoCafPage]
})
export class ErroValidacaoCafPageModule {
}
