import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {AuthService} from '@corporativo/shared';
import {FluxoService} from '../../../../../../../libs/shared/src/lib/services/fluxo.service';

@Component({
  selector: 'app-erro-validacao-caf',
  templateUrl: './erro-validacao-caf.page.html',
  styleUrls: ['./erro-validacao-caf.page.scss'],
  standalone: false
})
export class ErroValidacaoCafPage implements OnInit {

  mensagemErro: any;
  mensagemPadrao = 'Não foi possível realizar a validação facial.';
  fluxoDados: any;
  dados: any;

  constructor(
    public router: Router,
    private authService: AuthService,
    private fluxoService: FluxoService
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
    this.dados = state.dados;
    this.mensagemErro = state.mensagem || this.mensagemPadrao;
  }

  async ngOnInit() {
  }

  tentarNovamente() {
    this.fluxoService.set({
      fluxoDados: this.fluxoDados,
      dados: this.dados
    });
    this.router.navigate(['/selfie-caf']);
  }

  voltarInicio() {
    this.fluxoService.clear();
    this.router.navigate(['/inicio']);
  }

  voltarLogin() {
    this.fluxoService.clear();
    this.authService.clearToken();
    this.router.navigate(['/login']);
  }
}
