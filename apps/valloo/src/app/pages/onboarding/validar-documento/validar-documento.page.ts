import {Component} from '@angular/core';
import {Router} from '@angular/router';
import {Platform} from '@ionic/angular';
import {CafDocumentDetector} from '@valloo-tecnologia/caf-document-detector';
import {environment} from '../../../../environments/environment';
import {CafFaceLiveness} from '@valloo-tecnologia/caf-face-liveness';
import {jwtDecode} from 'jwt-decode';
import {OnboardingService} from '@corporativo/shared';
import {tokenMock} from '@utils/mock-caf.util';
import {loading} from '@utils/loading.util';

@Component({
  selector: 'app-validar-documento',
  templateUrl: './validar-documento.page.html',
  styleUrls: ['./validar-documento.page.scss'],
  standalone: false
})
export class ValidarDocumentoPage {

  fluxoDados: any = {};

  constructor(
    public router: Router,
    private platform: Platform,
    private onboardingService: OnboardingService
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
  }

  async ionViewDidEnter() {
    if (!this.platform.is('hybrid')) {
      this.fluxoDados.type = 'cnh';
      const response$ = this.onboardingService.enviarDadosMock(this.fluxoDados);
      await loading(response$.subscribe({
        next: async (result: any) => {
          this.fluxoDados.idTransaction = result.id;
          this.fluxoDados.requestId = result.requestId;
          return this.router.navigate(['/cadastrar-senha'], {state:{fluxoDados: this.fluxoDados, jwt: tokenMock()}});
        }, error: async (error: any) => {
          return this.router.navigate(['/erro-validacao'], { state: { retornoCaf: false } });
        }
      }))
    } else {
      this.validarCaf(this.fluxoDados.tipoDocumento);
    }
  }

  private validarCaf(type: string) {
    let documentDetectorSteps: any[] = type.toLowerCase() === 'rg' ? ['RG_FRONT', 'RG_BACK'] : ['CNH_FRONT', 'CNH_BACK'];
    CafDocumentDetector.addListener('onSuccess', async (data) => {
      console.log('CafDocumentDetector onSuccess:', data);
      this.fluxoDados.imageFrontURL = data.result.captures[0].imageUrl;
      this.fluxoDados.imageBackURL = data.result.captures[1].imageUrl;
      this.fluxoDados.type = type;
      await this.iniciarLiveness();
    });
    CafDocumentDetector.addListener('onCancel', async (data) => {
      console.log('CafDocumentDetector onCancel:', data);
      await this.router.navigate(['/selecao-tipo-documento'], { state: { fluxoDados: this.fluxoDados } });
    });
    CafDocumentDetector.addListener('onError', async (data) => {
      console.log('CafDocumentDetector onError:', data);
      await this.router.navigate(['/erro-validacao'], { state: { retornoCaf: false } });
    });
    CafDocumentDetector.start({
      mobileToken: environment.tokenCaf,
      documentSteps: documentDetectorSteps,
      stage: 'PROD'
    });
  }

  async iniciarLiveness() {
    const documento = this.fluxoDados.cpf;
    CafFaceLiveness.start({
      mobileToken: environment.tokenFaceLiveness,
      personId: documento,
      stage: 'PROD'
    })
      .then((result: any) => {
        const jwt = result.signedResponse;
        const decoded: any = jwtDecode(jwt);
        this.fluxoDados.selfie = decoded.imageUrl;

        if (!decoded.isAlive) {
          this.router.navigate(['/erro-validacao'], {state: {retornoCaf: false, fluxoDados: this.fluxoDados}});
          return;
        }

        this.onboardingService.enviarDados(this.fluxoDados, jwt).subscribe({
          next: async () => {
            return this.router.navigate(['/cadastrar-senha'], {state:{fluxoDados: this.fluxoDados, jwt}});
          },
          error: async (error: any) => {
            return this.router.navigate(['/erro-validacao'], { state: { retornoCaf: false } });
          }
        });
      })
      .catch((error: any) => {
        console.error('Liveness error:', error);
        return this.router.navigate(['/erro-validacao'], { state: { retornoCaf: false, fluxoDados: this.fluxoDados } });
      });
  }

}
