import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {ValidarDocumentoPage} from './validar-documento.page';
import {ValidarDocumentoPageRoutingModule} from './validar-documento-routing.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MaskitoDirective,
    ReactiveFormsModule,
    ValidarDocumentoPageRoutingModule
  ],
  declarations: [ValidarDocumentoPage]
})
export class ValidarDocumentoPageModule {
}
