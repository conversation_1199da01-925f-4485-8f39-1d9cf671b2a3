import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {CadastrarSenhaPage} from './cadastrar-senha.page';
import {CadastrarSenhaPageRoutingModule} from './cadastrar-senha-routing.module';
import {TitleToolbarModule} from '../../../../../../../libs/components/src';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        MaskitoDirective,
        ReactiveFormsModule,
        CadastrarSenhaPageRoutingModule,
        TitleToolbarModule
    ],
  declarations: [CadastrarSenhaPage]
})
export class CadastrarSenhaPageModule {
}
