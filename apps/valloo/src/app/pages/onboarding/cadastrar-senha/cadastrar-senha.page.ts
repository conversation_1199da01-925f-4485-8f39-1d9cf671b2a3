import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {ModalController, PopoverController} from '@ionic/angular';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {environment} from '../../../../environments/environment';
import {concatMap} from 'rxjs';
import {loading} from '../../../../../../../libs/utils/loading.util';
import {FuncionalidadeService, OnboardingService, TipoObjetivoEnum} from '../../../../../../../libs/shared/src';
import {
  InfoEnvioTokenComponent
} from '../../../../../../../libs/components/src/lib/info-envio-token/info-envio-token.component';
import {ModalErroComponent} from '../../../../../../../libs/modals/src';
import {toast} from '../../../../../../../libs/utils/toast.util';
import {ValidatorsApp} from '../../../../../../../libs/utils/validators.util';


@Component({
  selector: 'app-cadastrar-senha',
  templateUrl: './cadastrar-senha.page.html',
  styleUrls: ['./cadastrar-senha.page.scss'],
  standalone: false
})
export class CadastrarSenhaPage implements OnInit {

  form: FormGroup;
  senhaContemLetraMinuscula = false;
  senhaContemLetraMaiuscula = false;
  senhaContemNumero = false;
  senhaContemCaracterEspecial = false;
  senhaContemMinimoCaracteres = false;
  fluxoDados: any;
  jwt: any;
  validacaoNecessaria: boolean;

  constructor(
      private router: Router,
      private formBuilder: FormBuilder,
      private popoverController: PopoverController,
      private onboardingService: OnboardingService,
      private modalController: ModalController,
      public funcionalidadeService: FuncionalidadeService,
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
    this.jwt = state.jwt;
    this.validacaoNecessaria = state.validacaoNecessaria;

    this.form = this.formBuilder.group({
      novaSenha: ['', Validators.compose([Validators.required,])],
      confirmarSenha: ['', Validators.compose([Validators.required])],
      token: ['']
    }, {
      validator: ValidatorsApp.senhasIguaisValidator
    });
    this.form?.get('novaSenha')?.valueChanges.subscribe((value: any) => {
      this.validarSenha(value);
    });
  }

  async ngOnInit() {
  }

  async ionViewDidEnter() {}

  validarSenha(senha: string) {
    const regexLetraMinuscula = /[a-z]/;
    const regexLetraMaiusula = /[A-Z]/;
    const regexNumeros = /[0-9]/;
    const regexCaracterEspecial = /[^a-zA-Z0-9]/;

    this.senhaContemLetraMinuscula = regexLetraMinuscula.test(senha);
    this.senhaContemLetraMaiuscula = regexLetraMaiusula.test(senha);
    this.senhaContemNumero = regexNumeros.test(senha);
    this.senhaContemCaracterEspecial = regexCaracterEspecial.test(senha);
    this.senhaContemMinimoCaracteres = senha.length >= 8;
  }

  cadastrarSenha() {
    this.fluxoDados.senha = this.form.controls['novaSenha'].value;
    const request: any = {
      cpf: this.fluxoDados.cpf,
      senha: this.fluxoDados.senha,
      idInstituicao: environment.idInstituicao,
      idProcessadora: environment.idProcessadora,
    }
    if (!this.fluxoDados.validacaoNecessaria) {
      loading(
        this.onboardingService.cadastrarPortadorLogin(request).subscribe({
          next: result => {
            if (this.fluxoDados.tipoDocumento != null) {
              return this.router.navigate(['/validar-caf-loading'], {state:{fluxoDados: this.fluxoDados, jwt: this.jwt}});
            } else {
              return this.router.navigate(['/cadastro-sucesso'], {state:{fluxoDados: this.fluxoDados}});
            }
          }, error: (error: any) => {
            const mensagem = error.msg || error.error.msg || error.error.message || error.message
            this.abrirModalErro(mensagem);
          }
        })
      )
    } else {
      const request = {
        cpf: this.fluxoDados.cpf,
        novaSenha: this.fluxoDados.senha,
        idProcessadora: environment.idProcessadora,
        idInstituicao: environment.idInstituicao
      }
      const response$ = this.onboardingService.registraValidacaoFacial(this.fluxoDados.cpf, TipoObjetivoEnum.OnboardingCAFForcado, this.jwt);
      loading(response$.pipe(
        concatMap(() => {
          return this.onboardingService.alterarSenhaPortador(request);
        })
      ).subscribe({
        next: (result: any) => {
          if (result.sucesso) {
            this.router.navigate(['/validar-caf-loading'], {state:{fluxoDados: this.fluxoDados, jwt: this.jwt}});
          }
        }, error: (error: any) => {
          const mensagem = error.msg || error.error.msg || error.error.message || error.message
          this.abrirModalErro(mensagem);
        }
      }))
    }
  }

  async informarSobreToken(ev: any) {
    const popover = await this.popoverController.create({
      component: InfoEnvioTokenComponent,
      event: ev,
      translucent: true
    });
    return await popover.present();
  }

  async abrirModalErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Criar senha',
        tituloTexto: 'Criar senha login',
        mensagem: message,
        tituloBotao: 'Ir para o início',
        corToolbar: 'none'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const {data} = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.router.navigate(['/login']);
    }
  }

  async enviarSMS() {
    const dataFormatada = this.fluxoDados.dataNascimento.split("T")[0];
    const response$ = this.funcionalidadeService.enviarTokenSenha(this.fluxoDados.cpf, dataFormatada);
    loading(response$.subscribe({
      next: (data: any) => {
        if (!data.sucesso) {
          toast('Desculpe, não foi possível realizar a operação.');
        } else {
          toast('Token enviado com sucesso.')
        }
      }, error: (error) => {
        const message = error.error.msg ? error.error.msg : error.msg;
        this.abrirModalErro(message);
      }
    }));
  }
}
