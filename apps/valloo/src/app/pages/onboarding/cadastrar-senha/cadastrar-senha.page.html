<ion-header>
  <ion-toolbar color="transparent">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="false">Criar uma senha</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content class="content-default">
  <article class="progress">
    <ion-progress-bar value=".80"></ion-progress-bar>
  </article>

  <article class="titulo">
    <ion-text>Para concluirmos, vamos criar uma senha para o seu aplicativo. Siga os critérios de segurança para
      criá-la.
    </ion-text>
  </article>

  <article class="criar-senha">
    <form [formGroup]="form">
      <ion-item lines="none" class="custom-input">
        <ion-input labelPlacement="stacked" label="Senha" placeholder="Senha" formControlName="novaSenha"
                   type="password" errorText="Informe a nova senha">
          <ion-input-password-toggle slot="end"></ion-input-password-toggle>
        </ion-input>
      </ion-item>

      <ion-item lines="none" class="custom-input">
        <ion-input labelPlacement="stacked" label="Confirme a senha" placeholder="Confirme a senha" formControlName="confirmarSenha"
                   type="password" errorText="Confirme a nova senha">
          <ion-input-password-toggle slot="end"></ion-input-password-toggle>
        </ion-input>
      </ion-item>

    </form>
  </article>

  <article class="validacao">
    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemLetraMaiuscula" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemLetraMaiuscula" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Uma letra MAIÚSCULA</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemLetraMinuscula" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemLetraMinuscula" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Uma letra minúscula</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemNumero" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemNumero" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Um numeral (123456)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemCaracterEspecial" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemCaracterEspecial" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>Um caractere especial (# $ % *)</ion-text>
      </ion-col>
    </ion-row>

    <ion-row>
      <ion-col size="1">
        <ion-icon *ngIf="!senhaContemMinimoCaracteres" name="close-outline" color="danger"></ion-icon>
        <ion-icon *ngIf="senhaContemMinimoCaracteres" name="checkmark-outline" color="success"></ion-icon>
      </ion-col>
      <ion-col>
        <ion-text>No mínimo 8 caracteres</ion-text>
      </ion-col>
    </ion-row>
  </article>

</ion-content>

<ion-footer class="ion-no-border">
  <ion-button class="btn ion-margin-bottom" expand="block" type="button" (click)="cadastrarSenha()" [disabled]="form.invalid || !senhaContemLetraMaiuscula || !senhaContemLetraMinuscula ||
                !senhaContemNumero || !senhaContemCaracterEspecial || !senhaContemMinimoCaracteres">Cadastrar senha</ion-button>
</ion-footer>
