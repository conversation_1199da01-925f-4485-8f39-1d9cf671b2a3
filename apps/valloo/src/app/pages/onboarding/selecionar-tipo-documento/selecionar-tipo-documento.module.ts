import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {SelecionarTipoDocumentoPage} from './selecionar-tipo-documento.page';
import {SelecionarTipoDocumentoPageRoutingModule} from './selecionar-tipo-documento-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MaskitoDirective,
    ReactiveFormsModule,
    SelecionarTipoDocumentoPageRoutingModule,
    TitleToolbarModule
  ],
  declarations: [SelecionarTipoDocumentoPage]
})
export class SelecionarTipoDocumentoPageModule {
}
