import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';

@Component({
  selector: 'app-selecionar-tipo-documento',
  templateUrl: './selecionar-tipo-documento.page.html',
  styleUrls: ['./selecionar-tipo-documento.page.scss'],
  standalone: false
})
export class SelecionarTipoDocumentoPage implements OnInit {

  fluxoDados: any;

  constructor(
    public router: Router
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
  }

  async ngOnInit() {
  }

  async enviarDocumento(tipoDocumento: string) {
    this.fluxoDados.tipoDocumento = tipoDocumento;
    return this.router.navigate(['/validar-documento'], {state: {fluxoDados: this.fluxoDados}});
  }
}
