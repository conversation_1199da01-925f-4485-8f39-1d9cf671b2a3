<ion-header>
  <ion-toolbar color="none">
    <mobile-title-toolbar class="toolbar" [mostrarVoltar]="false">Atenção</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content>

  <div class="validacao" *ngIf="retornoCaf && !permissao">
    <section>
      <article class="img">
        <ion-icon name="atencao" color="warning"></ion-icon>
      </article>
      <h2 class="ion-text-center">Abertura de conta em análise</h2>
    </section>
    <div class="mensagem">
      <p>
        Recebemos seus dados e estamos analisando.<br><br> O prazo é de até 48 horas, e você receberá um SMS com o status da analise.<br><br> Obrigado!
      </p>
    </div>
  </div>

  <div class="validacao" *ngIf="retornoCaf && permissao">
    <section>
      <article class="img">
        <ion-icon name="atencao" color="warning"></ion-icon>
      </article>
      <h2 class="ion-text-center">Permissão da câmera</h2>
    </section>
    <div class="mensagem">
      <p>
        É necessário permitir o acesso a câmera do seu dispositivo para continuar.<br><br>
        Acesse as configurações do aplicativo no seu celular e habilite as permissões
      </p>
    </div>
  </div>

  <div class="validacao" *ngIf="erroCaf">
    <section>
      <article class="img">
        <ion-icon name="atencao" color="warning"></ion-icon>
      </article>
      <h2 class="ion-text-center">Algo deu errado</h2>
    </section>
    <div class="mensagem">
      <p>
        Houve algum problema ao realizar a captura e processamento da solicitação da imagem.<br/><br/>
        Você pode tentar novamente acessando a botão abaixo:
      </p>
    </div>
  </div>

  <div class="validacao" *ngIf="!retornoCaf">
    <section>
      <article class="img">
        <ion-icon name="atencao" color="warning"></ion-icon>
      </article>
      <h2 class="ion-text-center">Validações de segurança não processadas</h2>
    </section>
    <div class="mensagem">
      <p>
        Para facilitar a identificação, lembre-se de tirar a foto em um ambiente bem iluminado.
        Evite o uso de bonés, toucas ou acessórios que possam dificultar a visão do seu rosto. <br><br>
        Essas medidas ajudarão a garantir uma identificação correta.
      </p>
    </div>
  </div>
</ion-content>

<ion-footer>
  <ion-button expand="block" *ngIf="!retornoCaf" (click)="tentarNovamente()">Tentar novamente</ion-button>
  <ion-button class="ion-margin-top" fill="outline" expand="block" (click)="voltarInicio()">Ir para o início</ion-button>
</ion-footer>
