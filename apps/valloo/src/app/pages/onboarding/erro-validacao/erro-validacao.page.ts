import {Component, OnInit} from '@angular/core';
import {Router} from '@angular/router';

@Component({
  selector: 'app-erro-validacao',
  templateUrl: './erro-validacao.page.html',
  styleUrls: ['./erro-validacao.page.scss'],
  standalone: false
})
export class ErroValidacaoPage implements OnInit {

  permissao: boolean;
  retornoCaf: boolean;
  erroCaf: boolean;

  fluxoDados: any;

  constructor(
    public router: Router
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.retornoCaf = state.retornoCaf;
    this.permissao = state.permissao;
    this.erroCaf = state.erroCaf;
    this.fluxoDados = state.fluxoDados;
  }

  async ngOnInit() {
  }

  tentarNovamente() {
    this.router.navigate(['/validar-documento'], {state:{fluxoDados: this.fluxoDados}});
  }

  voltarInicio() {
    this.router.navigate(['/login']);
  }
}
