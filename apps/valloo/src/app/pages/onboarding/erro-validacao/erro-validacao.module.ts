import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {ErroValidacaoPage} from './erro-validacao.page';
import {ErroValidacaoPageRoutingModule} from './erro-validacao-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    MaskitoDirective,
    ReactiveFormsModule,
    ErroValidacaoPageRoutingModule,
    TitleToolbarModule
  ],
  declarations: [ErroValidacaoPage]
})
export class ErroValidacaoPageModule {
}
