import {toast} from '@utils/toast.util';
import {loading} from '@utils/loading.util';
import {AuthService} from '@corporativo/shared';
import {Router} from '@angular/router';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-cadastro-sucesso',
  templateUrl: './cadastro-sucesso.page.html',
  styleUrls: ['./cadastro-sucesso.page.scss'],
  standalone: false
})
export class CadastroSucessoPage implements OnInit {

  fluxoDados: any;

  constructor(
    public router: Router,
    private authService: AuthService,
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.fluxoDados = state.fluxoDados;
  }

  async ngOnInit() {
  }

  async acessarApp() {
    const auth = {
      cpf: this.fluxoDados.cpf,
      senha: this.fluxoDados.senha
    };
    await this.logar(auth);
  }

  async logar(auth: any) {
    console.log('==> auth: ', auth);
    await loading(this.authService.entrar(auth).subscribe({
      next: () => {
        this.router.navigate(['inicio']);
      },
      error: (error: any) => {
        const message = error.message ? error.message : error.msg;
        toast(message || 'Não foi possível realizar o login, tente novamente.');
      }
    }));

  }
}
