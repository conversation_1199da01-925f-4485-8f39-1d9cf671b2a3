import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {MaskitoDirective} from '@maskito/angular';
import {CadastroSucessoPage} from './cadastro-sucesso.page';
import {CadastroSucessoPageRoutingModule} from './cadastro-sucesso-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        MaskitoDirective,
        ReactiveFormsModule,

        CadastroSucessoPageRoutingModule,
        TitleToolbarModule
    ],
  declarations: [CadastroSucessoPage]
})
export class CadastroSucessoPageModule {
}
