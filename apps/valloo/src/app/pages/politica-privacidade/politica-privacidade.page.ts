import {Component, OnInit} from '@angular/core';
import {Observable} from 'rxjs';
import {DiretrizService} from '../../../../../../libs/shared/src';

@Component({
  selector: 'mobile-politica-privacidade',
  templateUrl: './politica-privacidade.page.html',
  styleUrls: ['./politica-privacidade.page.scss'],
  standalone: false
})
export class PoliticaPrivacidadePage implements OnInit {
  termoUso$!: Observable<any>;

  constructor(
    private diretrizService: DiretrizService
  ) {

  }

  ngOnInit() {
    this.termoUso$ = this.diretrizService.buscarPoliticaPrivacidade();
  }
}
