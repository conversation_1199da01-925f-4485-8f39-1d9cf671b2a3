import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {IonicModule} from '@ionic/angular';
import {PoliticaPrivacidadePage} from './politica-privacidade.page';
import {PoliticaPrivacidadePageRoutingModule} from './politica-privacidade-routing.module';
import {TitleToolbarModule} from '@corporativo/components';

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        IonicModule,
        PoliticaPrivacidadePageRoutingModule,
        TitleToolbarModule,
        ReactiveFormsModule
    ],
  declarations: [PoliticaPrivacidadePage]
})
export class PoliticaPrivacidadePageModule {
}
