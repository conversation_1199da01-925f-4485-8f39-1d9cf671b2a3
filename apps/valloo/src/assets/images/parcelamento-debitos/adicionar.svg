<svg width="68" height="68" viewBox="0 0 68 68" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_76_193)">
<circle cx="34" cy="32" r="30" fill="white"/>
</g>
<g filter="url(#filter1_d_76_193)">
<path d="M46.1784 30.2862H34.7179V18.8257C34.7179 18.3716 34.3463 18 33.8922 18H33.1161C32.6619 18 32.2904 18.3716 32.2904 18.8257V30.2862H20.8257C20.3716 30.2862 20 30.6578 20 31.1119V31.8881C20 32.3422 20.3716 32.7138 20.8257 32.7138H32.2862V44.1743C32.2862 44.6284 32.6578 45 33.1119 45H33.8881C34.3422 45 34.7138 44.6284 34.7138 44.1743V32.7138H46.1743C46.6284 32.7138 47 32.3422 47 31.8881V31.1119C47 30.6578 46.6284 30.2862 46.1743 30.2862H46.1784Z" fill="#52565F"/>
</g>
<defs>
<filter id="filter0_d_76_193" x="0" y="0" width="68" height="68" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_76_193"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_76_193" result="shape"/>
</filter>
<filter id="filter1_d_76_193" x="16" y="15" width="35" height="35" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_76_193"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_76_193" result="shape"/>
</filter>
</defs>
</svg>
