/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

//Imports css do projeto
@use './theme/animations';

// Shared Styles
@use '../../../libs/shared/src/scss/shared.scss' as *;

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */
//@import "@ionic/angular/css/palettes/dark.system.css";


// Fonte Path
$font-path: "./assets/fonts/" !default;

@font-face {
  font-family: 'Lato';
  font-weight: normal;
  font-style: normal;
  src: url("#{$font-path}Lato/Lato-Regular.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: 900;
  font-style: normal;
  src: url("#{$font-path}Lato/Lato-Black.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: 900;
  font-style: italic;
  src: url("#{$font-path}Lato/Lato-BlackItalic.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: bold;
  font-style: normal;
  src: url("#{$font-path}Lato/Lato-Bold.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: bold;
  font-style: italic;
  src: url("#{$font-path}Lato/Lato-BoldItalic.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: normal;
  font-style: italic;
  src: url("#{$font-path}Lato/Lato-Italic.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: 300;
  font-style: normal;
  src: url("#{$font-path}Lato/Lato-Light.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: 300;
  font-style: italic;
  src: url("#{$font-path}Lato/Lato-LightItalic.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: 100;
  font-style: normal;
  src: url("#{$font-path}Lato/Lato-Thin.ttf");
}

@font-face {
  font-family: 'Lato';
  font-weight: 100;
  font-style: italic;
  src: url("#{$font-path}Lato/Lato-ThinItalic.ttf");
}

ion-button {
  height: 56px;
  font-weight: 700;
  text-transform: none;
  --border-radius: 8px;
  margin: 0;
}

ion-item {
  width: 100%;
}

ion-header {
  ion-toolbar {
    --min-height: 80px;
    // --background: var(--ion-toolbar-background);

    ion-title {
      font-weight: 600;
      font-size: 20px;
      line-height: 24px;
    }
  }
}

ion-grid {
  margin: 0;

  ion-row {
    margin: 0;

    ion-col {
      margin: 0;
    }
  }
}

ion-content {
  --background: var(--ion-color-background);
}

// Auxiliares
.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.flex-end {
  align-items: flex-end;
}

.m-0 {
  margin: 0;
}

.mt-05 {
  margin-top: 0.5rem;
}

.mt-1 {
  margin-top: 1rem;
}

.loading-default {
  --background: var(--ion-color-background);
  --spinner-color: var(--ion-color-primary);
}

ion-input::part(label) {
  color: var(--ion-color-gray-50);
  font-weight: var(--ion-font-weight-label);
  font-size: var(--ion-font-size-label);
}

form {
  ion-item {
    --background: var(--ion-color-background);
    --ion-text-color: var(--ion-color-gray-50);
    --background-hover: transparend;
    --padding-start: 0;
    //--inner-padding-end: 0;

    //    .custom-label {
    //      color: var(--ion-color-gray-50);
    //      font-weight: 600;
    //      font-size: var(--ion-font-size-label);
    //      margin-bottom: 20px;
    //    }
    //
    ion-input.ios,
    ion-select.ios,
    ion-textarea.ios {
      font-weight: 600;
      --placeholder-font-weight: 500;
      --color: var(--ion-color-gray-50);
      --placeholder-color: var(--ion-color-text-default);
      --placeholder-opacity: 0.3;
    }

    .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios {
      display: block;
      margin-right: -100%;
    }

    .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios {
      font-size: 10px;
    }

    &.ion-touched.ion-invalid {
      ion-input,
      ion-select,
      ion-textarea,
      .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios {
        display: block;
      }

      //      .custom-label {
      //        color: var(--ion-color-danger);
      //        margin-bottom: 20px;
      //      }
      //
      //      ion-select::part(label) {
      //        color: var(--ion-color-danger);
      //      }
    }

    //
    ion-select::part(label) {
      color: var(--ion-color-gray-50);
      font-weight: var(--ion-font-weight-label);
      font-size: var(--ion-font-size-label);
    }

    ion-select::part(icon) {
      color: var(--ion-color-quartenary);
    }

    ion-select::part(placeholder) {
      font-weight: 500;
      font-size: var(--ion-font-size-label);
    }
  }

  ion-toggle::part(label) {
    color: var(--ion-color-gray-50);
    font-weight: var(--ion-font-weight-label);
    font-size: 13px;
  }
}

ion-list {
  ion-item {
    --background: var(--ion-color-background);
    --ion-text-color: var(--ion-color-text-default);
    --background-hover: transparend;
    --padding-start: 0;
    --inner-padding-end: 0;

    .custom-label {
      color: var(--ion-color-gray-50);
      font-weight: var(--ion-font-weight-label);
      font-size: var(--ion-font-size-label);
    }

    ion-input.ios,
    ion-select.ios,
    ion-textarea.ios {
      font-weight: var(--ion-font-weight-label);
      --placeholder-font-weight: 500;
      --color: var(--ion-color-text-default);
      --placeholder-color: var(--ion-color-text-default);
      --placeholder-opacity: 0.3;
    }

    .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios {
      display: block;
      margin-right: -100%;
    }

    .input-bottom.sc-ion-input-ios .helper-text.sc-ion-input-ios {
      font-size: 10px;
    }

    &.ion-touched.ion-invalid {
      ion-input,
      ion-select,
      ion-textarea,
      .input-bottom.sc-ion-input-ios .error-text.sc-ion-input-ios {
        display: block;
      }

      .custom-label {
        color: var(--ion-color-danger);
      }

      ion-select::part(label) {
        color: var(--ion-color-danger);
      }
    }

    ion-select::part(label) {
      color: var(--ion-color-gray-50);
      font-weight: var(--ion-font-weight-label);
      font-size: var(--ion-font-size-label);
    }

    ion-select::part(icon) {
      color: var(--ion-color-quartenary);
    }

    ion-select::part(placeholder) {
      font-weight: 500;
      font-size: var(--ion-font-size-label);
    }
  }

  ion-toggle::part(label) {
    color: var(--ion-color-gray-50);
    font-weight: var(--ion-font-weight-label);
    font-size: 13px;
  }
}

.sc-ion-input-ios-s > [slot=start]:last-of-type {
  margin: 0 0.3rem 0 0 !important;
}

ion-select-popover {
  ion-item {
    font-weight: 500;
    //--color: var(--ion-color-gray-50);
    --background-focused: var(--ion-color-quartenary);
    --inner-border-width: 0;
    --border-color: var(--ion-color-medium);
    --border-width: 0 0 1px 0;
    --padding-start: 1rem;
    --padding-end: 1rem;
  }
}

.ripple-parent {
  position: relative;
  overflow: hidden;
}

ion-datetime {
  --background: var(--ion-color-background);
}

.popover-data {
  --min-width: 300px;
}

.diretrizes {

  h1 {
    text-align: center;
    font-size: 1.0rem;
    font-weight: bold;
    color: var(--ion-color-primary);
    margin: 16px auto 25px auto;
  }

  h3 {
    font-size: 0.9rem;
    font-weight: bold;
  }

  p {
    white-space: pre-wrap;
    font-size: 0.7rem;
    text-align: justify;

    &.centered {
      text-align: center;
      margin-top: 25px;
    }
  }
}

.content-default {
  --padding-bottom: 1.5rem;
  --padding-end: 1.5rem;
  --padding-start: 1.5rem;
}

.subsection {
  margin-bottom: 0.6rem;
}

.faleConosco {
  h1 {
    text-align: center;
    font-size: 1.0rem;
    font-weight: bold;
    color: var(--ion-color-primary);
    margin: 16px auto 25px auto;
  }

  h3 {
    font-size: 0.9rem;
    font-weight: bold;
  }

  p {
    text-align: center;

    &.centered {
      text-align: center;
      margin-top: 25px;
    }
  }
}

.modal-half-default {
  --backdrop-opacity: 0.7;
  --background: transparent;
}

.cartoes {
  ion-item {
    --background: var(--ion-color-medium);
  }
}

ion-footer {
  background: var(--ion-color-background);
  padding: 1rem 1.5rem;
}
