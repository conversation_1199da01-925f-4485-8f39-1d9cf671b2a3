import { CapacitorConfig } from '@capacitor/cli';
import { KeyboardResize } from '@capacitor/keyboard';

let config: CapacitorConfig;

const baseConfig: CapacitorConfig = {
  appId: 'br.com.corporativo.valloo',
  appName: 'Valloo Corporativo',
  webDir: '../../dist/apps/valloo',
  plugins: {
    CapacitorHttp: {
      enabled: true,
    },
    CapacitorCookies: {
      enabled: true,
    },
    SplashScreen: {
      launchAutoHide: false,
    },
    Keyboard: {
      resize: KeyboardResize.Ionic,
      resizeOnFullScreen: true
    },
  },
};
// @ts-ignore
switch (process.env.NODE_ENV) {
  case 'hom':
    config = {
      ...baseConfig,
      appId: 'br.com.corporativo.valloo',
      ios: {
        scheme: 'App hom',
      },
      android: {
        flavor: 'hom',
      },
    };
    break;
  default:
    config = {
      ...baseConfig,
      appId: 'br.com.corporativo.valloo',
      ios: {
        scheme: 'App',
      },
      android: {
        flavor: 'prod',
      },
    };
    break;
}

export default config;
