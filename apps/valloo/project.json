{"name": "valloo", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/valloo/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/valloo", "browser": "apps/valloo/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/valloo/tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["lodash", "crypto-js", "file-saver", "qrcode", "html2canvas", "localforage", "@mrmgomes/boleto-utils"], "assets": [{"glob": "**/*", "input": "apps/valloo/src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}, {"glob": "**/*.svg", "input": "apps/valloo/src/assets/custom-icons", "output": "./svg"}], "styles": ["apps/valloo/src/global.scss", "apps/valloo/src/theme/custom.scss", "apps/valloo/src/theme/variables.scss"], "fileReplacements": [{"replace": "libs/shared/src/lib/environments/environment.ts", "with": "apps/valloo/src/environments/environment.ts"}]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "fileReplacements": [{"replace": "apps/valloo/src/environments/environment.ts", "with": "apps/valloo/src/environments/environment.prod.ts"}, {"replace": "libs/shared/src/lib/environments/environment.ts", "with": "apps/valloo/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "hom": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "apps/valloo/src/environments/environment.ts", "with": "apps/valloo/src/environments/environment.hom.ts"}, {"replace": "libs/shared/src/lib/environments/environment.ts", "with": "apps/valloo/src/environments/environment.hom.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "valloo:build:production"}, "development": {"buildTarget": "valloo:build:development"}, "hom": {"buildTarget": "valloo:build:hom"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "valloo:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "valloo:build", "staticFilePath": "dist/apps/valloo/browser", "spa": true}}, "cap": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "--help"}}, "add": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "add"}, "configurations": {"ios": {"cmd": "add ios"}, "android": {"cmd": "add android"}}}, "copy": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "copy"}, "configurations": {"ios": {"cmd": "copy ios"}, "android": {"cmd": "copy android"}}}, "open": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "open"}, "configurations": {"ios": {"cmd": "open ios"}, "android": {"cmd": "open android"}}}, "run": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "run"}, "configurations": {"ios": {"cmd": "run ios"}, "android": {"cmd": "run android"}}}, "sync": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "sync"}, "configurations": {"ios": {"cmd": "sync ios"}, "android": {"cmd": "sync android"}}}, "update": {"executor": "@valloo-tecnologia/nx-ionic-cap:cap", "options": {"cmd": "update"}, "configurations": {"ios": {"cmd": "update ios"}, "android": {"cmd": "update android"}}}}}