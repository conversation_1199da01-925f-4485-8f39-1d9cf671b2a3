PODS:
  - CafFaceAuth (6.2.1):
    - FaceLiveness (= 6.3.2)
  - CafSolutions (1.0.1)
  - Capacitor (6.2.1):
    - CapacitorCordova
  - CapacitorApp (6.0.1):
    - Capacitor
  - CapacitorBarcodeScanner (1.0.4):
    - Capacitor
    - OSBarcodeLib (~> 1.1.0)
  - CapacitorCamera (6.1.2):
    - Capacitor
  - CapacitorClipboard (6.0.2):
    - Capacitor
  - CapacitorCommunityFileOpener (6.0.1):
    - Capacitor
  - CapacitorCordova (6.2.1)
  - CapacitorDevice (6.0.2):
    - Capacitor
  - CapacitorFilesystem (6.0.3):
    - Capacitor
  - CapacitorGeolocation (6.1.0):
    - Capacitor
  - CapacitorHaptics (6.0.1):
    - Capacitor
  - CapacitorKeyboard (6.0.2):
    - Capacitor
  - CapacitorNativeBiometric (0.0.1):
    - Capacitor
  - CapacitorScreenOrientation (6.0.3):
    - Capacitor
  - CapacitorShare (6.0.3):
    - Capacitor
  - CapacitorStatusBar (6.0.1):
    - Capacitor
  - CordovaPluginsStatic (7.4.2):
    - CapacitorCordova
    - OneSignalXCFramework (= 5.2.14)
  - DocumentDetector (15.0.3):
    - CafSolutions (= 1.0.1)
    - TensorFlowLiteC (= 2.14.0)
  - FaceLiveness (6.3.2):
    - CafSolutions (= 1.0.1)
    - FingerprintPro (= 2.6.0)
    - iProov (= 12.2.1)
  - FingerprintPro (2.6.0)
  - iProov (12.2.1)
  - OneSignalXCFramework (5.2.14):
    - OneSignalXCFramework/OneSignalComplete (= 5.2.14)
  - OneSignalXCFramework/OneSignal (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalLiveActivities
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalComplete (5.2.14):
    - OneSignalXCFramework/OneSignal
    - OneSignalXCFramework/OneSignalInAppMessages
    - OneSignalXCFramework/OneSignalLocation
  - OneSignalXCFramework/OneSignalCore (5.2.14)
  - OneSignalXCFramework/OneSignalExtension (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalInAppMessages (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLiveActivities (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalLocation (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalUser
  - OneSignalXCFramework/OneSignalNotifications (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalExtension
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOSCore (5.2.14):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalOutcomes (5.2.14):
    - OneSignalXCFramework/OneSignalCore
  - OneSignalXCFramework/OneSignalUser (5.2.14):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalNotifications
    - OneSignalXCFramework/OneSignalOSCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OSBarcodeLib (1.1.3)
  - TensorFlowLiteC (2.14.0):
    - TensorFlowLiteC/Core (= 2.14.0)
  - TensorFlowLiteC/Core (2.14.0)
  - VallooTecnologiaCafDocumentDetector (1.1.4):
    - Capacitor
    - DocumentDetector (= 15.0.3)
  - VallooTecnologiaCafFaceAuth (1.1.2):
    - CafFaceAuth (= 6.2.1)
    - Capacitor
  - VallooTecnologiaCafFaceLiveness (1.1.3):
    - Capacitor
    - FaceLiveness (= 6.3.2)

DEPENDENCIES:
  - "Capacitor (from `../../../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../../../node_modules/@capacitor/app`)"
  - "CapacitorBarcodeScanner (from `../../../../node_modules/@capacitor/barcode-scanner`)"
  - "CapacitorCamera (from `../../../../node_modules/@capacitor/camera`)"
  - "CapacitorClipboard (from `../../../../node_modules/@capacitor/clipboard`)"
  - "CapacitorCommunityFileOpener (from `../../../../node_modules/@capacitor-community/file-opener`)"
  - "CapacitorCordova (from `../../../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../../../node_modules/@capacitor/filesystem`)"
  - "CapacitorGeolocation (from `../../../../node_modules/@capacitor/geolocation`)"
  - "CapacitorHaptics (from `../../../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../../../node_modules/@capacitor/keyboard`)"
  - CapacitorNativeBiometric (from `../../../../node_modules/capacitor-native-biometric`)
  - "CapacitorScreenOrientation (from `../../../../node_modules/@capacitor/screen-orientation`)"
  - "CapacitorShare (from `../../../../node_modules/@capacitor/share`)"
  - "CapacitorStatusBar (from `../../../../node_modules/@capacitor/status-bar`)"
  - CordovaPluginsStatic (from `../capacitor-cordova-ios-plugins`)
  - "VallooTecnologiaCafDocumentDetector (from `../../../../node_modules/@valloo-tecnologia/caf-document-detector`)"
  - "VallooTecnologiaCafFaceAuth (from `../../../../node_modules/@valloo-tecnologia/caf-face-auth`)"
  - "VallooTecnologiaCafFaceLiveness (from `../../../../node_modules/@valloo-tecnologia/caf-face-liveness`)"

SPEC REPOS:
  trunk:
    - CafFaceAuth
    - CafSolutions
    - DocumentDetector
    - FaceLiveness
    - FingerprintPro
    - iProov
    - OneSignalXCFramework
    - OSBarcodeLib
    - TensorFlowLiteC

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../../../node_modules/@capacitor/app"
  CapacitorBarcodeScanner:
    :path: "../../../../node_modules/@capacitor/barcode-scanner"
  CapacitorCamera:
    :path: "../../../../node_modules/@capacitor/camera"
  CapacitorClipboard:
    :path: "../../../../node_modules/@capacitor/clipboard"
  CapacitorCommunityFileOpener:
    :path: "../../../../node_modules/@capacitor-community/file-opener"
  CapacitorCordova:
    :path: "../../../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../../../node_modules/@capacitor/filesystem"
  CapacitorGeolocation:
    :path: "../../../../node_modules/@capacitor/geolocation"
  CapacitorHaptics:
    :path: "../../../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../../../node_modules/@capacitor/keyboard"
  CapacitorNativeBiometric:
    :path: "../../../../node_modules/capacitor-native-biometric"
  CapacitorScreenOrientation:
    :path: "../../../../node_modules/@capacitor/screen-orientation"
  CapacitorShare:
    :path: "../../../../node_modules/@capacitor/share"
  CapacitorStatusBar:
    :path: "../../../../node_modules/@capacitor/status-bar"
  CordovaPluginsStatic:
    :path: "../capacitor-cordova-ios-plugins"
  VallooTecnologiaCafDocumentDetector:
    :path: "../../../../node_modules/@valloo-tecnologia/caf-document-detector"
  VallooTecnologiaCafFaceAuth:
    :path: "../../../../node_modules/@valloo-tecnologia/caf-face-auth"
  VallooTecnologiaCafFaceLiveness:
    :path: "../../../../node_modules/@valloo-tecnologia/caf-face-liveness"

SPEC CHECKSUMS:
  CafFaceAuth: 794cf4b7f38de0dd12ead9382c6bdcace0dcee95
  CafSolutions: d509f8672793cc8c022a91b203c84886682ca478
  Capacitor: 1e0d0e7330dea9f983b50da737d8918abcf273f8
  CapacitorApp: 0bc633b4eae40a1f32cd2834788fad3bc42da6a1
  CapacitorBarcodeScanner: 869a8f023e90b7cc92c18919732e3d935b2c0792
  CapacitorCamera: 9052bd3f464f135ba983feccd8ac1266272f0549
  CapacitorClipboard: 55e0a514f1e97b1409d533266c119dcbff3e78c3
  CapacitorCommunityFileOpener: f383451eef66633c7d80e8d247fcfd35032b99d0
  CapacitorCordova: 8d93e14982f440181be7304aa9559ca631d77fff
  CapacitorDevice: 1a215717f0b5061503b21a03508b0ec458a57d78
  CapacitorFilesystem: fa3099b3c3aa43a1b51362d0c999301ab1a9a752
  CapacitorGeolocation: d994ee64ee41909e2089e575c9d3a976007c2812
  CapacitorHaptics: fe689ade56ef20ec9b041a753c6da70c5d8ec9a9
  CapacitorKeyboard: 2700f9b18687be021e28b5a09b59eb151a46d5e0
  CapacitorNativeBiometric: b47637a8cd349bdac014424bb4ddcae9ee5d4919
  CapacitorScreenOrientation: 3bb823f5d265190301cdc5d58a568a287d98972a
  CapacitorShare: 7af6ca761ce62030e8e9fbd2eb82416f5ceced38
  CapacitorStatusBar: b81d4fb5d4e0064c712018071b3ab4b810b39a63
  CordovaPluginsStatic: 2e52f7dec452effbcfa3ca84e815ad18cd903e9f
  DocumentDetector: fa90086e0361c262ebe9a06a2efe7b8c78652bc9
  FaceLiveness: c62b4be6d56e6e9db07e35b9c899c4f95321b952
  FingerprintPro: 3f06f491c77d871ab543b49fd25fddc52dc34f8c
  iProov: d1c8fed3e9b7c1602a326d4789bbf883d3563880
  OneSignalXCFramework: 7112f3e89563e41ebc23fe807788f11985ac541c
  OSBarcodeLib: f2e981270a64faf476cb790864262b29ab8cd68a
  TensorFlowLiteC: a836de9b6e6018665ccc349538bdd2c3b80dd7a5
  VallooTecnologiaCafDocumentDetector: 60b8e03ad8c4a478124cec0796b972267a85c9d3
  VallooTecnologiaCafFaceAuth: b39cea5ea4215f34061fe9595e558aa0dae5dc0a
  VallooTecnologiaCafFaceLiveness: 65028c986151ea4ffe365b2fda4575609e5a9619

PODFILE CHECKSUM: e280cbd7f9f8d1d1cb1dc2c69e949ea690437956

COCOAPODS: 1.16.2
