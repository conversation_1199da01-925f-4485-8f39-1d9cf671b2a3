import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'mobile-ler-qrcode',
  templateUrl: './ler-qrcode.component.html',
  styleUrl: './ler-qrcode.component.scss',
  standalone: false,
})
export class LerQrcodeComponent {
  idConta!: number;

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {}

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }

  fechar() {
    return this.router.navigate([`pix/${this.idConta}`], {
      skipLocationChange: true,
    });
  }
}
