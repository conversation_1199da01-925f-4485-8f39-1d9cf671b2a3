import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChavesComponent } from './chaves.component';
import { ChavesRoutingModule } from './chaves-routing.module';
import { IonicModule } from '@ionic/angular';
import { SkeletonModule, TitleToolbarModule, TituloSecaoModule } from '@corporativo/components';
import { CadastrarComponent } from './cadastrar/cadastrar.component';
import { ReactiveFormsModule } from '@angular/forms';
import { PipesModule } from '@corporativo/shared';
import { MaskitoDirective } from '@maskito/angular';

@NgModule({
  declarations: [
    ChavesComponent,
    CadastrarComponent
  ],
  imports: [
    CommonModule,
    ChavesRoutingModule,
    IonicModule,
    TitleToolbarModule,
    TituloSecaoModule,
    ReactiveFormsModule,
    MaskitoDirective,
    SkeletonModule,
    PipesModule,
  ]
})
export class ChavesModule {
}
