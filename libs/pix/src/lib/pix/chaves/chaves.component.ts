import { Component, OnInit, ViewChild } from '@angular/core';
import { AlertController, ModalController, Platform } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { catchError, Observable } from 'rxjs';
import {
  AuthService,
  Credencial,
  environment,
  MetodoSegurancaEnum,
  PixService,
  Usuario,
} from '@corporativo/shared';
import { Clipboard } from '@capacitor/clipboard';
import { toast } from '@utils/toast.util';
import {
  ModalAtivarCartaoComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent,
} from '@corporativo/modals';
import { loading } from '@utils/loading.util';

@Component({
  selector: 'vlo-chaves',
  templateUrl: './chaves.component.html',
  styleUrls: ['./chaves.component.scss'],
  standalone: false,
})
export class ChavesComponent implements OnInit {
  chaves$!: Observable<any[]>;
  usuario!: Usuario;
  erroBuscar = false;
  tipoIcones: any = {
    cpf: {
      icon: 'documento',
      label: 'CPF',
    },
    email: {
      icon: 'c-email',
      label: 'E-mail',
    },
    automatica: {
      icon: 'security',
      label: 'Aleatória',
    },
    telefone: {
      icon: 'recarga-celular',
      label: 'Telefone',
    },
  };
  @ViewChild('popover') popover!: any;
  isOpen = false;
  chave: any;
  idConta!: number;
  credencial!: Credencial;
  modalBreakpoint = 0.5;

  constructor(
    private modalController: ModalController,
    private router: Router,
    private pixService: PixService,
    private authService: AuthService,
    private alertController: AlertController,
    private platform: Platform,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {}

  ionViewDidEnter() {
    this.usuario = this.authService.getUser();
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      // @ts-ignore
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        return conta ? x : null;
      });
    }
    this.buscarChaves();
  }

  cadastrar(tipo: string) {
    this.router.navigate([`pix/${this.idConta}/chaves/cadastrar/${tipo}`]);
    this.modalController.dismiss();
  }

  buscarChaves() {
    this.erroBuscar = false;
    this.chaves$ = this.pixService.buscarTodasChaves(this.idConta).pipe(
      catchError((err) => {
        this.erroBuscar = true;
        throw err;
      })
    );
  }

  mostrarAcoes(e: Event, chave: any) {
    this.popover.event = e;
    this.isOpen = true;
    this.chave = chave;
  }

  async excluirChave() {
    const ativo = await this.verificarCartaoAtivo();
    if (!ativo) {
      return;
    }
    const valido = await this.verificarSeguranca();
    if (!valido) {
      return;
    }
    await loading(
      this.pixService.excluirChave(this.chave.valor).subscribe({
        next: () => {
          const mensagemSucesso = {
            titulo: 'Exclusão de chave pix',
            mensagem: 'Sua chave pix foi excluida com sucesso. ',
            urlRetorno: `pix/${this.idConta}/chaves`,
            classeImagem: 'sucesso-2',
            tituloBotao: 'Voltar para minhas chaves',
          };
          this.apresentarSucesso(mensagemSucesso);
          this.buscarChaves();
        },
        error: (erro: any) => {
          if (!erro.DTL) {
            toast(erro.msg);
            return;
          }
          const dtl: any = erro.DTL.shift();
          if (dtl.message == 'CHAVE_EM_PROCESSO_REIVINDICACAO') {
            toast('Chave em processo de reivindicação.');
            return;
          }
        },
      })
    );
  }

  async copiar(chave: any) {
    await Clipboard.write({ string: chave.valor });
    await toast('Chave copiada para área de transferência!');
  }

  async confirmarExcluirChave() {
    const alert = await this.alertController.create({
      header: 'Excluir chave!',
      message: 'Deseja realmente excluir essa chave?',
      buttons: [
        {
          text: 'Cancelar',
          role: 'cancel',
          handler: () => {},
        },
        {
          text: 'OK',
          role: 'confirm',
          handler: () => {
            this.excluirChave();
          },
        },
      ],
    });

    await alert.present();
  }

  async verificarSeguranca() {
    if (
      this.credencial.metodoSegurancaTransacao ==
      MetodoSegurancaEnum.NaoVerificar
    ) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: this.credencial.metodoSegurancaTransacao,
        idCredencial: this.credencial.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso(mensagemProps: any) {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: mensagemProps,
      cssClass: 'modal-half-default',
    });
    await modal.present();
  }

  async alterarReivindicacao(status: string) {
    const ativo = await this.verificarCartaoAtivo();
    if (!ativo) {
      return;
    }
    const valido = await this.verificarSeguranca();
    if (!valido) {
      return;
    }
    await loading(
      this.pixService
        .alterarStatusReivindicacao(this.idConta, status, this.chave.valor)
        .subscribe({
          next: () => {
            const mensagemSucesso = {
              titulo: 'Reivindicação de chave',
              mensagem: 'Chave pix foi alterada com sucesso. ',
              urlRetorno: `pix/${this.idConta}/chaves`,
              classeImagem: 'sucesso-2',
              tituloBotao: 'Voltar para minhas chaves',
            };
            this.apresentarSucesso(mensagemSucesso);
            this.buscarChaves();
          },
          error: (erro: any) => {
            if (!erro.DTL) {
              toast(erro.msg || 'Houve um erro inesperado. Tente novamente!');
            }
            const dtl: any = erro.DTL.shift();
            if (dtl.message == 'REIVINDICACAO_INVALIDA') {
              toast('Reivindicação Inválida.');
              return;
            }
          },
        })
    );
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial,
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  ajusteModal() {
    const screenHeight = this.platform.height();
    if (screenHeight > 471 && screenHeight < 500) {
      this.modalBreakpoint = 1;
    } else if (screenHeight > 500 && screenHeight < 600) {
      this.modalBreakpoint = 0.9;
    } else if (screenHeight > 600 && screenHeight < 700) {
      this.modalBreakpoint = 0.75;
    } else if (screenHeight > 700 && screenHeight < 800) {
      this.modalBreakpoint = 0.65;
    } else if (screenHeight > 800) {
      this.modalBreakpoint = 0.6;
    }
  }

  protected readonly environment = environment;
}
