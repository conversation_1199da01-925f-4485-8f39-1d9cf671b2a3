<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/' + idConta"><PERSON><PERSON> chaves</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <section>
    <mobile-titulo-secao>Suas chaves</mobile-titulo-secao>
    <ng-container *ngIf="chaves$ | async as chaves; else carregando">
      <ng-container *ngIf="chaves.length; else estadoVazio">
        <p>Compartilhe sua chave para receber pelo App</p>
        <ion-list class="chaves">
          <ion-item lines="none" *ngFor="let chave of chaves">
            <ion-icon class="icon-chaves" [name]="tipoIcones[chave.tipo].icon" color="secondary" slot="start"></ion-icon>
            <ion-label>
              <h2>{{ tipoIcones[chave.tipo].label }}
                <ion-badge [color]="chave.idReivindicacao ? 'quartenary' : 'danger'">{{ chave.situacao }}</ion-badge>
              </h2>
              <p>{{ chave.tipo == 'cpf' ? (chave.valor | maskHideCpfCnpj) : chave.valor }}</p>
            </ion-label>
            <ion-button *ngIf="!chave.pendente" slot="end" fill="clear" color="quartenary" size="small"
                        (click)="copiar(chave)">
              <ion-icon name="copy-outline" slot="icon-only"></ion-icon>
            </ion-button>
            <ion-button slot="end" fill="clear" size="small" id="popover-button"
                        (click)="mostrarAcoes($event, chave)">
              <ion-icon name="ellipsis-vertical" slot="icon-only" color="secondary"></ion-icon>
            </ion-button>
          </ion-item>
        </ion-list>
      </ng-container>
      <ng-template #estadoVazio>
        <p>Você ainda não possui uma chave cadastrada acesse o botão de cadastrar e selecione o tipo de chave que
          gostaria de cadastrar.</p>
      </ng-template>
    </ng-container>
    <ng-template #carregando>
      <ng-container *ngIf="!erroBuscar else estadoErro">
        <p>
          <mobile-skeleton></mobile-skeleton>
        </p>
        <ion-list class="chaves" lines="none">
          <ion-item *ngFor="let c of [1,2,3]">
            <mobile-skeleton width="30px" slot="start"></mobile-skeleton>
            <ion-label>
              <h2>
                <mobile-skeleton></mobile-skeleton>
              </h2>
              <p>
                <mobile-skeleton></mobile-skeleton>
              </p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ng-container>
      <ng-template #estadoErro>
        <article class="ion-text-center">
          <p>Houve um erro inesperado ao buscar as chaves. Acesse a opção de atualizar ou tente novamente mais tarde.</p>
          <ion-button expand="block" fill="outline" (click)="buscarChaves()">Atualizar</ion-button>
        </article>
      </ng-template>
    </ng-template>
  </section>
  <ion-modal #modal trigger="open-modal" [initialBreakpoint]="modalBreakpoint" [breakpoints]="[0, 0.5, 0.75]"  (willPresent)="ajusteModal()">
    <ng-template>
      <ion-content class="ion-padding-top content-default">
        <section class="ion-margin-top">
          <mobile-titulo-secao>Cadastrar uma chave</mobile-titulo-secao>
          <p>Qual tipo de chave você gostaria de cadastrar? </p>
          <ion-list lines="none" class="cadastrar-chaves">
            <ion-item button="true" detail="false" (click)="cadastrar('email')">
              <ion-button slot="start" color="primary">
                <ion-icon name="c-email" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>E-mail</ion-label>
            </ion-item>
            <ion-item button="true" detail="false" (click)="cadastrar('celular')">
              <ion-button slot="start" color="primary">
                <ion-icon name="recarga-celular" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>Celular</ion-label>
            </ion-item>
            <ion-item button="true" detail="false" (click)="cadastrar('cpf')">
              <ion-button slot="start" color="primary">
                <ion-icon name="documento" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>CPF</ion-label>
            </ion-item>
            <ion-item button="true" detail="false" (click)="cadastrar('aleatoria')">
              <ion-button slot="start" color="primary">
                <ion-icon name="security" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>Aleatória</ion-label>
            </ion-item>
          </ion-list>
          <p class="mensagem-instituicao">Ao cadastrar sua chave Pix neste aplicativo, você estará vinculando-a à VALLOO S.A. INSTITUICAO
            DE PAGAMENTO com Cnpj 25.165.266/0001-15, cadastrada no Banco Central. Essa configuração não altera sua relação
            com a {{environment.appName}} para outros serviços.</p>
        </section>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-popover #popover [isOpen]="isOpen" (didDismiss)="isOpen = false" [dismissOnSelect]="true">
    <ng-template>
      <ion-content class="popover-content">
        <ion-list>
          <ion-item *ngIf="!chave.pendente" lines="none" [button]="true" [detail]="false" (click)="confirmarExcluirChave()">
            <ion-icon name="trash-outline" slot="start"></ion-icon>
            <ion-label>
              Excluir
            </ion-label>
          </ion-item>
          <ion-item lines="full" [button]="true" [detail]="false" *ngIf="chave.pendente && !chave.idReivindicacao" (click)="alterarReivindicacao('confirmar')">
            <ion-icon name="checkmark-circle-outline" slot="start"></ion-icon>
            <ion-label>
              Confirmar reivindicação
            </ion-label>
          </ion-item>
          <ion-item lines="full" [button]="true" [detail]="false" *ngIf="chave.pendente" (click)="alterarReivindicacao('concluir')">
            <ion-icon name="checkmark-circle-outline" slot="start"></ion-icon>
            <ion-label>
              Concluir reivindicação
            </ion-label>
          </ion-item>
          <ion-item lines="none" [button]="true" [detail]="false" *ngIf="chave.pendente" (click)="alterarReivindicacao('cancelar')">
            <ion-icon name="close-circle-outline" slot="start"></ion-icon>
            <ion-label>
              Cancelar reivindicação
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-content>
    </ng-template>
  </ion-popover>
</ion-content>
<ion-footer>
  <ion-button class="btn" id="open-modal" expand="block" [disabled]="erroBuscar">Cadastrar</ion-button>
  <div class="div-image">
    <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
  </div>
</ion-footer>
