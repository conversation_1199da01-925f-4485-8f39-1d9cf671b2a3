section {
  margin-bottom: 1.5rem;

  p {
    color: var(--ion-color-gray-50);
    font-size: 16px;
    font-weight: 300;
  }
}

.chaves {
  background: transparent;

  ion-item {
    --border-radius: 16px;
    --background: var(--ion-color-medium);
    margin-bottom: 1rem;
    --padding-start: 1rem;

    ion-icon {
      margin: 0 8px 0 0;

    }

    .icon-chaves {
      color: var(--ion-color-primary);
    }

    ion-label {
      h2 {
        color: var(--ion-color-label);
        font-size: 16px;
        font-weight: 600;
        line-height: normal;
      }

      margin-right: 0;

      ion-badge {
        font-size: 10px;
        margin-left: 0.5rem;
      }

      p {
        color: var(--ion-color-gray-50);
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    ion-button {
      margin: 0;
      --padding-start: 0;
      --padding-end: 0;
    }
  }
}

.cadastrar-chaves {
  background: transparent;
  margin-top: 1rem;

  ion-item {
    --background: transparent;
    margin-bottom: 1rem;
    --padding-start: 0;

    ion-label {
      color: var(--ion-color-gray-50);
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-left: 1rem;
    }

    ion-button {
      --border-radius: 50%;
      width: 30px;
      height: 30px;
    }
  }
}

.popover-content {
  --padding-start: 0;
  --padding-end: 0;

  ion-item {
    --padding-start: 8px;
    --padding-end: 8px;

    ion-icon {
      margin: 0 8px 0 0;
      font-size: 25px;
    }

    ion-label {
      color: var(--ion-color-gray-200);
      font-size: 14px;
      font-weight: 600;
      line-height: normal;
      margin: 0;
    }
  }
}

ion-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
}

ion-modal {
  --border-radius: 1.5rem;
}

.div-image {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.imagem-valloo {
  width: 40%;
}

.mensagem-instituicao{
  font-size: 10px !important;
}
