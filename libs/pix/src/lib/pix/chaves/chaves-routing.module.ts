import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ChavesComponent } from './chaves.component';
import { CadastrarComponent } from './cadastrar/cadastrar.component';

const routes: Routes = [
  {
    path: '',
    component: ChavesComponent
  },
  {
    path: 'cadastrar/:tipo',
    component: CadastrarComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ChavesRoutingModule {
}
