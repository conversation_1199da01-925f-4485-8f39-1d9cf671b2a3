<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'pix/'+idConta+'/chaves'">Cadastrar chave</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <form [formGroup]="formChave">
    <ion-item lines="none">
      <ion-input labelPlacement="stacked" [placeholder]="placeholder" formControlName="chave" [type]="typeInput"
                 errorText="Informe um valor válido" [maskito]="maskOptions" [maskitoElement]="maskPredicate"
                 helperText="Ao cadastrar sua chave Pix neste aplicativo, você estará vinculando-a à VALLOO S.A. INSTITUICAO DE
                 PAGAMENTO com Cnpj 25.165.266/0001-15, cadastrada no Banco Central. Essa configuração não altera sua relação
                 com a {{environment.appName}} para outros serviços.">
        <div slot="label" class="custom-label">{{ label }}</div>
      </ion-input>
    </ion-item>
  </form>

  <ion-modal #modal trigger="open-modal" [canDismiss]="fecharModalPortabilidade" [initialBreakpoint]="0.4"
             [breakpoints]="[0,0.4, 0.5, 0.75]" (willDismiss)="onWillDismiss($event)">
    <ng-template>
      <ion-content class="ion-padding-top content-default">
        <section class="ion-margin-top">
          <mobile-titulo-secao>Cadastro de chave pix</mobile-titulo-secao>
          <p>Essa chave está cadastrada em outra instituição, para cadastrá-la é necessário fazer a portabilidade, o
            tempo de conclusão é 7 dias.</p>
        </section>
        <ion-button expand="block" class="ion-margin-bottom" (click)="fazerPortabilidade()">Fazer portabilidade
        </ion-button>
        <ion-button expand="block" fill="outline" (click)="fecharModal()">Cadastrar outra chave</ion-button>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>

<ion-footer>
  <ion-button class="btn" id="open-modal" expand="block" [disabled]="formChave.invalid" (click)="cadastrar()">Cadastrar</ion-button>
</ion-footer>
