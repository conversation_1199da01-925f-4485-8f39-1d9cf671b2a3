import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { cpfMask, noneMask, phoneMask } from '@utils/masks.util';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ValidatorsApp } from '@utils/validators.util';
import {
  ModalAtivarCartaoComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent,
} from '@corporativo/modals';
import { IonModal, ModalController } from '@ionic/angular';
import {
  AuthService,
  Credencial,
  environment,
  MetodoSegurancaEnum,
  PixService,
  TipoChaveEnum,
  Usuario,
} from '@corporativo/shared';
import { toast } from '@utils/toast.util';
import { loading } from '@utils/loading.util';

@Component({
  selector: 'mobile-cadastrar',
  templateUrl: './cadastrar.component.html',
  styleUrl: './cadastrar.component.scss',
  standalone: false,
})
export class CadastrarComponent implements OnInit {
  tipo!: string;
  label = '';
  placeholder = '';
  typeInput = 'text';
  maskOptions: MaskitoOptions = noneMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();
  formChave = new FormGroup({
    chave: new FormControl('', [Validators.required]),
  });
  usuario!: Usuario;
  tipoId!: number;
  @ViewChild(IonModal) modalPortabilidade!: IonModal;
  fecharModalPortabilidade = false;
  idConta!: number;
  credencial!: Credencial;

  constructor(
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController,
    private authService: AuthService,
    private router: Router,
    private pixService: PixService
  ) {
    this.usuario = authService.getUser();
  }

  ngOnInit() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      // @ts-ignore
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        return conta ? x : null;
      });
    }
    this.tipo = this.activatedRoute.snapshot.paramMap.get('tipo') || '';
    switch (this.tipo) {
      case 'email': {
        this.label = 'E-mail';
        this.placeholder = '<EMAIL>';
        this.maskOptions = noneMask;
        this.typeInput = 'text';
        this.formChave
          .get('chave')
          ?.setValidators([Validators.required, Validators.email]);
        this.tipoId = TipoChaveEnum.Email;
        break;
      }
      case 'celular': {
        this.label = 'Celular';
        this.placeholder = '(00) 0 0000-0000';
        this.maskOptions = phoneMask;
        this.typeInput = 'tel';
        this.formChave
          .get('chave')
          ?.setValidators([Validators.required, Validators.minLength(16)]);
        this.tipoId = TipoChaveEnum.Telefone;
        break;
      }
      case 'cpf': {
        this.label = 'CPF';
        this.placeholder = '000.000.000-00';
        this.maskOptions = cpfMask;
        this.typeInput = 'tel';
        this.formChave
          .get('chave')
          ?.setValidators([Validators.required, ValidatorsApp.cpf()]);
        this.formChave.get('chave')?.setValue(this.usuario.documento);
        this.formChave.get('chave')?.disable();
        this.tipoId = TipoChaveEnum.CPF;
        this.cadastrar();
        break;
      }
      case 'aleatoria': {
        this.label = 'Será gerado uma chave aleatória';
        this.placeholder = 'Valor gerado aleatoriamente';
        this.maskOptions = noneMask;
        this.typeInput = 'text';
        this.formChave.get('chave')?.setValidators([Validators.required]);
        this.formChave.get('chave')?.disable();
        this.tipoId = TipoChaveEnum.Automatica;
        this.cadastrar();
        break;
      }
    }
  }

  ionViewWillEnter() {}

  async cadastrar() {
    const ativo = await this.verificarCartaoAtivo();
    if (!ativo) {
      return;
    }
    const valido = await this.verificarSeguranca();
    if ((this.tipo == 'aleatoria' || this.tipo == 'cpf') && valido == null) {
      await this.router.navigate([`pix/${this.idConta}/chaves`]);
      return;
    }
    if (!valido) {
      return;
    }
    const valor = this.formChave.getRawValue().chave || '';
    await loading(
      this.pixService.incluirChave(this.usuario, this.tipoId, this.credencial, this.idConta, valor).subscribe({
          next: () => {
            const mensagemSucesso = {
              titulo: 'Chave pix',
              mensagem:
                'Sua chave pix foi cadastrada com sucesso e você já pode receber pagamentos através da chave pix. ',
              urlRetorno: `pix/${this.idConta}/chaves`,
              classeImagem: 'sucesso-2',
            };
            this.apresentarSucesso(mensagemSucesso);
          },
          error: (erro: any) => {
            if (!erro.DTL) {
              toast(erro.msg);
              this.router.navigate([`pix/${this.idConta}/chaves`]);
              return;
            }
            const dtl: any = erro.DTL.shift();
            if (
              [
                'EXISTENTE_OUTRO_DONO',
                'EXISTENTE_MESMO_DONO_OUTRO_PSP',
              ].includes(dtl.message)
            ) {
              this.modalPortabilidade.isOpen = true;
              return;
            }
            const mensagens: any = {
              CHAVE_EM_PROCESSO_REIVINDICACAO:
                'Chave em processo de reivindicação.',
              REIVINDICACAO_JA_EXISTE: 'Reivindicação já existe.',
              LIMITE_EXCEDIDO: 'Limite excedido de chaves cadastradas.',
              CHAVE_REIVINDICADA: 'Chave Reivindicada.',
              EXISTENTE_MESMO_DONO_E_PSP:
                'Chave existente e já vinculada a sua conta.',
              EXISTENTE_MESMO_DONO_PSP:
                'Chave existente e já vinculada a sua conta.',
            };
            toast(
              mensagens[dtl.message] == null
                ? dtl.message
                : mensagens[dtl.message]
            );
            this.router.navigate([`pix/${this.idConta}/chaves`]);
          },
        })
    );
  }

  async verificarSeguranca() {
    if (!this.credencial) {
      return;
    }
    if (
      this.credencial.metodoSegurancaTransacao ==
      MetodoSegurancaEnum.NaoVerificar
    ) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: this.credencial.metodoSegurancaTransacao,
        idCredencial: this.credencial.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso(mensagemProps: any) {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: mensagemProps,
      cssClass: 'modal-half-default',
    });
    await modal.present();
  }

  async fazerPortabilidade() {
    const valor = this.formChave.getRawValue().chave || '';
    await loading(
      this.pixService
        .reivindicarChave(
          this.usuario,
          this.tipoId,
          this.credencial,
          this.idConta,
          valor
        )
        .subscribe({
          next: () => {
            this.fecharModal();
            const mensagemSucesso = {
              titulo: 'A reivindicação enviada!',
              mensagem:
                'A pessoa que está usando a chave Pix receberá a reivindicação e terá 14 dias para aceitar ou recusar. Se não houver resposta nesse período ser aceita automaticamente. ',
              urlRetorno: `pix/${this.idConta}/chaves`,
              classeImagem: 'sucesso-2',
            };
            this.apresentarSucesso(mensagemSucesso);
          },
          error: (erro: any) => {
            const dtl: any = erro.DTL.shift();
            const mensagens: any = {
              LIMITE_EXCEDIDO: 'Limite excedido de chaves cadastradas.',
              REIVINDICACAO_JA_EXISTE: 'Reivindicação já existe.',
              CHAVE_EM_PROCESSO_REIVINDICACAO:
                'Chave em processo de reivindicação.',
              CHAVE_REIVINDICADA: 'Chave reivindicada.',
            };
            if (Object.keys(mensagens).includes(dtl.message)) {
              toast(mensagens[dtl.message]);
              return;
            } else {
              toast(erro.message);
            }
            this.fecharModal();
          },
        })
    );
  }

  fecharModal() {
    this.fecharModalPortabilidade = true;
    this.modalPortabilidade.dismiss();
  }

  onWillDismiss(event: Event) {
    this.router.navigate([`pix/${this.idConta}/chaves`]);
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial,
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  protected readonly environment = environment;
}
