import { NgModule } from '@angular/core';
import { PixRoutingModule } from './pix-routing.module';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { SaldoModule, SaldoSimplesModule } from '@corporativo/saldo';
import { ButtonModule, CartoesModule, TitleToolbarModule, TituloSecaoModule, } from '@corporativo/components';
import { PixComponent } from './pix.component';
import { LerQrcodeComponent } from './ler-qrcode/ler-qrcode.component';
import { DirectivesModule, PipesModule } from '@corporativo/shared';

@NgModule({
  declarations: [PixComponent, LerQrcodeComponent],
	imports: [
		CommonModule,
		IonicModule,
		PixRoutingModule,
		HttpClientModule,
		SaldoModule,
		TitleToolbarModule,
		TituloSecaoModule,
		ButtonModule,
		SaldoSimplesModule,
		PipesModule,
		CartoesModule,
    DirectivesModule
	],
  exports: [PixComponent],
})
export class PixModule {
}
