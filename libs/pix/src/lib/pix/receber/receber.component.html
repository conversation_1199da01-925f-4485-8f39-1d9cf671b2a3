<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/' + idConta">Re<PERSON>ber pelo Pix</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <form [formGroup]="formReceber" class="ion-margin-top">
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="0,00" type="tel" formControlName="valor"
                   errorText="Informe um valor válido" [brmasker]="{money: true, thousand: '.',  decimalCaracter: ',', decimal: 2}">
          <div slot="label" class="custom-label">Valor (Opcional)</div>
          <ion-text slot="start">R$</ion-text>
        </ion-input>
      </ion-item>
      <ion-item class="ion-margin-bottom">
        @if (chaves$ | async; as chaves) {
          <ion-select label="Chave" formControlName="chave" interface="popover" placeholder="Selecione"
                      label-placement="stacked" [compareWith]="compareWith"
                      toggleIcon="chevron-down-outline" expandedIcon="chevron-up-outline">
            @for (chave of chaves$ | async; track chave.chaveId) {
              <ion-select-option [value]="chave">{{ chave.chave.valor }}</ion-select-option>
            }
          </ion-select>
        } @else {
          <ion-spinner name="dots"></ion-spinner>
        }
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Digite uma descrição" type="text" formControlName="descricao"
                   maxlength="40" [maskito]="textMaskOptions" [maskitoElement]="maskPredicate" counter>
          <div slot="label" class="custom-label">Descrição (Opcional)</div>
        </ion-input>
      </ion-item>
    </form>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="gerar()" [disabled]="formReceber.invalid">Gerar QR Code</ion-button>
  <div class="div-image">
    <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
  </div>
</ion-footer>
