import { Component, ElementRef, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Clipboard } from '@capacitor/clipboard';
import { toast } from '@utils/toast.util';
import { SafeUrl } from '@angular/platform-browser';
import { Platform } from '@ionic/angular';
import { NgxCaptureService } from 'ngx-capture';
import { Share } from '@capacitor/share';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { loading } from '@utils/loading.util';
import * as QRCode from 'qrcode';
import { FileOpener } from '@capacitor-community/file-opener';
import { environment, LogoInstituicaoEnum } from '@corporativo/shared';

@Component({
  selector: 'mobile-qrcode',
  templateUrl: './qrcode.component.html',
  styleUrl: './qrcode.component.scss',
  standalone: false,
})
export class QrcodeComponent {
  qrcode!: string;
  dados: any;
  qrCodeDownloadLink: any;
  @ViewChild('screen', { static: true }) screen!: ElementRef;
  @ViewChild('qrcodeView', { static: true }) qrcodeView!: ElementRef;
  idConta!: number;
  logo =
    LogoInstituicaoEnum[environment.app as keyof typeof LogoInstituicaoEnum];

  constructor(
    private router: Router,
    private platform: Platform,
    private captureService: NgxCaptureService,
    private activatedRoute: ActivatedRoute
  ) {
    const data: any = this.router.getCurrentNavigation()?.extras?.state;
    if (data) {
      this.qrcode = data.qrcode;
      this.dados = data.dados;
    }
  }

  async ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
    await QRCode.toCanvas(this.qrcodeView.nativeElement, this.qrcode);
  }

  onChangeURL(url: SafeUrl) {
    this.qrCodeDownloadLink = url;
  }

  async copiar() {
    await Clipboard.write({ string: this.qrcode });
    await toast('Link copiado para área de transferência!');
  }

  async compartilhar() {
    if (!this.platform.is('hybrid')) {
      return;
    }
    await loading(
      this.captureService.getImage(this.screen.nativeElement, true).subscribe({
        next: async (imageBase64) => {
          const image = imageBase64.replace('data:image/png;base64,', '');
          const result = await Filesystem.writeFile({
            path: `qrcode.png`,
            data: image,
            directory: Directory.Documents,
          });
          await Share.share({
            title: 'Valloo QR code',
            text: `Para me transferir pela conta da Valloo ou de outros bancos pelo Pix`,
            dialogTitle: 'Compartilhar',
            url: result.uri,
          });
        },
        error: async () => {
          await toast(
            'Houve um erro inesperado ao compartilhar imagem. Tente novamente!'
          );
        },
      })
    );
  }

  async salvar() {
    if (!this.platform.is('hybrid')) {
      return;
    }
    await loading(
      this.captureService
        .getImage(this.qrcodeView.nativeElement, true)
        .subscribe({
          next: async (imageBase64) => {
            const image = imageBase64.replace('data:image/png;base64,', '');
            const result = await Filesystem.writeFile({
              path: `qrcode.png`,
              data: image,
              directory: Directory.Documents,
            });
            FileOpener.open({ filePath: result.uri })
              .then(() => console.log('Arquivo foi aberto'))
              .catch((e: any) => console.log('Erro ao abrir arquivo', e));
            await toast('Imagem salva com sucesso!');
          },
          error: async () => {
            await toast(
              'Houve um erro inesperado ao salvar a imagem. Tente novamente!'
            );
          },
        })
    );
  }
}
