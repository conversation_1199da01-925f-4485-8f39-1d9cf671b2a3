<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+ idConta">QR Code</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <div #screen class="screen">
      <article class="qrcode">
        <canvas #qrcodeView></canvas>
      </article>
      <mobile-titulo-secao>Informações do QR Code</mobile-titulo-secao>
      <article class="info">
        <div class="info">
          <p>Valor</p>
          <p><b>{{ dados.valor | currency:'BRL' }}</b></p>
        </div>
        <div>
          <p>Chave</p>
          <p><b>{{ dados.chave.chave.valor }}</b></p>
        </div>
        <div>
          <p>Nome</p>
          <p><b>{{ dados.chave.pessoa.nome | titlecase }}</b></p>
        </div>
        <div>
          <p>Instituição</p>
          <p><b>{{ dados.chave.conta.nomeParticipante }}</b></p>
        </div>
        <div>
          <p>Descrição</p>
          <p><b>{{ dados.descricao || '-' }}</b></p>
        </div>
      </article>

    <div class="div-image">
      <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
    </div>
  </div>

  <ion-modal #modal trigger="open-modal" [initialBreakpoint]="0.3" [breakpoints]="[0, 0.3]">
    <ng-template>
      <ion-content class="ion-padding-top content-default">
        <section class="ion-margin-top">
          <ion-list lines="none" class="opcoes">
            <ion-item button="true" detail="true" (click)="copiar()">
              <ion-button slot="start" color="secondary" aria-readonly="true">
                <ion-icon name="copy-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>Copiar código Pix copia e cola</ion-label>
            </ion-item>
            <ion-item button="true" detail="true" (click)="compartilhar()">
              <ion-button slot="start" color="secondary">
                <ion-icon name="arrow-redo-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>Compartilhar imagem</ion-label>
            </ion-item>
            <ion-item button="true" detail="true" (click)="salvar()">
              <ion-button slot="start" color="secondary">
                <ion-icon name="image-outline" slot="icon-only"></ion-icon>
              </ion-button>
              <ion-label>Salvar imagem</ion-label>
            </ion-item>
          </ion-list>
        </section>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
<ion-footer class="ion-no-border">
  <ion-toolbar>
    <ion-button expand="block" class="btn ion-margin-bottom" id="open-modal">Compartilhar</ion-button>
    <ion-button expand="block" [routerLink]="'/pix/'+ idConta" fill="outline">Fechar</ion-button>
  </ion-toolbar>
</ion-footer>
