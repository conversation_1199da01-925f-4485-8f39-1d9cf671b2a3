ion-footer {
  ion-toolbar {
    padding: 1rem 1.5rem;
  }
}

//.screen {
//  padding: 1rem;
//}

.qrcode {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.info {
  margin-top: 1.5rem;

  div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
  }

  p {
    color: var(--ion-color-gray-50);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    margin: 0;
    text-align: end;

    b {
      font-size: 16px;
      color: var(--ion-color-primary);
      font-weight: 500;
    }
  }
}

ion-modal {
  --border-radius: 1.5rem;
}

.opcoes {
  background: transparent;
  margin-top: 1rem;

  ion-item {
    --background: transparent;
    margin-bottom: 1rem;
    --padding-start: 0;

    ion-label {
      color: var(--ion-color-gray-50);
      font-size: 16px;
      font-weight: 600;
      line-height: normal;
      margin-left: 1rem;
    }

    ion-button {
      --border-radius: 50%;
      width: 30px;
      height: 30px;
    }
  }
}

img {
  width: 50%;
  margin-top: 2rem;
}

canvas {
  border: solid 1px var(--ion-color-medium);
  border-radius: 8px;
}

.imagem-valloo {
  width: 40%;
}

.div-image {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

