import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ReceberComponent } from './receber.component';
import { QrcodeComponent } from '@corporativo/pix';

const routes: Routes = [
  {
    path: '',
    component: ReceberComponent
  },
  {
    path: 'qrcode',
    component: QrcodeComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReceberRoutingModule {
}
