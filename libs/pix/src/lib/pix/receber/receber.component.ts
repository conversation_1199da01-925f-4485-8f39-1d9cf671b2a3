import { Component } from '@angular/core';
import { textMask } from '@utils/masks.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import {
  AuthService,
  Credencial,
  PixService,
  Usuario,
} from '@corporativo/shared';
import { Observable } from 'rxjs';
import { loading } from '@utils/loading.util';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController, Platform } from '@ionic/angular';
import { toast } from '@utils/toast.util';
import {
  ModalAtencaoComponent,
  ModalEnderecoComponent,
} from '@corporativo/modals';

@Component({
  selector: 'mobile-receber',
  templateUrl: './receber.component.html',
  styleUrls: ['./receber.component.scss'],
  standalone: false,
})
export class ReceberComponent {
  readonly textMaskOptions: MaskitoOptions = textMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();
  usuario!: Usuario;
  chaves$!: Observable<any[]>;
  idConta!: number;
  formReceber = new FormGroup({
    valor: new FormControl(''),
    chave: new FormControl('', [Validators.required]),
    descricao: new FormControl(''),
  });
  credencial!: Credencial;

  constructor(
    private authService: AuthService,
    private pixService: PixService,
    private router: Router,
    private platform: Platform,
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController
  ) {
    this.usuario = this.authService.getUser();
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      // @ts-ignore
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        return conta ? x : null;
      });
      this.buscarChaves();
    }
  }

  compareWith(o1: any, o2: any) {
    return o1 && o2 ? o1.chaveId === o2.chaveId : o1 === o2;
  }

  buscarChaves() {
    this.chaves$ = this.pixService.buscarChaves(this.idConta);
  }

  async gerar() {
    const dados: any = this.formReceber.getRawValue();
    dados.valor = +dados.valor
      .replace('R$ ', '')
      .replace(/\./g, '')
      .replace(/,/g, '.');

    if (this.usuario.enderecoResidencial == null || !this.usuario.enderecoResidencial.dtHrConfirmacao) {
      await this.abrirModalAtencao();
      return;
    }
    await loading(
      this.pixService.gerarQrCode(this.usuario, dados).subscribe({
        next: (retorno) => {
          this.router.navigate([`pix/${this.idConta}/receber/qrcode`], {
            state: {
              qrcode: retorno.value.qrCode.valor,
              dados,
            },
          });
        },
        error: (erro) => {
          toast(
            'Houve um erro inesperado ao gerar o QR Code. Tente novamente!'
          );
        },
      })
    );
  }

  async abrirModalAtencao() {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Atualizar endereço',
        mensagem:
          'Seus dados de endereço estão desatualizados, para gerar um QR Code é necessário atualizar.',
        tituloBotaoPrimario: 'Atualizar',
        tituloBotaoSecundario: 'Fechar',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.modalController.dismiss();
      await this.atualizarEndereco();
    }
    if (data.role == 'secundaria') {
      await this.modalController.dismiss();
    }
  }

  async atualizarEndereco() {
    const modal = await this.modalController.create({
      component: ModalEnderecoComponent,
      componentProps: {
        credencial: this.credencial,
        tituloSecao: 'Atualize seu endereço',
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data) {
      this.usuario = this.authService.getUser();
    }
  }
}
