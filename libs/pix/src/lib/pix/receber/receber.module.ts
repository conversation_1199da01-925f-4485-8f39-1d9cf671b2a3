import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReceberComponent } from './receber.component';
import { ReceberRoutingModule } from './receber-routing.module';
import { IonicModule } from '@ionic/angular';
import { SaldoModule, SaldoSimplesModule } from '@corporativo/saldo';
import { TitleToolbarModule, TituloSecaoModule } from '@corporativo/components';
import { ReactiveFormsModule } from '@angular/forms';
import { DirectivesModule, PipesModule } from '@corporativo/shared';
import { QrcodeComponent } from '@corporativo/pix';
import { NgxCaptureModule } from 'ngx-capture';
import { MaskitoDirective } from '@maskito/angular';

@NgModule({
  declarations: [
    ReceberComponent,
    QrcodeComponent
  ],
  imports: [
    CommonModule,
    ReceberRoutingModule,
    IonicModule,
    SaldoModule,
    TitleToolbarModule,
    ReactiveFormsModule,
    SaldoSimplesModule,
    PipesModule,
    TituloSecaoModule,
    NgxCaptureModule,
    DirectivesModule,
    MaskitoDirective
  ]
})
export class ReceberModule {
}
