<article class="favorecido">
  <ion-item lines="none">
    <div class="iniciais" slot="start">
      <p>{{ favorecido?.nome | iniciais:'first' }}</p>
    </div>
    <ion-label>
      <h2>{{ favorecido?.nome | titlecase }}</h2>
      <ng-container *ngIf="favorecido?.chave; else agenciaConta">
        <p>Chave: {{ favorecido?.chave }}</p>
      </ng-container>
      <ng-template #agenciaConta>
        <p>Agência: {{ favorecido?.agencia }}</p>
        <p>Conta: {{ favorecido?.conta }}</p>
      </ng-template>
      <p>{{ favorecido?.instituicao }}</p>
      <p>{{ favorecido?.documento | maskHideCpfCnpj }}</p>
    </ion-label>
  </ion-item>
</article>
