.favorecido {
  margin-bottom: 2rem;

  ion-item {
    --background: transparent;
    --padding-start: 0;
  }

  ion-label {
    margin: 0;
  }

  h2 {
    color: var(--ion-color-gray-50);
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--ion-color-gray-50);
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 3px;
  }
}

.iniciais {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--ion-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 1rem 0 0;
  position: relative;

  p {
    margin: 0;
    color: var(--ion-color-secondary-contrast);
    letter-spacing: 1px;
    font-size: 30px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-transform: uppercase;
  }
}
