import { Component } from '@angular/core';
import { Observable } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService, Limite, PixService } from '@corporativo/shared';

@Component({
  selector: 'mobile-limites',
  templateUrl: './limites.component.html',
  styleUrls: ['./limites.component.scss'],
  standalone: false,
})
export class LimitesComponent {
  limite$!: Observable<Limite>;
  usuario!: any;
  idConta!: number;

  constructor(
    private pixService: PixService,
    private router: Router,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute
  ) {}

  ionViewWillEnter() {
    this.usuario = this.authService.getUser();
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
    this.limite$ = this.pixService.buscarLimite(this.idConta);
  }

  alterar(tipo: string, limite: Limite) {
    return this.router.navigate([`pix/${this.idConta}/limites/alterar`], {
      state: { limite, tipo },
    });
  }
}
