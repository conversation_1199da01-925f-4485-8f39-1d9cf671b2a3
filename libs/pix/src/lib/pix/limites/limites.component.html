<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/' + idConta">Meus limites Pix</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <ng-container *ngIf="limite$ | async as limite; else carregando">
    <section class="limites">
      <mobile-titulo-secao>Diurno</mobile-titulo-secao>
      <article class="limite">
        <div>
          <p><b>Por transação</b></p>
          <p>{{limite.diurnoPorTransacao | currency:'BRL'}}</p>
        </div>
        @if (limite.statusLimiteDiurnoPorTransacao == 'PENDENTE') {
          <ion-chip color="secondary">Em análise</ion-chip>
        } @else {
          <ion-button color="primary" size="small" (click)="alterar('diurno-transacao', limite)">
            Alterar limite
          </ion-button>
        }
      </article>
      <article class="limite">
        <div>
          <p><b>Por período</b></p>
          <p>{{limite.diurnoMaximo | currency:'BRL'}}</p>
        </div>
        @if (limite.statusLimiteDiurnoMaximo == 'PENDENTE') {
          <ion-chip color="secondary">Em análise</ion-chip>
        } @else {
          <ion-button color="primary" size="small" (click)="alterar('diurno-periodo', limite)">
            Alterar limite
          </ion-button>
        }
      </article>
    </section>
    <section class="limites">
      <mobile-titulo-secao>Noturno</mobile-titulo-secao>
      <article class="limite">
        <div>
          <p><b>Por transação</b></p>
          <p>{{limite.noturnoPorTransacao | currency:'BRL'}}</p>
        </div>
        @if (limite.statusLimiteNoturnoPorTransacao == 'PENDENTE') {
          <ion-chip color="secondary">Em análise</ion-chip>
        } @else {
          <ion-button color="primary" size="small" (click)="alterar('noturno-transacao', limite)">
            Alterar limite
          </ion-button>
        }
      </article>
      <article class="limite">
        <div>
          <p><b>Por período</b></p>
          <p>{{limite.noturnoMaximo | currency:'BRL'}}</p>
        </div>
        @if (limite.statusLimiteNoturnoMaximo == 'PENDENTE') {
          <ion-chip color="secondary">Em análise</ion-chip>
        } @else {
          <ion-button color="primary" size="small" (click)="alterar('noturno-periodo', limite)">
            Alterar limite
          </ion-button>
        }
      </article>
    </section>
  </ng-container>
  <ng-template #carregando>
    <section>
      <mobile-titulo-secao>Diurno</mobile-titulo-secao>
      <article class="limite">
        <div style="width: 100%">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
        <div style="width: 100%" class="ion-margin-start">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
      </article>
      <article class="limite">
        <div style="width: 100%">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
        <div style="width: 100%" class="ion-margin-start">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
      </article>
    </section>
    <section>
      <mobile-titulo-secao>Noturno</mobile-titulo-secao>
      <article class="limite">
        <div style="width: 100%">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
        <div style="width: 100%" class="ion-margin-start">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
      </article>
      <article class="limite">
        <div style="width: 100%">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
        <div style="width: 100%" class="ion-margin-start">
          <mobile-skeleton width="100%" height="20px"></mobile-skeleton>
        </div>
      </article>
    </section>
  </ng-template>
</ion-content>
