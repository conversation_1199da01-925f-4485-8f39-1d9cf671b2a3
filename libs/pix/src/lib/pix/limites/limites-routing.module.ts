import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LimitesComponent } from './limites.component';

const routes: Routes = [
  {
    path: '',
    component: LimitesComponent
  },
  {
    path: 'alterar',
    loadChildren: () => import('./alterar/alterar.module').then(m => m.AlterarModule)
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LimitesRoutingModule {
}
