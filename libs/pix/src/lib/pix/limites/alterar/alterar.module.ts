import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AlterarRoutingModule } from './alterar-routing.module';
import { AlterarComponent } from './alterar.component';
import { IonicModule } from '@ionic/angular';
import { ReactiveFormsModule } from '@angular/forms';
import { TitleToolbarModule } from '@corporativo/components';
import {
  ModalSenhaCartaoModule,
  ModalSucessoModule,
} from '@corporativo/modals';
import { DirectivesModule } from '@corporativo/shared';
import { MaskitoDirective } from '@maskito/angular';

@NgModule({
  declarations: [AlterarComponent],
  imports: [
    CommonModule,
    AlterarRoutingModule,
    IonicModule,
    TitleToolbarModule,
    ReactiveFormsModule,
    ModalSenhaCartaoModule,
    ModalSucessoModule,
    DirectivesModule,
    MaskitoDirective,
  ],
})
export class AlterarModule {}
