import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { ModalController } from '@ionic/angular';
import { moneyMask } from '@utils/masks.util';
import {
  ModalAtivarCartaoComponent,
  ModalErroComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent,
} from '@corporativo/modals';
import {
  AuthService,
  Credencial,
  Limite,
  MetodoSegurancaEnum,
  PixService,
} from '@corporativo/shared';
import { loading } from '@utils/loading.util';
import { notEqualValidator } from '@utils/validators.util';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'vlo-alterar',
  templateUrl: './alterar.component.html',
  styleUrls: ['./alterar.component.scss'],
  standalone: false,
})
export class AlterarComponent {
  subtitulo = '';
  label = '';
  tipo!: string;
  limites!: Limite;
  explicacao!: string;
  limite: any;
  usuario!: any;
  formLimite = new FormGroup({
    valor: new FormControl(0, Validators.required),
    valorFormatado: new FormControl('', Validators.required),
  });
  idConta!: number;
  credencial!: Credencial;
  conta: any;
  readonly moneyMaskOptions: MaskitoOptions = moneyMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();

  constructor(
    private router: Router,
    private modalController: ModalController,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private pixService: PixService
  ) {
    this.usuario = authService.getUser();
    const data: any = router.getCurrentNavigation()?.extras?.state;
    if (data) {
      this.limites = data.limite;
      this.tipo = data.tipo;
      switch (this.tipo) {
        case 'diurno-transacao': {
          this.subtitulo = 'transação';
          this.label = 'Por transação (diurno)';
          this.explicacao =
            'Esse é o valor máximo que você pode fazer por transação durante o período diurno (6h às 20h).';
          this.limite = {
            atual: this.limites.diurnoPorTransacao,
            maximo: this.limites.instituicaoDiurnoMaximo,
            minimo: 0.01,
            periodo: 'Diurno',
            tipo: 1,
          };
          break;
        }
        case 'diurno-periodo': {
          this.subtitulo = 'período';
          this.label = 'Por período (diurno)';
          this.explicacao =
            'Esse é o valor máximo da soma de todos os envios que você pode fazer durante o período diurno (6h às 20h).';
          this.limite = {
            atual: this.limites.diurnoMaximo,
            maximo: this.limites.instituicaoDiurnoMaximo,
            minimo: 0.01,
            periodo: 'Diurno',
            tipo: 0,
          };
          break;
        }
        case 'noturno-periodo': {
          this.subtitulo = 'período';
          this.label = 'Por período (noturno)';
          this.explicacao =
            'Esse é o valor máximo da soma de todos os envios que você pode fazer durante o período noturno (20h às 6h).';
          this.limite = {
            atual: this.limites.noturnoMaximo,
            maximo: this.limites.instituicaoNoturnoMaximo,
            minimo: 0.01,
            periodo: 'Noturno',
            tipo: 2,
          };
          break;
        }
        case 'noturno-transacao': {
          this.subtitulo = 'transação';
          this.label = 'Por transação (noturno)';
          this.explicacao =
            'Esse é o valor máximo que você pode fazer por transação durante o período noturno (20h às 6h).';
          this.limite = {
            atual: this.limites.noturnoPorTransacao,
            maximo: this.limites.instituicaoNoturnoMaximo,
            minimo: 0.01,
            periodo: 'Noturno',
            tipo: 3,
          };
          break;
        }
      }
      const currencyPipe = new CurrencyPipe('pt-BR');
      this.formLimite
        .get('valorFormatado')
        ?.setValue(currencyPipe.transform(this.limite.atual));
      this.formLimite
        .get('valor')
        ?.setValidators([
          Validators.min(0.01),
          Validators.max(this.limite.maximo),
          notEqualValidator(this.limite.atual),
        ]);
      this.formLimite.get('valorFormatado')?.valueChanges.subscribe((x) => {
        if (!x) {
          return;
        }
        const valor = +x
          .replace('R$ ', '')
          .replace(/\./g, '')
          .replace(/,/g, '.');
        this.formLimite.get('valor')?.setValue(valor);
      });
    }
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        this.conta = conta;
        return conta ? x : null;
      });
    }
  }

  async alterar() {
    const ativo = await this.verificarCartaoAtivo();
    if (!ativo) {
      return;
    }
    const valido = await this.verificarSeguranca();
    if (valido) {
      await this.solicitarAlteracao();
    }
  }

  async solicitarAlteracao() {
    const dados: any = {};
    dados.idConta = this.idConta;
    dados.portador = this.usuario.nomeCompleto;
    dados.valorAtual = this.limite.atual;
    dados.valorSolicitado = this.formLimite.getRawValue().valor;
    dados.periodo = this.limite.periodo;
    dados.tipoLimitePix = this.limite.tipo;
    dados.situacao = 'PENDENTE';
    dados.idProduto = this.conta.idProdutoInstituicao;

    await loading(
      this.pixService.solicitarLimite(dados).subscribe({
        next: async () => {
          await this.apresentarSucesso();
        },
        error: async (erro: any) => {
          await this.apresentarErro(erro?.error?.msg);
        },
      })
    );
  }

  async verificarSeguranca() {
    if (
      this.credencial.metodoSegurancaTransacao ==
      MetodoSegurancaEnum.NaoVerificar
    ) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: this.credencial.metodoSegurancaTransacao,
        idCredencial: this.credencial.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Solicitação enviada',
        mensagem:
          'A solicitação foi enviada para análise, os pedidos de alteração dos limites têm um prazo entre 24 e 48 horas para serem analisados, e você será comunicado pelo aplicativo da Valloo.',
        urlRetorno: `pix/${this.idConta}/limites`,
        classeImagem: 'sucesso-1',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Ocoreu um erro!',
        mensagem:
          message || 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.role == 'primaria') {
      return;
    }
    if (data && data.role == 'secundaria') {
      this.router.navigate([`pix/${this.idConta}/limites`]);
      return;
    }
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial,
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }
}
