section {
  margin-bottom: 1.5rem;
}

.limite {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 1rem;
  background: var(--ion-color-medium);
  border-radius: 10px;
  margin: 1rem 0;

  p {
    color: var(--ion-color-gray-50);
    font-weight: 300;
    margin: 0;

    b {
      color: var(--ion-color-secondary);
      font-weight: 600;
    }
  }

  p:first-child {
    margin: 0 0 0.5rem 0;
  }

  p:last-child {
    margin: 0;
  }

  ion-button {
    height: 22px;
    font-size: 12px;
    font-weight: 600;
    --border-radius: 4px;
  }
}

ion-skeleton-text {
  border-radius: 6px;
}


