section {
  margin-bottom: 1.5rem;

  article {
    margin-bottom: 1rem;
  }
}

ion-segment {
  --background: var(--ion-color-background);

  ion-segment-button {
    --border-radius: 4px;
    --background: var(--ion-color-medium);
    font-size: 12px;
    font-weight: 600;
    margin-right: 0.5rem;
    min-height: 22px;
    text-transform: none;
    --color: var(--ion-color-gray-50);
    --indicator-color: var(--ion-color-primary);
    --color-checked: var(--ion-color-primary-contrast)
  }

  ion-segment-button:last-child {
    margin-right: 0;
  }

  ion-segment-button::before {
    border: none;
  }
}

.data-agendamento {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 1rem 0;

  .dia {
    border-radius: 4px;
    background: var(--ion-color-primary);
    padding: 4px;

    p {
      color: var(--ion-color-secondary-contrast);
      font-size: 12px;
      font-weight: 600;
      margin: 0;
    }
  }

  ion-button {
    --border-radius: 100%;
    min-width: 32px;
    width: 32px;
    height: 32px;
    --padding-bottom: 8px;
    --padding-end: 8px;
    --padding-top: 8px;
    --padding-start: 8px;
    margin: 0;
  }
}

.agendamento {
  --background: var(--ion-color-background);
  --padding-start: 0;
  --border-radius: 8px;

  .tipo {
    background-color: var(--ion-color-medium);
    border-radius: 100%;
    min-width: 42px;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;

    ion-icon {
      font-size: 22px;
    }
  }

  p {
    font-size: 14px;
    font-weight: 400;
    line-height: 119.5%;
    margin: 0;
    color: var(--ion-color-text-default);
  }

  ion-label {
    white-space: normal;

    h3 {
      color: var(--ion-color-gray-50);
      font-size: 14px;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    p {
      font-size: 12px;
      font-weight: 400;
      line-height: 119.5%;
      margin: 0;
      color: var(--ion-color-text-default);
    }
  }
}
