<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/' + idConta">Meus agendamentos</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <section *ngIf="idConta">
    <mobile-total-agendado [idConta]="idConta" (trocarTipo)="alterarTipo($event)"></mobile-total-agendado>
  </section>
  <section>
    <article>
      <mobile-searchbar placeholder="Pesquise um agendamento"
                        (changeInput)="filtrarAgendamentos($event)"></mobile-searchbar>
    </article>
    <ion-segment [scrollable]="true" [value]="mes" (ionChange)="alterarMes($event)">
      <ion-segment-button [value]="item.mes" *ngFor="let item of meses">
        {{ (item.data | date:'MMMM') | titlecase }}
      </ion-segment-button>
    </ion-segment>
  </section>
  <section>
    <ng-container *ngIf="agendamentos$ | async as agendamentos; else carregando">
      <ng-container *ngIf="agendamentos.length; else estadoVazio">
        <ng-container *ngFor="let agenda of agendamentos; let i = index">
          <mobile-titulo-secao>{{ (agenda.data | date:'MMMM') | titlecase }}</mobile-titulo-secao>
          <div class="data-agendamento">
            <div class="dia">
              <p>{{ agenda.data | date:'mediumDate' }}</p>
            </div>
          </div>
          <ion-item *ngFor="let agendamento of agenda.agendamentos" lines="none" class="agendamento" [button]="true"
                    [detail]="false" (click)="verAgendamento(agendamento)">
            <div class="tipo" slot="start">
              <ion-icon *ngIf="agendamento.agendado" name="agendamento"></ion-icon>
              <ion-icon *ngIf="agendamento.cancelado" name="agendamento-cancelado"></ion-icon>
              <ion-icon *ngIf="agendamento.enviado" name="agendamento-envio"></ion-icon>
            </div>
            <ion-label>
              <h3>{{ agendamento.descricao }}</h3>
              <p>{{ agendamento.benNome | titlecase }}</p>
            </ion-label>
            <p slot="end" *ngIf="!emPontos">{{ agendamento.valorOperacao | currency:'BRL' }}</p>
            <p slot="end" *ngIf="emPontos">Ps {{ agendamento.valorOperacaoPontos | number:'1.0-0' }}</p>
          </ion-item>
        </ng-container>
      </ng-container>
      <ng-template #estadoVazio>
        <mobile-estado-vazio message="Nenhuma transação encontrada no mês selecionado."
                             iconName="extrato"></mobile-estado-vazio>
      </ng-template>
    </ng-container>
    <ng-template #carregando>
      <ng-container *ngFor="let i of [1,2,3]">
        <div class="data-agendamento">
          <div class="dia">
            <p>
              <mobile-skeleton></mobile-skeleton>
            </p>
          </div>
        </div>
        <ion-item lines="none" class="agendamento">
          <div class="tipo" slot="start"></div>
          <ion-label>
            <h3>
              <mobile-skeleton></mobile-skeleton>
            </h3>
            <p>
              <mobile-skeleton></mobile-skeleton>
            </p>
          </ion-label>
          <p slot="end">
            <mobile-skeleton></mobile-skeleton>
          </p>
        </ion-item>
      </ng-container>
    </ng-template>
  </section>
</ion-content>
