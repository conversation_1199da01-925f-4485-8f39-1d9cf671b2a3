import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { Observable } from 'rxjs';
import { PixService } from '@corporativo/shared';

export interface Total {
  pontos: number;
  reais: number;
}

@Component({
  selector: 'mobile-total-agendado',
  templateUrl: './total-agendado.component.html',
  styleUrls: ['./total-agendado.component.scss'],
  standalone: false,
})
export class TotalAgendadoComponent implements OnInit {
  @Input() idConta!: number;
  @Output() trocarTipo = new EventEmitter();
  swiperRef: ElementRef | undefined;
  total$!: Observable<Total>;

  constructor(private pixService: PixService) {}

  next(tipo: string) {
    this.trocarTipo.emit(tipo);
    this.swiperRef?.nativeElement.swiper.slideNext();
  }

  prev() {
    this.swiperRef?.nativeElement.swiper.slidePrev();
  }

  ngOnInit() {
    this.total$ = this.pixService.buscarTotalAgendado(this.idConta);
  }
}
