.valor {

  p {
    color: var(--ion-color-label);
    font-weight: var(--ion-font-weight-label);
    margin: 0 0 6px 0;
  }

  h1 {
    margin: 0;
    color: var(--ion-color-label);
    //font-size: var(--ion-font-size-h1);
    font-size: 20px;
    font-weight: var(--ion-font-weight-text);
    line-height: 1;

    b {
      font-weight: var(--ion-font-weight-h1);
      font-size: 20px;
    }
  }
}


ion-item {
  width: 100%;
  --background: var(--ion-color-background);
  --padding-start: 0;

  ion-label.right {
    margin-right: 0;
  }

  .itens {
    width: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .buttons {
      display: flex;
      align-items: flex-end;
      flex-direction: column;
      justify-content: center;

      ion-button {
        margin: 0;
        --padding-start: 0;
        --padding-end: 0;
        --padding-bottom: 0;
        --padding-top: 0;
        height: 30px;
      }

      ion-button:last-child {
        height: 22px;
        --padding-start: 1rem;
        --padding-end: 1rem;
        --border-radius: 4px;
      }
    }

    &.left {
      padding: 1rem 1rem 1rem 0;
    }
  }

  ion-icon {
    margin: 0;
  }
}
