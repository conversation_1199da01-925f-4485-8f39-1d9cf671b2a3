@if (total$ | async; as total) {
<!--  <swiper-container #swiper slidesPerView="1" loop="false">-->
<!--    <swiper-slide>-->
      <ion-item lines="none">
        <div class="itens">
          <ion-label class="right">
            <div class="valor">
              <p>Total agendado</p>
              <h1>R$ <b>{{ total.reais | currencyBr }}</b></h1>
            </div>
          </ion-label>
        </div>
<!--        <ion-icon class="ion-align-self-end" slot="end" color="quartenary" name="chevron-forward-circle" size="small"-->
<!--                  (click)="next('pontos')"></ion-icon>-->
      </ion-item>
<!--    </swiper-slide>-->
<!--    <swiper-slide>-->
<!--      <ion-item lines="none">-->
<!--        <div class="itens right">-->
<!--          <ion-label class="right">-->
<!--            <div class="valor">-->
<!--              <p>Total agendado</p>-->
<!--              <h1>Ps&nbsp;<b>{{ total.pontos | number: '1.0-0' }}</b></h1>-->
<!--            </div>-->
<!--          </ion-label>-->
<!--        </div>-->
<!--        <ion-icon class="ion-align-self-end" slot="end" color="quartenary" name="chevron-forward-circle" size="small"-->
<!--                  (click)="next('reais')"></ion-icon>-->
<!--      </ion-item>-->
<!--    </swiper-slide>-->
<!--  </swiper-container>-->
} @else {
  <ion-item lines="none">
    <div class="itens">
      <ion-label class="right">
        <div class="valor">
          <p>Total agendado</p>
          <h1>
            <mobile-skeleton height="20px" width="120px"></mobile-skeleton>
          </h1>
        </div>
      </ion-label>
    </div>
  </ion-item>
}
