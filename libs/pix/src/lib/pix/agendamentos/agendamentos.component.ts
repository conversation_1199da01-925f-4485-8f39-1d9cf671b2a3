import { Component } from '@angular/core';
import {
  Agenda,
  Agendamento,
  AuthService,
  PixService,
} from '@corporativo/shared';
import { addMonths, getMonth } from 'date-fns';
import * as _ from 'lodash';
import { merge, Observable } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'vlo-agendamentos',
  templateUrl: './agendamentos.component.html',
  styleUrls: ['./agendamentos.component.scss'],
  standalone: false,
})
export class AgendamentosComponent {
  usuario!: any;
  mes = 0;
  emPontos = false;
  limpar = false;
  meses: any[] = [];
  agendamentos$!: Observable<Agenda[]>;
  idConta!: number;

  constructor(
    private authService: AuthService,
    private pixService: PixService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.usuario = this.authService.getUser();
    const hoje = new Date();
    this.mes = getMonth(hoje);
    const meses: any[] = [{ data: hoje, mes: this.mes }];
    for (let i = 1; i <= 3; i++) {
      const mesFrente = addMonths(hoje, i);
      const mesPassado = addMonths(hoje, -i);
      meses.push({ data: mesPassado, mes: getMonth(mesPassado) });
      meses.push({ data: mesFrente, mes: getMonth(mesFrente) });
    }
    this.meses = _.orderBy(meses, 'data', 'asc');
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
    this.agendamentos$ = merge(
      this.pixService.buscarAgendamentos(this.idConta, this.mes),
      this.pixService.getAgendamentosFiltrados()
    );
  }

  filtrarAgendamentos(valor: string) {
    this.pixService.filtrarAgendamentos(valor);
  }

  alterarMes(customEvent: CustomEvent) {
    this.pixService.buscarAgendamentosPorMes(customEvent.detail.value);
  }

  alterarTipo(tipo: string) {
    this.emPontos = tipo === 'pontos';
  }

  verAgendamento(agendamento: Agendamento) {
    this.router.navigate([`pix/${this.idConta}/agendamentos/detalhe`], {
      state: { agendamento },
    });
  }
}
