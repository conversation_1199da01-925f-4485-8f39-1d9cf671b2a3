import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AgendamentosComponent } from './agendamentos.component';
import { DetalheComponent } from './detalhe/detalhe.component';

const routes: Routes = [
  {
    path: '',
    component: AgendamentosComponent,
  },
  {
    path: 'detalhe',
    component: DetalheComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AgendamentosRoutingModule {}
