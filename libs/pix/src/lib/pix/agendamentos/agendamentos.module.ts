import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AgendamentosComponent } from './agendamentos.component';
import { AgendamentosRoutingModule } from './agendamentos-routing.module';
import { IonicModule } from '@ionic/angular';
import {
  EstadoVazioModule,
  SearchbarModule,
  SkeletonModule,
  TitleToolbarModule,
  TituloSecaoModule,
} from '@corporativo/components';
import { DetalheComponent } from './detalhe/detalhe.component';
import { TotalAgendadoModule } from './total-agendado/total-agendado.module';
import { FavorecidoModule } from '@corporativo/pix';

@NgModule({
  declarations: [AgendamentosComponent, DetalheComponent],
  imports: [
    CommonModule,
    AgendamentosRoutingModule,
    IonicModule,
    TitleToolbarModule,
    TotalAgendadoModule,
    SearchbarModule,
    SkeletonModule,
    TituloSecaoModule,
    FavorecidoModule,
    EstadoVazioModule,
  ],
  exports: [DetalheComponent],
})
export class AgendamentosModule {}
