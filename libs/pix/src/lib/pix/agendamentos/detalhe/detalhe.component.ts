import { Component, OnInit } from '@angular/core';
import {
  Agendamento,
  AuthService,
  Credencial,
  MetodoSegurancaEnum,
  PixService,
  Usuario,
} from '@corporativo/shared';
import { ActivatedRoute, Router } from '@angular/router';
import { Al<PERSON><PERSON>ontroller, ModalController } from '@ionic/angular';
import {
  ModalAtivarCartaoComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent,
} from '@corporativo/modals';
import { loading } from '@utils/loading.util';
import { toast } from '@utils/toast.util';

@Component({
  selector: 'mobile-detalhe',
  templateUrl: './detalhe.component.html',
  styleUrl: './detalhe.component.scss',
  standalone: false,
})
export class DetalheComponent implements OnInit {
  usuario!: Usuario;
  beneficiario: any;
  agendamento!: Agendamento;
  idConta!: number;
  credencial!: Credencial | undefined;

  constructor(
    private authService: AuthService,
    private router: Router,
    private alertController: AlertController,
    private pixService: PixService,
    private modalController: ModalController,
    private activatedRoute: ActivatedRoute
  ) {
    this.usuario = this.authService.getUser();
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    if (state.agendamento) {
      this.agendamento = state.agendamento;
      this.beneficiario = {
        nome: this.agendamento.benNome,
        agencia: this.agendamento.benCodAgencia,
        conta: this.agendamento.benNroConta,
        documento: this.agendamento.benInscricaoNacional,
        tipo: this.agendamento.benContaTipoId,
        instituicao: this.agendamento.benInstituicao,
        ispbParticipante: this.agendamento.benIspb,
        chave: this.agendamento.benChaveEnderecamento,
      };
    }
  }

  ngOnInit() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        return conta ? x : null;
      });
    }
  }

  verComprovante() {
    return this.router.navigate([
      `/extrato/${this.idConta}/comprovante/${this.agendamento.benEndToEnd}`,
    ]);
  }

  async cancelar() {
    const ativo = await this.verificarCartaoAtivo();
    if (!ativo) {
      return;
    }

    const valido = await this.verificarSeguranca();
    if (!valido) {
      return;
    }
    await loading(
      this.pixService.cancelarAgendamento(this.agendamento).subscribe({
        next: () => {
          this.apresentarSucesso();
        },
        error: (erro: any) => {
          toast(erro.msg || 'Houve um erro inesperado. Tente novamente!');
        },
      })
    );
  }

  async confirmarCancelar() {
    const alert = await this.alertController.create({
      header: 'Cancelar agendamento!',
      message: 'Deseja realmente cancelar esse agendamento?',
      buttons: [
        {
          text: 'Cancelar',
          role: 'cancel',
          handler: () => {},
        },
        {
          text: 'OK',
          role: 'confirm',
          handler: () => {
            this.cancelar();
          },
        },
      ],
    });
    await alert.present();
  }

  async verificarSeguranca() {
    if (
      this.credencial?.metodoSegurancaTransacao ==
      MetodoSegurancaEnum.NaoVerificar
    ) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        metodoSegurancaTransacao: this.credencial?.metodoSegurancaTransacao,
        idCredencial: this.credencial?.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Agendamento cancelado',
        mensagem: 'Seu agendamento pix foi cancelado com sucesso. ',
        urlRetorno: `pix/${this.idConta}/agendamentos`,
        classeImagem: 'sucesso-1',
        tituloBotao: 'Voltar para meus agendamentos',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial,
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }
}
