<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+idConta+'/agendamentos'">Agendamento</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <mobile-favorecido [favorecido]="beneficiario"></mobile-favorecido>
  </section>
  <section>
    <mobile-titulo-secao>Informações do envio</mobile-titulo-secao>
    <article class="info-envio ion-margin-top">
      <p>Valor:</p>
      <p><b>{{ agendamento.valorOperacao | currency: 'BRL' }}</b></p>
    </article>
    <article class="info-envio">
      <p>Quando:</p>
      <p><b>{{ agendamento.dataAgendamento | date: 'dd/MM/yyyy' }}</b></p>
    </article>
    <article class="info-envio" *ngIf="agendamento.campoLivre">
      <p>Mensagem:</p>
      <p><b>{{ agendamento.campoLivre }}</b></p>
    </article>
  </section>
  <section *ngIf="agendamento.enviado">
    <article>
      <ion-item class="opcao" lines="none" [button]="true" [detailIcon]="'chevron-forward-circle'"
      (click)="verComprovante()">
        <div class="icone" slot="start">
          <ion-icon name="comprovante" slot="start"></ion-icon>
        </div>
        <ion-label>
          <h3>Ver comprovante</h3>
        </ion-label>
      </ion-item>
    </article>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="confirmarCancelar()" *ngIf="agendamento.agendado">Cancelar</ion-button>
</ion-footer>
