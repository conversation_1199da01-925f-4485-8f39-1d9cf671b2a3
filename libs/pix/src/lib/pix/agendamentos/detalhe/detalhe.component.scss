section {
  margin-top: 1.5rem;
}

ion-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
}

.info-envio {
  display: flex;
  align-items: center;
  justify-content: space-between;

  p {
    font-size: 16px;
    color: var(--ion-color-text-default);
    margin: 0 0 1.3rem 0;
    font-weight: 400;

    b {
      font-weight: 600;
      color: var(--ion-color-gray-50);
    }
  }
}

.opcao {
  --background: var(--ion-color-background);
  --padding-start: 0;
  --border-radius: 8px;
  --inner-padding-end: 4px;

  .icone {
    background-color: var(--ion-color-medium);
    border-radius: 100%;
    min-width: 42px;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;

    ion-icon {
      font-size: 20px;
    }
  }

  ion-label {
    h3 {
      color: var(--ion-color-gray-50);
      font-size: 16px;
      font-weight: 600;
    }
  }
}
