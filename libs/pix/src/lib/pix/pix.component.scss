section {
  margin-bottom: 1.5rem;
}

article {
  margin-top: 1rem;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

ion-item {
  --background: var(--ion-color-background);
  --padding-start: 0;
  --border-radius: 8px;

  ion-label {
    white-space: normal;

    p {
      color: var(--ion-color-secondary);
      font-size: 10px;
      font-weight: 400;
      line-height: 119.5%;
      margin: 0;
    }
  }
}

h3 {
  margin: 1rem 0 0 0;
  color: var(--ion-color-gray-50);
  font-weight: 600;
  font-size: 16px;
  line-height: normal;
}

.configuracoes {
  ion-item {
    --inner-padding-end: 0;
    --detail-icon-color: var(--ion-color-medium-contrast);
    --detail-icon-opacity: 1;
  }
}
