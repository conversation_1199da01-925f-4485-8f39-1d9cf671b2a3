import { Component, OnInit, ViewChild } from '@angular/core';
import {
  AuthService,
  Conta,
  Credencial,
  SaldoConta,
} from '@corporativo/shared';
import { ActivatedRoute, Router } from '@angular/router';
import { textMask } from '@utils/masks.util';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { addMonths, format, isAfter, parseISO } from 'date-fns';
import { IonModal } from '@ionic/angular';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { dateBrToEn } from '@utils/date.util';

@Component({
  selector: 'mobile-informar-valor',
  templateUrl: './informar-valor.component.html',
  styleUrl: './informar-valor.component.scss',
  standalone: false,
})
export class InformarValorComponent implements OnInit {
  usuario!: any;
  destino: any;
  chave: any;
  dados: any;
  saldo!: SaldoConta;
  readonly textMaskOptions: MaskitoOptions = textMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();
  formValor = new FormGroup({
    valorFormatado: new FormControl('', [Validators.required]),
    valor: new FormControl(0, [Validators.required, Validators.min(0.01)]),
    data: new FormControl(format(new Date(), 'dd/MM/yyyy'), [
      Validators.required,
    ]),
    agendamento: new FormControl(),
    mensagem: new FormControl(),
  });
  hoje = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss");
  min = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss");
  max = format(addMonths(new Date(), 2), "yyyy-MM-dd'T'HH:mm:ss");
  @ViewChild(IonModal, { static: true }) modalData!: IonModal;
  idConta!: number;
  contas!: Conta[];
  conta!: Conta | undefined;

  constructor(
    private authService: AuthService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.usuario = this.authService.getUser();
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    if (state.dados) {
      this.dados = state.dados;
      this.destino = {
        nome: this.dados.propriaConta
          ? this.usuario.nomeCompleto
          : this.dados.nome,
        agencia: this.dados.agencia,
        conta: this.dados.conta,
        documento: this.dados.propriaConta
          ? this.usuario.documento
          : this.dados.documento,
        tipo: this.dados.tipo,
        instituicao: this.dados.instituicao.nome,
        ispbParticipante: this.dados.instituicao.ispbParticipante,
      };
    }
    if (state.chave) {
      const chave = state.chave;
      this.chave = chave;
      this.destino = {
        nome: chave.pessoa.nome,
        agencia: chave.conta.agencia,
        conta: chave.conta.numero,
        documento: chave.pessoa.inscricaoNacional,
        instituicao: chave.conta.nomeParticipante,
        ispbParticipante: chave.conta.ispbParticipante,
        chave: chave.chave.valor,
        endToEnd: chave.endToEnd,
        tipo: chave?.chave?.tipo?.descricao,
      };
    }
    this.formValor.get('valorFormatado')?.valueChanges.subscribe((x) => {
      let valor = 0;
      if (x) {
        valor = +x.replace('R$ ', '').replace(/\./g, '').replace(/,/g, '.');
      }
      this.formValor.get('valor')?.setValue(valor);
    });
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      const credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        return conta ? x : null;
      });
      this.contas = credencial.contas;
      this.conta = this.contas.find((x) => x.idConta == this.idConta);
      if (this.conta) {
        this.saldo = this.conta.saldoConta;
        this.formValor
          .get('valor')
          ?.setValidators([
            Validators.required,
            Validators.min(0.01),
            Validators.max(this.saldo.saldoDisponivel),
          ]);
      }
    }
  }

  ngOnInit() {}

  pegarSaldo(saldo: any) {
    this.formValor
      .get('valor')
      ?.setValidators([
        Validators.required,
        Validators.min(0.01),
        Validators.max(saldo.dinheiro),
      ]);
    this.saldo = saldo;
  }

  selecionarData(event: any) {
    this.hoje = event.detail.value;
    this.formValor
      .get('data')
      ?.setValue(format(parseISO(event.detail.value), 'dd/MM/yyyy'));
    this.modalData.dismiss();
  }

  continuar() {
    const dataEnvio = this.formValor.get('data')?.value || '';
    const data = parseISO(dateBrToEn(dataEnvio));
    this.formValor.get('agendamento')?.setValue(false);
    if (isAfter(data, new Date())) {
      this.formValor.get('agendamento')?.setValue(true);
    }
    const dados: any = this.formValor.getRawValue();
    this.router.navigate([`/pix/${this.idConta}/enviar/confirmar`], {
      state: { destinatario: this.destino, dados },
    });
  }
}
