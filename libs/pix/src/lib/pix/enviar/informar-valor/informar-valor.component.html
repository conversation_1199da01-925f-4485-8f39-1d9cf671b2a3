<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+idConta+'/enviar'">Dados do envio</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <mobile-favorecido [favorecido]="destino"></mobile-favorecido>
  <mobile-saldo-simples *ngIf="contas" [idConta]="idConta" [contas]="contas" [showNext]="false"></mobile-saldo-simples>
  <section>
    <mobile-titulo-secao>Informações do envio</mobile-titulo-secao>
    <form [formGroup]="formValor" class="ion-margin-top">
      <ion-item lines="none" *ngIf="saldo">
        <ion-input labelPlacement="stacked" placeholder="0,00" type="tel" formControlName="valorFormatado"
                   errorText="Informe um valor válido" [brmasker]="{money: true, thousand: '.',  decimalCaracter: ',', decimal: 2}"
                   helperText="Valor entre R$ 0,01 até {{saldo.saldoDisponivel | currency:'BRL'}}">
          <div slot="label" class="custom-label">Valor</div>
          <ion-text slot="start">R$</ion-text>
        </ion-input>
      </ion-item>
      <ion-item lines="none" aria-readonly="true" id="open-modal" [button]="true" detail="false">
        <ion-input readonly labelPlacement="stacked" placeholder="00/00/0000" type="text"
                   errorText="Informe uma data válida" helperText="Você pode alterar a data do envio"
                   formControlName="data">
          <div slot="label" class="custom-label">Data</div>
          <ion-icon slot="end" name="calendar-outline"></ion-icon>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Digite uma mensagem" type="text" formControlName="mensagem"
                   helperText="Você pode inserir uma descrição do envio, não utilize emojis" maxlength="72"
                   [maskito]="textMaskOptions" [maskitoElement]="maskPredicate">
          <div slot="label" class="custom-label">Mensagem</div>
        </ion-input>
      </ion-item>
    </form>
  </section>
</ion-content>
<ion-modal #modal trigger="open-modal">
  <ng-template>
    <ion-header class="ion-no-border">
      <ion-toolbar>
        <mobile-title-toolbar [buttonModal]="true" (closeEmitter)="modal.dismiss()">Selecionar</mobile-title-toolbar>
      </ion-toolbar>
    </ion-header>
    <ion-content class="content-default">
      <section>
        <ion-datetime [showDefaultButtons]="false" [value]="hoje" [max]="max" [min]="min" locale="pt-BR"
                      (ionChange)="selecionarData($event)" presentation="date"></ion-datetime>
      </section>
    </ion-content>
    <ion-footer>
      <ion-button fill="clear" (click)="modal.dismiss()" expand="block">Cancelar</ion-button>
    </ion-footer>
  </ng-template>
</ion-modal>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="continuar()" [disabled]="formValor.invalid">Continuar</ion-button>
  <div class="div-image">
    <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
  </div>
</ion-footer>
