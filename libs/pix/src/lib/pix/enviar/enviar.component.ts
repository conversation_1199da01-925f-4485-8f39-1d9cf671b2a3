import { Component, ViewChild } from '@angular/core';
import { AuthService, PixService } from '@corporativo/shared';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AlertController, IonModal } from '@ionic/angular';
import {
  catchError,
  debounceTime,
  merge,
  Observable,
  Subject,
  tap,
} from 'rxjs';
import { loading } from '@utils/loading.util';
import { toast } from '@utils/toast.util';
import { ActivatedRoute, Router } from '@angular/router';
import {
  cnpjMask,
  cpfMask,
  noneMask,
  numberMask,
  phoneMask,
} from '@utils/masks.util';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import {
  validarCelular,
  validarCNPJ,
  validarCPF,
  validarEmail,
  validarUUID,
} from '@utils/validators.util';
import { TextUtil } from '@utils/text.util';

@Component({
  selector: 'mobile-enviar',
  templateUrl: './enviar.component.html',
  styleUrls: ['./enviar.component.scss'],
  standalone: false,
})
export class EnviarComponent {
  usuario!: any;
  formEnviar = new FormGroup({
    valor: new FormControl('', Validators.required),
  });
  @ViewChild(IonModal, { static: true }) modalContatos!: IonModal;
  contatos$!: Observable<any[]>;
  erroContatos = false;
  maskOptions: MaskitoOptions = noneMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();
  valido = false;
  valor = '';
  tipoChave = 0;
  alterarContatos$: Subject<any[]> = new Subject<any[]>();
  contatos: any[] = [];
  idConta!: number;
  escolherCpfCelular = false;
  modalEscolherCpfCelular = false;
  cpfCelular = '';

  constructor(
    private pixService: PixService,
    private authService: AuthService,
    private alertController: AlertController,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    this.usuario = authService.getUser();
  }

  ionViewDidEnter() {
    this.formEnviar.reset();
    this.cpfCelular = '';
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
    this.modalContatos.isOpen = true;
    this.buscarContatos();
    this.formEnviar
      .get('valor')
      ?.valueChanges.pipe(debounceTime(300))
      .subscribe((x) => {
        this.validarTipoChave(x);
      });
  }

  ionViewWillLeave() {
    this.modalContatos.isOpen = false;
  }

  validarTipoChave(valor: string | null) {
    console.log('validarTipoChave', valor);
    this.valido = false;
    this.maskOptions = noneMask;
    if (!valor) {
      return;
    }
    this.escolherCpfCelular = false;

    const valorSemFormato = valor.replace(/\D/g, '');

    if (validarEmail(valor)) {
      this.valido = true;
      this.maskOptions = noneMask;
      this.valor = valor;
      return;
    }

    if (validarCNPJ(valorSemFormato)) {
      this.valido = true;
      this.maskOptions = cnpjMask;
      this.valor = valorSemFormato;

      const cnpjFormatado = TextUtil.mascaraCnpj(valorSemFormato);

      setTimeout(() => {
        this.formEnviar
          .get('valor')
          ?.setValue(cnpjFormatado, { emitEvent: false });
      }, 0);
      return;
    }

    if (validarCPF(valorSemFormato) && !validarCelular(valorSemFormato)) {
      this.valido = true;
      this.maskOptions = cpfMask;
      this.valor = valorSemFormato;

      const cpfFormatado = TextUtil.mascaraCpf(valorSemFormato);

      setTimeout(() => {
        this.formEnviar
          .get('valor')
          ?.setValue(cpfFormatado, { emitEvent: false });
      }, 0);
      return;
    }

    if (validarCelular(valorSemFormato) && !validarCPF(valorSemFormato)) {
      this.valido = true;
      this.maskOptions = phoneMask;
      this.valor = valorSemFormato;

      const telefoneFormatado = TextUtil.converterCelular(valorSemFormato);

      setTimeout(() => {
        this.formEnviar
          .get('valor')
          ?.setValue(telefoneFormatado, { emitEvent: false });
      }, 0);
      return;
    }

    if (validarUUID(valor)) {
      this.valido = true;
      this.maskOptions = noneMask;
      this.valor = valor;
      return;
    }

    if (validarCelular(valorSemFormato) && validarCPF(valorSemFormato)) {
      this.escolherCpfCelular = true;
      this.valido = true;
      this.maskOptions = numberMask;
      this.valor = valorSemFormato;
      return;
    }

    this.valor = valor;
  }

  buscarContatos() {
    this.erroContatos = false;
    this.contatos$ = merge(
      this.pixService.buscarContatos(this.idConta).pipe(
        tap((x) => (this.contatos = [...x])),
        catchError((erro: any) => {
          this.erroContatos = true;
          throw erro;
        })
      ),
      this.alterarContatos$.asObservable()
    );
  }

  selecionarContato(contato: any) {
    this.valor = contato.valorChave;
    this.tipoChave = contato.tipoChave;
    this.continuar();
  }

  async confirmarExcluirContato(id: number) {
    const alert = await this.alertController.create({
      header: 'Excluir chave!',
      message: 'Deseja realmente excluir essa chave?',
      buttons: [
        {
          text: 'Cancelar',
          role: 'cancel',
          handler: () => {
            // Método vazio necessário para compatibilidade com a API do AlertController
          },
        },
        {
          text: 'OK',
          role: 'confirm',
          handler: () => {
            this.excluirContato(id);
          },
        },
      ],
    });

    await alert.present();
  }

  async excluirContato(id: number) {
    await loading(
      this.pixService.excluirContato(id).subscribe({
        next: () => {
          toast('Contato excluido com sucesso.');
          this.buscarContatos();
        },
        error: (error) => {
          toast('Houve um erro ao excluir o contato. Tente novamente.');
        },
      })
    );
  }

  escolherEContinuar(cpfCelular: 'cpf' | 'celular') {
    this.cpfCelular = cpfCelular;
    this.modalEscolherCpfCelular = false;
    this.escolherCpfCelular = false;
    this.continuar();
  }

  async continuar() {
    if (this.escolherCpfCelular) {
      this.modalEscolherCpfCelular = true;
      return;
    }
    let chave = '';
    if (
      validarCNPJ(this.valor) ||
      validarCPF(this.valor) ||
      validarCelular(this.valor)
    ) {
      chave = TextUtil.removeNotDigit(this.valor);
    }
    if (!this.cpfCelular && validarCelular(this.valor)) {
      chave = '+55' + TextUtil.removeNotDigit(this.valor);
    }
    if (this.cpfCelular == 'celular' && validarCelular(this.valor)) {
      chave = '+55' + TextUtil.removeNotDigit(this.valor);
    }
    if (this.cpfCelular == 'cpf' && validarCPF(this.valor)) {
      chave = TextUtil.removeNotDigit(this.valor);
    }
    if (chave == '') {
      chave = this.valor;
    }
    await loading(
      this.pixService.consultarChave(chave).subscribe({
        next: (dados: any) => {
          if (dados.isSuccess) {
            this.router.navigate(
              [`pix/${this.idConta}/enviar/informar-valor`],
              { state: { chave: dados.value } }
            );
          } else {
            this.router.navigate([`pix/${this.idConta}/enviar/verificar`]);
          }
        },
        error: () => {
          this.router.navigate([`pix/${this.idConta}/enviar/verificar`]);
        },
      })
    );
  }

  filtrarContatos(search: string) {
    if (search === undefined) {
      this.alterarContatos$.next([...this.contatos]);
    } else {
      const normalizedQuery = search.toLowerCase();
      const filteredItems = this.contatos.filter((item) => {
        return item.nome.toLowerCase().includes(normalizedQuery);
      });
      this.alterarContatos$.next(filteredItems);
    }
  }
}
