<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+idConta+'/enviar'">Dados do envio</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <mobile-favorecido [favorecido]="destinatario"></mobile-favorecido>
  <mobile-titulo-secao>Informações do envio</mobile-titulo-secao>
  <article class="dados-envio ion-margin-top">
    <p>Valor:</p>
    <p><b>{{ dados.valor | currency: 'BRL' }}</b></p>
  </article>
  <article class="dados-envio">
    <p>Quando:</p>
    <p><b>{{ dados.data }} {{ dados.agendamento ? '(Agendar)' : '(Hoje)' }}</b></p>
  </article>
  <article class="dados-envio">
    <p>Mensagem:</p>
    <p><b>{{ dados.mensagem || '' }}</b></p>
  </article>
</ion-content>
<ion-footer>
  <ion-button class="btn ion-margin-bottom" expand="block" (click)="enviar()">
    {{ dados.tipoQRCode == 'ESTATICO' ? 'Transferir' : 'Pagar' }}
  </ion-button>
  <ion-button expand="block" fill="outline" (click)="cancelar()">Cancelar</ion-button>
  <div class="div-image">
    <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
  </div>
</ion-footer>
