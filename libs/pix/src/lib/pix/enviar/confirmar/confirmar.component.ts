import { Component } from '@angular/core';
import { ModalController } from '@ionic/angular';
import {
  AuthService,
  Credencial,
  MetodoSegurancaEnum,
  PixService, StorageService,
  Usuario,
} from '@corporativo/shared';
import {
  ModalAtivarCartaoComponent,
  ModalErroComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent,
} from '@corporativo/modals';
import { ActivatedRoute, Router } from '@angular/router';
import { loading } from '@utils/loading.util';
import { lastValueFrom } from 'rxjs';

@Component({
  selector: 'mobile-confirmar',
  templateUrl: './confirmar.component.html',
  styleUrl: './confirmar.component.scss',
  standalone: false,
})
export class ConfirmarComponent {
  dados: any;
  destinatario: any;
  usuario!: Usuario;
  idConta!: number;
  credencial!: Credencial;
  temNotaFiscal: boolean = false;

  constructor(
    private router: Router,
    private modalController: ModalController,
    private authService: AuthService,
    private pixService: PixService,
    private activatedRoute: ActivatedRoute,
    private storageService: StorageService
  ) {
    const state: any = this.router.getCurrentNavigation()?.extras.state;
    this.dados = state.dados;
    this.destinatario = state.destinatario;
    this.usuario = this.authService.getUser();
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      // @ts-ignore
      this.credencial = this.usuario.credenciais.find((x: Credencial) => {
        const conta = x.contas.find((c) => c.idConta == this.idConta);
        return conta ? x : null;
      });

      const permissoes = this.storageService.getProdutosPermissoes();
      permissoes.filter((x: any) => {
        if (x.idConta == idConta && x.temNotaFiscal) {
          this.temNotaFiscal = true;
        }
      })
    }
  }

  async enviar() {
    const cartaoAtivo = await this.verificarCartaoAtivo();
    if (!cartaoAtivo) {
      return;
    }
    this.credencial = cartaoAtivo;
    const valido = await this.verificarSeguranca();
    if (!valido) {
      return;
    }
    await loading(
      this.pixService.enviarPix(this.usuario, this.idConta, this.destinatario, this.dados).subscribe({
          next: async (retorno: any) => {
            if (retorno == null || this.dados.agendamento && retorno.dataAgendamento == null) {
              return this.apresentarErro(retorno.message);
            }

            if (!this.dados.agendamento && !retorno.isSuccess) {
              return this.apresentarErro(retorno.message);
            }
            this.salvarContato();
            const data = await this.apresentarSucesso(this.dados.agendamento);
            if (retorno.isSuccess && data && data.role == 'secundaria') {
              await this.router.navigate([`/extrato/${this.idConta}/comprovante/` + retorno.value.ordemPagamento.endToEnd], {state: {temNotaFiscal: this.temNotaFiscal}});
              return;
            }
          },
          error: (erro: any) => {
            let mensagemErro = erro.error?.erroMessage?.errors[0].message;
            mensagemErro = mensagemErro || erro?.error?.msg;
            this.apresentarErro(mensagemErro);
          },
        })
    );
  }

  async apresentarSucesso(agendamento: false) {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: agendamento ? 'Pix agendado!' : 'Pix enviado!',
        mensagem: agendamento
          ? 'Seu pix foi agendado ao favorecido e na data marcada estará disponível na conta.'
          : 'Seu pix foi enviado ao favorecido e em instantes estará disponível na conta.',
        urlRetorno: '/inicio',
        classeImagem: 'sucesso-3',
        tituloBotaoSecundario: agendamento ? '' : 'Ver comprovante',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  cancelar() {
    return this.router.navigate([`/pix/${this.idConta}`], { replaceUrl: true });
  }

  async verificarSeguranca() {
    if (
      this.credencial.metodoSegurancaTransacao ==
      MetodoSegurancaEnum.NaoVerificar
    ) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: this.credencial.metodoSegurancaTransacao,
        idCredencial: this.credencial.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  salvarContato() {
    if (!this.destinatario.chave) {
      return;
    }
    lastValueFrom(
      this.pixService.salvarContato(
        this.usuario,
        this.idConta,
        this.destinatario
      )
    );
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Erro ao enviar',
        mensagem: message,
        urlRetorno: '/inicio',
        tituloBotao: 'Tentar novamente',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return credencialAtiva;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial,
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }
}
