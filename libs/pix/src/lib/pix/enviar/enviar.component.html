<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/' + idConta">Enviar Pix</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <form [formGroup]="formEnviar">
      <ion-item lines="none">
        <ion-input class="custom" labelPlacement="stacked" placeholder="Digite uma chave" type="text"
                   formControlName="valor" errorText="Informe um valor" maxlength="200"
                   [maskito]="maskOptions" [maskitoElement]="maskPredicate"
                   helperText="Digite a chave CPF/CNPJ, E-mail, Celular ou outra">
          <div slot="label" class="custom-label">Insira a chave Pix</div>
        </ion-input>
      </ion-item>
    </form>
  </section>
  <section>
    <ion-button class="pix-manual" expand="block" fill="clear" size="small" routerLink="informar-dados">
      Não tem a chave Pix? Enviar com agência e conta
    </ion-button>
    <ion-button class="btn ion-margin-bottom" expand="block" (click)="continuar()" [disabled]="!valido">
      Continuar
    </ion-button>
    <div class="div-image">
      <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
    </div>
  </section>
  <ion-modal #modal [backdropDismiss]="false" [initialBreakpoint]="0.5" [backdropBreakpoint]="0.70"
             [breakpoints]="[0.2, 0.5, 0.75]" class="modal-contatos">
    <ng-template>
      <ion-header class="ion-no-border">
        <ion-toolbar color="medium">
          <mobile-title-toolbar [mostrarVoltar]="false">Contatos</mobile-title-toolbar>
        </ion-toolbar>
      </ion-header>
      <ion-content [fullscreen]="true" class="content-default">
        <section>
          <ng-container *ngIf="contatos$ | async as contatos; else carregando">
            <mobile-searchbar (changeInput)="filtrarContatos($event)" [escuro]="true"
                              placeholder="Pesquise pelo nome"></mobile-searchbar>
            <ng-container *ngIf="contatos.length; else estadoVazio">
              <ion-list lines="none" class="contatos">
                <ion-item-sliding *ngFor="let contato of contatos">
                  <ion-item button="true" detail="true"
                            (click)="selecionarContato(contato)">
                    <div class="iniciais-nome" slot="start">
                      <p>{{ contato.nome | iniciais:'first' }}</p>
                    </div>
                    <ion-label>
                      <h2>{{ contato.nome | titlecase }}</h2>
                      <p>{{ contato.participante }}</p>
                      <p>Arraste para o lado se quiser excluir</p>
                    </ion-label>
                  </ion-item>
                  <ion-item-options side="start">
                    <ion-item-option color="danger" expandable="true" (click)="confirmarExcluirContato(contato.id)">
                      <ion-icon slot="icon-only" name="trash"></ion-icon>
                    </ion-item-option>
                  </ion-item-options>
                </ion-item-sliding>
              </ion-list>
            </ng-container>
            <ng-template #estadoVazio>
              <mobile-estado-vazio [light]="true"
                message="Nenhum contato encontrado. Faça um envio de Pix para salvar um contato."></mobile-estado-vazio>
            </ng-template>
          </ng-container>
          <ng-template #carregando>
            <ng-container *ngIf="!erroContatos; else estadoErro">
              <mobile-searchbar [disable]="true" placeholder="Pesquise pelo nome do contato"></mobile-searchbar>
              <ion-list lines="none" class="contatos">
                <ion-item button="false" detail="false" *ngFor="let i of [1,2,3]">
                  <ion-avatar slot="start">
                    <ion-skeleton-text animated></ion-skeleton-text>
                  </ion-avatar>
                  <ion-label>
                    <h2>
                      <mobile-skeleton width="100%"></mobile-skeleton>
                    </h2>
                    <p>
                      <mobile-skeleton></mobile-skeleton>
                    </p>
                  </ion-label>
                </ion-item>
              </ion-list>
            </ng-container>
            <ng-template #estadoErro>
              <article class="ion-text-center">
                <ion-text class="ion-margin-bottom">
                  <p>Houve um erro ao buscar contatos. Acesse a opção de Atualizar para tenter novamente!</p>
                </ion-text>
                <ion-button expand="block" fill="outline" (click)="buscarContatos()">
                  Atualizar
                </ion-button>
              </article>
            </ng-template>
          </ng-template>
        </section>
      </ion-content>
    </ng-template>
  </ion-modal>
  <ion-modal #modal trigger="open-modal" [isOpen]="modalEscolherCpfCelular" class="modal-escolher-chave"
             [initialBreakpoint]="0.30" [breakpoints]="[0.30, 0.40, 0.50]" (didDismiss)="modalEscolherCpfCelular = false">
    <ng-template>
      <ion-content class="content-default">
        <section>
          <mobile-titulo-secao>O número informado é um CPF ou celular?</mobile-titulo-secao>
          <div>
            <ion-button fill="outline" class="ion-margin-bottom" (click)="escolherEContinuar('cpf')">CPF</ion-button>
            <ion-button fill="outline" (click)="escolherEContinuar('celular')">Celular</ion-button>
          </div>
        </section>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
