section {
  margin-bottom: 1.5rem;
}

.modal-contatos {
  ion-content {
    --background: #F0F0F0;
  }
}

.input-bottom sc-ion-input-ios {
  .helper-text sc-ion-input-ios {
    font-size: 10px;
  }
}

.contatos {
  background: transparent;
  margin-top: 1rem;

  ion-avatar {
    width: 56px;
    height: 56px;
  }

  ion-item-sliding {
    margin-bottom: 0.5rem;
  }

  ion-item {
    --background: #F0F0F0;
    --padding-start: 0;

    ion-label {
      h2 {
        color: var(--ion-color-gray-50);
        font-size: 16px;
        font-weight: 600;
      }

      p {
        color: var(--ion-color-gray-50);
        font-size: 10px;
        font-weight: 400;
      }

      p:last-child {
        margin-top: 4px;
        color: var(--ion-color-gray-600);
      }
    }
  }
}

.iniciais-nome {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: var(--ion-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 1rem 0 0;
  position: relative;

  p {
    margin: 0;
    color: var(--ion-color-secondary-contrast);
    letter-spacing: 1px;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    text-transform: uppercase;
  }
}

.modal-escolher-chave {
  section {
    margin-top: 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;

    div {
      width: 100%;
    }

    ion-button:first-child {
      margin-top: 2rem;
    }

    ion-button {
      width: 100%;
    }
  }
}

.pix-manual {
  margin-bottom: 13px;
  height: 20px;
  margin-top: 47px;
  font-size: 12px;
}

.div-image {
  display: flex;
  justify-content: center;
  transform: translateY(-8px);
}

.imagem-valloo {
  width: 40%;
}
