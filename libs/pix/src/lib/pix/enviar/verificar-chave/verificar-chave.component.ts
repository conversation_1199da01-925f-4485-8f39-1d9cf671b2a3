import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'mobile-verificar-chave',
  templateUrl: './verificar-chave.component.html',
  styleUrl: './verificar-chave.component.scss',
  standalone: false,
})
export class VerificarChaveComponent {
  idConta!: number;

  constructor(private activatedRoute: ActivatedRoute) {}

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }
}
