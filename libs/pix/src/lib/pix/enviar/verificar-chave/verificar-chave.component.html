<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+idConta+'/enviar'">Enviar Pix</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <h3>A chave precisa esta vinculada a uma conta</h3>
  <p>Confirme se está correto.</p>
  <p>
    Você pode informar outra chave ou informar os dados de agência e conta para continuar.
  </p>
</ion-content>
<ion-footer>
  <ion-button class="ion-margin-bottom" expand="block" size="small" fill="clear"
              [routerLink]="'/pix/'+idConta+'/enviar/informar-dados'">
    Não tem a chave pix? Enviar com agência e conta
  </ion-button>
  <ion-button expand="block" fill="outline" [routerLink]="'/pix/'+idConta+'/enviar'">Voltar</ion-button>
</ion-footer>
