import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { PixService } from '@corporativo/shared';

@Component({
  selector: 'mobile-selecionar-participantes',
  templateUrl: './selecionar-participantes.component.html',
  styleUrl: './selecionar-participantes.component.scss',
  standalone: false,
})
export class SelecionarParticipantesComponent implements OnInit {
  @Output() selectionCancel = new EventEmitter<void>();
  @Output() selectionChange = new EventEmitter<string[]>();
  itens: any[] = [];
  filteredItems: any[] = [];
  carregando = true;
  erro = false;

  constructor(private pixService: PixService) {}

  ngOnInit() {
    this.buscarItens();
  }

  buscarItens() {
    this.carregando = true;
    this.erro = false;
    this.pixService.buscarParticipantes().subscribe({
      next: (itens: any[]) => {
        this.carregando = false;
        this.itens = [...itens];
        this.filteredItems = [...itens];
      },
      error: () => {
        this.erro = true;
      },
    });
  }

  trackItems(index: number, item: any) {
    return item.value;
  }

  cancelChanges() {
    this.selectionCancel.emit();
  }

  searchbarInput(ev: any) {
    this.filterList(ev.target.value);
  }

  filterList(searchQuery: string | undefined) {
    if (searchQuery === undefined) {
      this.filteredItems = [...this.itens];
    } else {
      const normalizedQuery = searchQuery.toLowerCase();
      this.filteredItems = this.itens.filter((item) => {
        return item.nome.toLowerCase().includes(normalizedQuery);
      });
    }
  }

  selectItem(item: any) {
    this.selectionChange.emit(item);
  }
}
