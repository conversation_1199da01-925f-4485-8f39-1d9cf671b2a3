<ion-header class="ion-no-border">
  <ion-toolbar class="titulo-pesquisa">
    <mobile-title-toolbar [buttonModal]="true" (closeEmitter)="cancelChanges()">Instituição</mobile-title-toolbar>
  </ion-toolbar>
  <ion-toolbar>
    <ion-searchbar class="pesquisa-participantes" [disabled]="carregando || erro" placeholder="Pesquise pelo nome"
                   (ionInput)="searchbarInput($event)"></ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ng-container *ngIf="!carregando; else carregandoLista">
    <ion-list>
      <ion-item lines="full" [button]="true" *ngFor="let item of filteredItems; trackBy: trackItems"
                (click)="selectItem(item)">
        <ion-label>{{ item.nome | uppercase }}</ion-label>
      </ion-item>
    </ion-list>
  </ng-container>
  <ng-template #carregandoLista>
    <ng-container *ngIf="!erro; else estadoErro">
      <ion-list>
        <ion-item lines="full" [button]="false" *ngFor="let i of [1,2,3]">
          <ion-label>
            <mobile-skeleton></mobile-skeleton>
          </ion-label>
        </ion-item>
      </ion-list>
    </ng-container>
    <ng-template #estadoErro>
      <article class="ion-text-center">
        <ion-text color="primary">
          <p>
            Houve um erro ao carregar a lista de instituições. Acesse a opção de atualizar ou tente novamente mais
            tarde.
          </p>
        </ion-text>
        <ion-button fill="outline" expand="block" (click)="buscarItens()">Atualizar</ion-button>
      </article>
    </ng-template>
  </ng-template>
</ion-content>
