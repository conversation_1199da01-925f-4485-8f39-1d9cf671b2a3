import { Component } from '@angular/core';
import { textMask } from '@utils/masks.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import {
  ModalAtivarCartaoComponent,
  ModalErroComponent,
  ModalSegurancaComponent,
  ModalSucessoComponent,
} from '@corporativo/modals';
import { ModalController } from '@ionic/angular';
import {
  AuthService,
  Credencial,
  MetodoSegurancaEnum,
  PixService,
  Usuario,
} from '@corporativo/shared';
import { loading } from '@utils/loading.util';

@Component({
  selector: 'mobile-devolver',
  templateUrl: './devolver.component.html',
  styleUrl: './devolver.component.scss',
  standalone: false,
})
export class DevolverComponent {
  readonly textMaskOptions: MaskitoOptions = textMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();
  formDevolver = new FormGroup({
    valorFormatado: new FormControl('', [Validators.required]),
    valor: new FormControl(0, [Validators.required, Validators.min(0.01)]),
    motivo: new FormControl('', Validators.required),
  });
  operacao: any;
  usuario: Usuario;
  idConta!: number;
  credencial!: Credencial;

  constructor(
    private router: Router,
    private pixService: PixService,
    private authService: AuthService,
    private modalController: ModalController,
    private activatedRoute: ActivatedRoute
  ) {
    this.usuario = this.authService.getUser();
    const dados: any = this.router.getCurrentNavigation()?.extras?.state;
    this.formDevolver.get('valorFormatado')?.valueChanges.subscribe((x) => {
      let valor = 0;
      if (x) {
        valor = +x.replace('R$ ', '').replace(/\./g, '').replace(/,/g, '.');
      }
      this.formDevolver.get('valor')?.setValue(valor);
    });
    if (dados) {
      this.operacao = dados.operacao;
      this.formDevolver
        .get('valor')
        ?.setValidators([
          Validators.required,
          Validators.min(0.01),
          Validators.max(this.operacao.instrucoesDevolucao.valorPermitido),
        ]);
    }
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
    // @ts-ignore
    this.credencial = this.usuario.credenciais.find((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
  }

  cancelar() {
    return this.router.navigate(['/extrato/' + this.idConta]);
  }

  async confirmar() {
    const ativo = await this.verificarCartaoAtivo();
    if (!ativo) {
      return;
    }
    const valido = await this.verificarSeguranca();
    if (!valido) {
      return;
    }
    const devolucao = this.formDevolver.getRawValue();
    await loading(
      this.pixService
        .devolver(this.usuario, devolucao, this.operacao, this.idConta)
        .subscribe({
          next: async (retorno: any) => {
            const data = await this.apresentarSucesso();
            if (retorno.isSuccess && data && data.role == 'secundaria') {
              await this.router.navigate([
                `/extrato/${this.idConta}/comprovante/${retorno.value.ordemPagamento.endToEnd}`,
              ]);
              return;
            }
          },
          error: (erro: any) => {
            const mensagemErro =
              erro.error?.erroMessage?.errors[0].message ||
              'Houve um erro inesperado.';
            this.apresentarErro(mensagemErro);
          },
        })
    );
  }

  async verificarSeguranca() {
    if (
      this.credencial.metodoSegurancaTransacao ==
      MetodoSegurancaEnum.NaoVerificar
    ) {
      return true;
    }

    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: this.credencial.metodoSegurancaTransacao,
        idCredencial: this.credencial.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Devolução realizada com sucesso!',
        mensagem:
          'Sua devolução do pix foi realizada ao favorecido e em instantes estará disponível na conta.',
        urlRetorno: '/inicio',
        classeImagem: 'sucesso-3',
        tituloBotaoSecundario: 'Ver comprovante',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Erro ao devolver',
        mensagem: message,
        urlRetorno: '/inicio',
        tituloBotao: 'Tentar novamente',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async verificarCartaoAtivo() {
    const credenciais = this.usuario.credenciais.filter((x: Credencial) => {
      const conta = x.contas.find((c) => c.idConta == this.idConta);
      return conta ? x : null;
    });
    let credencialAtiva = null;
    let credencialInativa = null;
    if (credenciais.length) {
      for (const cartao of credenciais) {
        if (cartao.status == 1) {
          credencialAtiva = cartao;
        } else {
          credencialInativa = cartao;
        }
      }
    }
    if (credencialAtiva) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalAtivarCartaoComponent,
      showBackdrop: true,
      backdropDismiss: true,
      componentProps: {
        credencial: this.credencial,
      },
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }
}
