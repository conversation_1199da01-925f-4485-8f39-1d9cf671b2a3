<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+idConta+'/enviar'">Devolver Pix</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <mobile-titulo-secao>Informações do destinatário</mobile-titulo-secao>
    <article>
      <div>
        <p>Valor</p>
        <p><b>{{ operacao.valor | currency: 'BRL' }}</b></p>
      </div>
      <div>
        <p>Data</p>
        <p><b>{{ operacao.data | date:'dd/MM/yyyy' }}</b></p>
      </div>
      <div>
        <p>Instituição</p>
        <p><b>{{ operacao.origemMovimento.nomeInstituicao }}</b></p>
      </div>
      <div>
        <p>CPF/CNPJ</p>
        <p><b>{{ operacao.origemMovimento.inscricaoNacional | maskHideCpfCnpj }}</b></p>
      </div>
      <div>
        <p>Descrição</p>
        <p><b>{{ operacao.campoLivre }}</b></p>
      </div>
      <p>ID da transação</p>
      <p>{{ operacao.endToEnd }}</p>
    </article>
  </section>
  <section>
    <mobile-titulo-secao>Informações para devolução</mobile-titulo-secao>
    <form [formGroup]="formDevolver">
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="0,00" type="tel" formControlName="valorFormatado"
                   errorText="Informe um valor válido" [brmasker]="{money: true, thousand: '.',  decimalCaracter: ',', decimal: 2}"
                   helperText="Valor entre R$ 0,01 até {{operacao?.instrucoesDevolucao?.valorPermitido | currency:'BRL'}}">
          <div slot="label" class="custom-label">Valor</div>
          <ion-text slot="start">R$</ion-text>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" placeholder="Digite um motivo para devolver" type="text"
                   formControlName="motivo" maxlength="200"
                   [maskito]="textMaskOptions"
                   [maskitoElement]="maskPredicate"
                   errorText="Informe um motivo">
          <div slot="label" class="custom-label">Motivo</div>
        </ion-input>
      </ion-item>
    </form>
  </section>
  <section class="ion-margin-top">
    <ion-button expand="block" class="btn ion-margin-bottom" [disabled]="formDevolver.invalid" (click)="confirmar()">
      Confirmar
    </ion-button>
    <ion-button expand="block" fill="outline" (click)="cancelar()">Cancelar</ion-button>
  </section>
</ion-content>

