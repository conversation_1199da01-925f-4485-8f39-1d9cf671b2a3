import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { pixCopiaColaMask } from '@utils/masks.util';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { ValidatorsApp } from '@utils/validators.util';
import { loading } from '@utils/loading.util';
import { PixService } from '@corporativo/shared';
import { ModalErroComponent } from '@corporativo/modals';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'mobile-copia-cola',
  templateUrl: './copia-cola.component.html',
  styleUrl: './copia-cola.component.scss',
  standalone: false,
})
export class CopiaColaComponent {
  idConta!: number;
  formConsultar = new FormGroup({
    valor: new FormControl('', [
      Validators.required,
      ValidatorsApp.pixValidator(),
    ]),
  });
  maskOptions: MaskitoOptions = pixCopiaColaMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();

  constructor(
    private activatedRoute: ActivatedRoute,
    private pixService: PixService,
    private router: Router,
    private modalController: ModalController
  ) {}

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }

  async continuar() {
    const data: any = this.formConsultar.getRawValue();
    await loading(
      this.pixService.consultarQrCode(data.valor).subscribe({
        next: (retorno: any) => {
          const { dados, destino, chave } = retorno;
          if (!dados.valor) {
            this.router.navigate(
              [`/pix/${this.idConta}/enviar/informar-valor`],
              { state: { chave } }
            );
            return;
          }
          this.router.navigate([`/pix/${this.idConta}/enviar/confirmar`], {
            skipLocationChange: true,
            state: { destinatario: destino, dados },
          });
        },
        error: (erro: any) => {
          const message = erro.DTL ? erro.DTL[0].message : erro.msg;
          this.apresentarErro(
            message || 'Erro inesperado ao consultar o código Pix. FP002'
          );
        },
      })
    );
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Erro ao consultar',
        mensagem: message,
        urlRetorno: '/inicio',
        tituloBotao: 'Tentar novamente',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }
}
