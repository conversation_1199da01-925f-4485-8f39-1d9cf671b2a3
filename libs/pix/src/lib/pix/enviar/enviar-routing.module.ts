import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EnviarComponent } from './enviar.component';
import {
  ConfirmarComponent,
  DevolverComponent,
  InformarDadosComponent,
  InformarValorComponent,
  VerificarChaveComponent,
} from '@corporativo/pix';
import { CopiaColaComponent } from './copia-cola/copia-cola.component';

const routes: Routes = [
  {
    path: '',
    component: EnviarComponent,
  },
  {
    path: 'informar-dados',
    component: InformarDadosComponent,
  },
  {
    path: 'informar-valor',
    component: InformarValorComponent,
  },
  {
    path: 'confirmar',
    component: ConfirmarComponent,
  },
  {
    path: 'verificar',
    component: VerificarChaveComponent,
  },
  {
    path: 'devolver',
    component: DevolverComponent,
  },
  {
    path: 'copia-cola',
    component: CopiaColaComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class EnviarRoutingModule {}
