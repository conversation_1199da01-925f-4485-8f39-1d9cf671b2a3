import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EnviarComponent } from './enviar.component';
import { EnviarRoutingModule } from './enviar-routing.module';
import { IonicModule } from '@ionic/angular';
import { SaldoModule, SaldoSimplesModule } from '@corporativo/saldo';
import {
  EstadoVazioModule,
  SearchbarModule,
  SkeletonModule,
  TitleToolbarModule,
  TituloSecaoModule,
} from '@corporativo/components';
import { ReactiveFormsModule } from '@angular/forms';
import { DirectivesModule, PipesModule } from '@corporativo/shared';
import {
  ConfirmarComponent,
  DevolverComponent,
  FavorecidoModule,
  InformarDadosComponent,
  InformarValorComponent,
  SelecionarParticipantesComponent,
  VerificarChaveComponent,
} from '@corporativo/pix';
import { MaskitoDirective } from '@maskito/angular';
import { CopiaColaComponent } from './copia-cola/copia-cola.component';

@NgModule({
  declarations: [
    EnviarComponent,
    InformarDadosComponent,
    InformarValorComponent,
    ConfirmarComponent,
    VerificarChaveComponent,
    SelecionarParticipantesComponent,
    DevolverComponent,
    CopiaColaComponent,
  ],
  imports: [
    CommonModule,
    IonicModule,
    EnviarRoutingModule,
    SaldoModule,
    TitleToolbarModule,
    SaldoSimplesModule,
    ReactiveFormsModule,
    TituloSecaoModule,
    SearchbarModule,
    PipesModule,
    SkeletonModule,
    FavorecidoModule,
    DirectivesModule,
    MaskitoDirective,
    EstadoVazioModule,
  ],
})
export class EnviarModule {}
