import { Component, ViewChild } from '@angular/core';
import { IonModal } from '@ionic/angular';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { cnpjMask, cpfMask, numberMask } from '@utils/masks.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { eCNPJ, eCPF, ValidatorsApp } from '@utils/validators.util';
import { debounceTime } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { PixService } from '@corporativo/shared';

@Component({
  selector: 'mobile-informar-dados',
  templateUrl: './informar-dados.component.html',
  styleUrl: './informar-dados.component.scss',
  standalone: false,
})
export class InformarDadosComponent {
  @ViewChild(IonModal, { static: true }) modalParicipantes!: IonModal;
  instituicao: any;
  maskOptions: MaskitoOptions = numberMask;
  maskNumberOptions: MaskitoOptions = numberMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();
  formDados = new FormGroup({
    instituicao: new FormControl('', Validators.required),
    agencia: new FormControl('', Validators.required),
    conta: new FormControl('', Validators.required),
    tipo: new FormControl('1', Validators.required),
    propriaConta: new FormControl(false),
    nome: new FormControl('', Validators.required),
    documento: new FormControl('', [
      Validators.required,
      Validators.minLength(11),
      Validators.maxLength(14),
    ]),
  });
  idConta!: number;

  constructor(
    private pixService: PixService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {
    const controlDocumento = this.formDados.get('documento');
    controlDocumento?.valueChanges
      .pipe(debounceTime(500))
      .subscribe((valor) => {
        controlDocumento?.setValidators([
          Validators.required,
          Validators.minLength(11),
          Validators.maxLength(14),
        ]);
        this.maskOptions = numberMask;
        if (!valor || !valor.length) {
          return;
        }
        if (eCPF(valor)) {
          controlDocumento?.setValidators([
            Validators.required,
            ValidatorsApp.cpf(),
          ]);
          this.maskOptions = cpfMask;
        }
        if (eCNPJ(valor)) {
          controlDocumento?.setValidators([
            Validators.required,
            ValidatorsApp.cnpj(),
          ]);
          this.maskOptions = cnpjMask;
        }
      });
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }

  selecionarInstituicao(item: any) {
    this.instituicao = item;
    this.formDados.get('instituicao')?.setValue(this.instituicao);
    this.modalParicipantes.dismiss();
  }

  continuar() {
    this.router.navigate([`pix/${this.idConta}/enviar/informar-valor`], {
      state: { dados: this.formDados.getRawValue() },
    });
  }

  alterarParaPropriaConta(event: CustomEvent) {
    if (event.detail.checked) {
      this.formDados.get('nome')?.clearValidators();
      this.formDados.get('nome')?.reset();
      this.formDados.get('documento')?.clearValidators();
      this.formDados.get('documento')?.reset();
    } else {
      this.formDados.get('nome')?.setValidators([Validators.required]);
      this.formDados.get('documento')?.reset();
      this.formDados
        .get('documento')
        ?.setValidators([
          Validators.required,
          Validators.minLength(11),
          Validators.maxLength(14),
        ]);
      this.formDados.get('nome')?.reset();
    }
  }
}
