ion-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
}

ion-item {
  ion-label {
    .placeholder {
      font-weight: 500;
      color: var(--ion-color-text-default);
      opacity: 0.3;
      font-size: 16px;
    }

    .valor {
      font-weight: var(--ion-font-weight-label);
      color: var(--ion-color-text-default);
      font-size: 16px;
    }
  }

  ion-icon {
    color: var(--ion-color-quartenary);
    font-size: 18px;
  }
}

h3 {
  color: var(--ion-color-gray-50);
  font-size: 16px;
  font-weight: 600;
}

h2 {
  color: var(--ion-color-gray-50);
  font-size: 12px;
  font-weight: 600;
}

.div-image {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.imagem-valloo {
  width: 40%;
}
