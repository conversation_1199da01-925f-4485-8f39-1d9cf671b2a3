<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/'+idConta+'/enviar'">Agência e conta</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <h3>Informações bancárias</h3>
    <form [formGroup]="formDados">
      <ion-item class="ion-margin-bottom" detail="false" button id="modal-participantes">
        <ion-label>
          <h2>Instituição</h2>
          <p *ngIf="!instituicao" class="placeholder">Selecione</p>
          <p *ngIf="instituicao" class="valor">{{ instituicao.nome }}</p>
        </ion-label>
        <ion-icon name="chevron-down-outline" slot="end" color="primary"></ion-icon>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" formControlName="agencia" placeholder="Número da agência" type="tel"
                   [errorText]="" [counter]="true"
                   maxlength="4" [maskito]="maskNumberOptions" [maskitoElement]="maskPredicate">
          <div slot="label" class="custom-label">Número da agência</div>
        </ion-input>
      </ion-item>
      <ion-item lines="none">
        <ion-input labelPlacement="stacked" formControlName="conta" placeholder="Número da conta com dígito" type="tel"
                   errorText=""
                   [counter]="true" maxlength="20" [maskito]="maskNumberOptions" [maskitoElement]="maskPredicate">
          <div slot="label" class="custom-label">Número da conta com dígito</div>
        </ion-input>
      </ion-item>
      <ion-item class="ion-margin-bottom">
        <ion-select label="Tipo da conta" formControlName="tipo" interface="popover" placeholder="Selecione"
                    label-placement="stacked"
                    toggleIcon="chevron-down-outline" expandedIcon="chevron-up-outline">
          <ion-select-option value="0">Conta poupança</ion-select-option>
          <ion-select-option value="1">Conta corrente</ion-select-option>
          <ion-select-option value="3">Conta salário</ion-select-option>
          <ion-select-option value="6">Conta pagamento</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item class="ion-margin-bottom">
        <ion-toggle #minhaConta (ionChange)="alterarParaPropriaConta($event)" formControlName="propriaConta">A conta
          para envio é minha
        </ion-toggle>
      </ion-item>
      <ng-container *ngIf="!minhaConta.checked">
        <h3>Informações pessoais</h3>
        <ion-item lines="none">
          <ion-input labelPlacement="stacked" formControlName="nome" placeholder="Digite o nome do favorecido"
                     type="text"
                     errorText="Informe um valor válido">
            <div slot="label" class="custom-label">Nome do favorecido</div>
          </ion-input>
        </ion-item>
        <ion-item lines="none">
          <ion-input labelPlacement="stacked" formControlName="documento" placeholder="Digite um CPF ou CNPJ" type="tel"
                     errorText="Informe um CPF/CNPJ válido" [maskito]="maskOptions" [maskitoElement]="maskPredicate">
            <div slot="label" class="custom-label">CPF/CNPJ</div>
          </ion-input>
        </ion-item>
      </ng-container>
    </form>
  </section>
</ion-content>
<ion-modal trigger="modal-participantes" #modal>
  <ng-template>
    <mobile-selecionar-participantes
      class="ion-page"
      (selectionChange)="selecionarInstituicao($event)"
      (selectionCancel)="modal.dismiss()"
    ></mobile-selecionar-participantes>
  </ng-template>
</ion-modal>
<ion-footer>
  <ion-button class="btn" expand="block" [disabled]="formDados.invalid" (click)="continuar()">Continuar</ion-button>
  <div class="div-image">
    <ion-img class="imagem-valloo" src="/assets/images/logo-valloo.svg"></ion-img>
  </div>
</ion-footer>
