<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [buttonModal]="true" (closeEmitter)="irParaInicio()">Pix</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <section>
    <div class="subsection">
      <mobile-titulo-secao>Meu cartão</mobile-titulo-secao>
    </div>
    <mobile-cartoes [idCredencial]="idCredencial" [showNext]="false"></mobile-cartoes>
  </section>
  <section *ngIf="contas">
    <mobile-saldo-simples (trocarConta)="trocarConta($event)" [contas]="contas"
                          [idConta]="idConta" [showNext]="false"></mobile-saldo-simples>
  </section>
  <section *ngIf="temAlgumProdutoAtivo(['pix_enviar', 'pix_meus_agendamentos', 'pix_ler_qrcode'])">
    <article appDynamicClass>
      <mobile-button *ngIf="temProdutoAtivo('pix_enviar')" icon="enviar-dinheiro" [routerLink]="'enviar'" [color]="'secondary'" nome="Enviar"></mobile-button>
      <mobile-button *ngIf="temProdutoAtivo('pix_meus_agendamentos')" icon="meus-agendamentos" [routerLink]="'agendamentos'" [color]="'primary'"
                            nome="Meus<br/>agendamentos"></mobile-button>
      <mobile-button *ngIf="temProdutoAtivo('pix_ler_qrcode')" icon="ler-qrcode" (click)="lerQrcode()" [color]="'secondary'"
                            nome="Ler um <br/>QR Code"></mobile-button>
    </article>
  </section>
  <section *ngIf="temAlgumProdutoAtivo(['pix_gerar_qrcode', 'pix_copia_cola', 'pix_automatico'])">
    <article appDynamicClass>
      <mobile-button *ngIf="temProdutoAtivo('pix_copia_cola')" icon="copy-outline" [routerLink]="'enviar/copia-cola'" [color]="'secondary'"
                     nome="Pix Copia <br/> e cola"></mobile-button>
      <mobile-button *ngIf="temProdutoAtivo('pix_gerar_qrcode')" icon="gerar-qrcode" [routerLink]="'receber'" [color]="'secondary'"
                      nome="Gerar um <br/>QR Code"></mobile-button>
      <mobile-button *ngIf="temProdutoAtivo('pix_automatico')" icon="pix-automatico" [routerLink]="'automatico'" [color]="'secondary'"
                     nome="Pix<br/>Automático"></mobile-button>
    </article>
  </section>
  <section class="configuracoes">
    <h3 *ngIf="temProdutoAtivo('pix_meus_limites')">Meus limites</h3>
    <ion-item *ngIf="temProdutoAtivo('pix_meus_limites')" lines="none" [button]="true" [detail]="true" routerLink="limites">
      <ion-label>
        <p>Altere seus limites de envio de Pix, você pode alterar seu limite por período (diário ou noturno) ou por
          envio.</p>
      </ion-label>
    </ion-item>
      <h3 *ngIf="temProdutoAtivo('pix_minhas_chaves')">Minhas chaves</h3>
      <ion-item *ngIf="temProdutoAtivo('pix_minhas_chaves')" lines="none" [button]="true" [detail]="true" routerLink="chaves">
        <ion-label>
          <p>Cadastre uma chave Pix ou faça a portabilidade da chave de outro aplicativo.</p>
        </ion-label>
      </ion-item>
  </section>
</ion-content>
