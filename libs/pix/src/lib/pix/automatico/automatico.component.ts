import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'mobile-pix-automatico',
  templateUrl: './automatico.component.html',
  styleUrls: ['./automatico.component.scss'],
  standalone: false,
})
export class AutomaticoComponent {
  idConta!: number;
  situacao = 'pendente';

  constructor(
    private activatedRoute: ActivatedRoute
  ) {
  }

  ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }
}
