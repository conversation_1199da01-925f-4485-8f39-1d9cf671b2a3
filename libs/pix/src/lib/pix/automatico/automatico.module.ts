import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AutomaticoComponent } from './automatico.component';
import { AutomaticoRoutingModule } from './automatico-routing.module';
import { IonicModule } from '@ionic/angular';
import { SaldoModule, SaldoSimplesModule } from '@corporativo/saldo';
import { EstadoVazioModule, TitleToolbarModule, TituloSecaoModule } from '@corporativo/components';
import { ReactiveFormsModule } from '@angular/forms';
import { DirectivesModule, PipesModule } from '@corporativo/shared';
import { MaskitoDirective } from '@maskito/angular';
import { SaibaMaisComponent } from './saiba-mais/saiba-mais.component';

@NgModule({
  declarations: [
    AutomaticoComponent,
    SaibaMaisComponent
  ],
  imports: [
    CommonModule,
    AutomaticoRoutingModule,
    IonicModule,
    SaldoModule,
    TitleToolbarModule,
    ReactiveFormsModule,
    SaldoSimplesModule,
    PipesModule,
    TituloSecaoModule,
    DirectivesModule,
    MaskitoDirective,
    EstadoVazioModule
  ]
})
export class AutomaticoModule {
}
