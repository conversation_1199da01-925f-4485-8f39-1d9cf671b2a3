<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [rotaPadrao]="'/pix/' + idConta">Autorizações</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content [fullscreen]="true" class="content-default">
  <section>
    <mobile-titulo-secao>Histórico</mobile-titulo-secao>
  </section>
  <section>
    <ion-segment [scrollable]="true" [value]="situacao">
      <ion-segment-button value="pendente">
        Pendentes
      </ion-segment-button>
      <ion-segment-button value="ativo">
        Ativos
      </ion-segment-button>
      <ion-segment-button value="todos">
        Todos
      </ion-segment-button>
    </ion-segment>
  </section>
  <section>
    <div class="estado-vazio">
      <mobile-estado-vazio message="Nenhum Pix automático encontrado."></mobile-estado-vazio>
    </div>
  </section>
</ion-content>
<ion-footer class="ion-no-border">
  <ion-button expand="block" fill="outline" routerLink="saiba-mais">Saiba mais</ion-button>
</ion-footer>
