import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Platform } from '@ionic/angular';

@Component({
  selector: 'mobile-pix-automatico-saiba-mais',
  templateUrl: './saiba-mais.component.html',
  styleUrl: './saiba-mais.component.scss',
  standalone: false,
})
export class SaibaMaisComponent {
  idConta!: number;

  constructor(
    private router: Router,
    private platform: Platform,
    private activatedRoute: ActivatedRoute
  ) {}

  async ionViewDidEnter() {
    const idConta = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
    }
  }
}
