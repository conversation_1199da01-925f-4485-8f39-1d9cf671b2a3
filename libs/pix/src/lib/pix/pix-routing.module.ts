import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PixComponent } from './pix.component';
import { LerQrcodeComponent } from './ler-qrcode/ler-qrcode.component';

const routes: Routes = [
  {
    path: '',
    component: PixComponent,
  },
  {
    path: 'enviar',
    loadChildren: () =>
      import('./enviar/enviar.module').then((m) => m.EnviarModule),
  },
  {
    path: 'limites',
    loadChildren: () =>
      import('./limites/limites.module').then((m) => m.LimitesModule),
  },
  {
    path: 'chaves',
    loadChildren: () =>
      import('./chaves/chaves.module').then((m) => m.ChavesModule),
  },
  {
    path: 'agendamentos',
    loadChildren: () =>
      import('./agendamentos/agendamentos.module').then(
        (m) => m.AgendamentosModule
      ),
  },
  {
    path: 'receber',
    loadChildren: () =>
      import('./receber/receber.module').then((m) => m.ReceberModule),
  },
  {
    path: 'ler-qrcode',
    component: LerQrcodeComponent,
  },
  {
    path: 'automatico',
    loadChildren: () =>
      import('./automatico/automatico.module').then((m) => m.AutomaticoModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PixRoutingModule {}
