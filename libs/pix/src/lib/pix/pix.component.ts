import { Component, OnInit } from '@angular/core';
import { loading } from '@utils/loading.util';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController, Platform } from '@ionic/angular';
import {
  AuthService,
  Conta,
  PixService,
  ProdutoService,
} from '@corporativo/shared';
import { lastValueFrom } from 'rxjs';
import { ModalErroComponent } from '@corporativo/modals';
import {
  CapacitorBarcodeScanner,
  CapacitorBarcodeScannerAndroidScanningLibrary,
  CapacitorBarcodeScannerCameraDirection,
  CapacitorBarcodeScannerScanOrientation,
  CapacitorBarcodeScannerTypeHint,
} from '@capacitor/barcode-scanner';
import type { CapacitorBarcodeScannerScanResult } from '@capacitor/barcode-scanner/dist/esm/definitions';

@Component({
  selector: 'mobile-pix',
  templateUrl: './pix.component.html',
  styleUrl: './pix.component.scss',
  standalone: false,
})
export class PixComponent implements OnInit {
  usuario!: any;
  idConta!: number;
  idCredencial!: number;
  contas!: Conta[];
  conta!: Conta;
  produtos: any[] = [];

  constructor(
    private pixService: PixService,
    private router: Router,
    private platform: Platform,
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private modalController: ModalController,
    private produtoService: ProdutoService
  ) {
    this.usuario = this.authService.getUser();
  }

  ionViewDidEnter() {
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.idConta = +idConta;
      for (const credencial of this.usuario.credenciais) {
        const conta = credencial.contas.find(
          (c: any) => c.idConta == this.idConta
        );
        if (conta) {
          this.idCredencial = credencial.idCredencial;
          this.contas = credencial.contas.filter(
            (x: any) => x.idConta == this.idConta
          );
          this.conta = conta;
        }
      }
      lastValueFrom(this.pixService.buscarLimite(this.idConta));
    }
  }

  async ngOnInit() {
    const idConta: any = this.activatedRoute.snapshot.paramMap.get('idConta');
    if (idConta) {
      this.buscarProdutos(idConta);
    }
  }

  buscarProdutos(idConta: number) {
    this.produtoService.getFuncionalidades(idConta).subscribe({
      next: (produtos: any) => {
        const functionalidadesPix: any[] =
          this.produtoService.getFuncionalidadesPix();
        this.produtos = produtos.concat(functionalidadesPix);
      },
    });
  }

  temProdutoAtivo(nomeProduto: string): boolean {
    return this.produtos.some(
      (produto: any) => produto.codigo === nomeProduto && produto.ativo
    );
  }

  temAlgumProdutoAtivo(nomesProdutos: string[]): boolean {
    return nomesProdutos.some((nomeProduto) =>
      this.produtos.some(
        (produto: any) => produto.codigo === nomeProduto && produto.ativo
      )
    );
  }

  async lerQrcode() {
    if (!this.platform.is('hybrid')) {
      // com valor
      // '00020101021126460014br.gov.bcb.pix0111069869154700209descricao520400005303986540520.005802BR5922WILLIAN COUTO DA SILVA6008Brasilia62170513identificador6304637D'
      // sem valor
      // '00020101021126330014br.gov.bcb.pix0111069869154705204000053039865802BR5922WILLIAN COUTO DA SILVA6008Brasilia62070503***6304196D'
      // com erro
      // '00020101021226850014br.gov.bcb.pix2563qrcodepix.bb.com.br\/pix\/v2\/be3175e4-e8ed-40e4-bc68-9983b601cade5204000053039865406327.535802BR5925SECRETARIA DA RECEITA FED6008BRASILIA62070503***63041663'
      this.consultar(
        '00020101021126460014br.gov.bcb.pix0111069869154700209descricao520400005303986540520.005802BR5922WILLIAN COUTO DA SILVA6008Brasilia62170513identificador6304637D'
      );
    } else {
      const result: CapacitorBarcodeScannerScanResult | null =
        await this.scan();
      if (result) {
        this.consultar(result.ScanResult);
      }
    }
  }

  async scan() {
    const options: any = {
      hint: CapacitorBarcodeScannerTypeHint.QR_CODE,
      scanOrientation: CapacitorBarcodeScannerScanOrientation.LANDSCAPE,
      cameraDirection: CapacitorBarcodeScannerCameraDirection.BACK,
      scanInstructions: 'Posicione o QRCode na marcação',
      android: {
        scanningLibrary: CapacitorBarcodeScannerAndroidScanningLibrary.ZXING,
      },
    };
    return CapacitorBarcodeScanner.scanBarcode(options);
  }

  async consultar(qrcode: any) {
    await loading(
      this.pixService.consultarQrCode(qrcode).subscribe({
        next: (retorno: any) => {
          const { dados, destino, chave } = retorno;
          if (!dados.valor) {
            this.router.navigate(
              [`/pix/${this.idConta}/enviar/informar-valor`],
              { state: { chave } }
            );
            return;
          }
          this.router.navigate([`/pix/${this.idConta}/enviar/confirmar`], {
            skipLocationChange: true,
            state: { destinatario: destino, dados },
          });
        },
        error: (erro: any) => {
          const message = erro.DTL ? erro.DTL[0].message : erro.msg;
          this.apresentarErro(
            message || 'Houve um erro inesperado ao consultar. FP001'
          );
        },
      })
    );
  }

  async apresentarErro(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: 'Erro ao consultar',
        mensagem: message,
        urlRetorno: '/inicio',
        tituloBotao: 'Tentar novamente',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  trocarConta(conta: Conta) {
    this.conta = conta;
  }

  irParaInicio() {
    return this.router.navigate(['/inicio']);
  }
}
