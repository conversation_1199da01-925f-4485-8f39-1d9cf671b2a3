import { environment, InstituicaoEnum } from '../shared/src';

export function isVallooMulticontas() {
  return environment.idInstituicao == InstituicaoEnum.VallooMulticontas;
}

export function calcularDigitoVerificadorConta(sequenciaNumerica: number) {
  let sequenciaNumericaAsString = padLeftZeros(sequenciaNumerica.toString(), 8);
  let n = 9;
  let soma = 0;

  for (let i = 0; i < sequenciaNumericaAsString.length; i++) {
    let digito: any = sequenciaNumericaAsString.substring(i, i + 1);
    soma += digito * n;
    n--;
  }
  let restoDaDivisao = soma % 11;
  let digitoVerificador = 11 - restoDaDivisao;

  if (digitoVerificador == 10 || digitoVerificador == 11) {
    return 0;
  } else {
    return digitoVerificador;
  }

}

export function padLeftZeros(inputString: string, length: number) {
  if (inputString.length >= length) {
    return inputString;
  }
  let sb = String();
  while (sb.length < length - inputString.length) {
    sb = sb + "0"
  }
  sb = sb + inputString;
  return sb;
}

export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, _) => {
    const reader: any = new FileReader();
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(blob);
  });
}
