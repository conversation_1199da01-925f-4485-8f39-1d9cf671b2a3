import { Strings } from './strings.util';

export class TextUtil {
  public static compareTextIndexOf(str1: string, str2: string) {
    // if (!str1 || !str2) {
    //   return ;
    // }
    return (
      Strings.removerAcentos(str1.toLowerCase()).indexOf(
        Strings.removerAcentos(str2.toLowerCase())
      ) > -1
    );
  }

  public static compare(str1: string, str2: string) {
    return (
      Strings.removerAcentos(str1).toLowerCase() ===
      Strings.removerAcentos(str2).toLowerCase()
    );
  }

  /**
   * Converte um numero com string conttenando mil ou mi
   */
  public static converterNumeroEmString(num: number): string | number | any {
    if (!num) {
      return '0';
    } else if (num < 1000) {
      return num.toString();
    } else if (num > 1000 && num < 1000000) {
      return (num / 1000).toFixed(2) + ' Mil';
    } else if (num > 1000000) {
      return (num / 1000000).toFixed(2) + ' Mi';
    }
  }

  public static removeNotDigit(str: string) {
    return str.replace(/\D/g, '');
  }

  public static converteData(
    data: string,
    split: string = '/',
    separador: string = '-'
  ) {
    if (!data) {
      return '';
    }

    const retorno = data.split(split);
    return retorno[2] + separador + retorno[1] + separador + retorno[0];
  }

  public static converteDataBR(
    data: string,
    split: string = '-',
    separador: string = '/'
  ) {
    if (!data) {
      return '';
    }

    const retorno = data.split(split);
    return retorno[2] + separador + retorno[1] + separador + retorno[0];
  }

  public static converteCPFouCNPJ(str: string): string {
    if (str.length <= 11) {
      return (str = this.mascaraCpf(str));
    } else {
      return (str = this.mascaraCnpj(str));
    }
  }

  public static mascaraCpf(valor: string) {
    return valor.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, '$1.$2.$3-$4');
  }

  public static mascaraCnpj(valor: string) {
    return valor.replace(
      /(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g,
      '$1.$2.$3/$4-$5'
    );
  }

  public static converteCep(valor: string) {
    return this.aplicarMascara(valor, '#####-###');
  }

  public static aplicarMascara(value: string, pattern: any) {
    let i = 0;
    const v = value.toString();
    return pattern.replace(/#/g, (_: any) => v[i++]);
  }

  public static converterCelular(valor: string) {
    if (valor.length === 10) {
      return valor.replace(/^(\d{2})(\d{4})(\d{4}).*/, '($1) $2-$3');
    } else {
      return valor.replace(/^(\d{2})(\d{5})(\d{4}).*/, '($1) $2-$3');
    }
  }

  public static removeCaracteres(valor: string) {
    return valor
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-zA-Z0-9 ]/g, '');
  }
}
