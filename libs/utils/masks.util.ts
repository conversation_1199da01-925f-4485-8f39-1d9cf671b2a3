import {
  maskitoDateOptionsGenerator,
  maskitoNumberOptionsGenerator,
} from '@maskito/kit';
import { MaskitoOptions } from '@maskito/core';

export const moneyMask = maskitoNumberOptionsGenerator({
  decimalZeroPadding: true,
  precision: 2,
  decimalSeparator: ',',
  thousandSeparator: '.',
  min: 0,
  prefix: 'R$ ',
});

export const expiredMask = maskitoDateOptionsGenerator({
  mode: 'mm/yy',
  separator: '/',
});

export const phoneMask = {
  mask: [
    '(',
    /\d/,
    /\d/,
    ')',
    ' ',
    /\d/,
    ' ',
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    '-',
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ],
} as MaskitoOptions;

export const cpfMask = {
  mask: [
    /\d/,
    /\d/,
    /\d/,
    '.',
    /\d/,
    /\d/,
    /\d/,
    '.',
    /\d/,
    /\d/,
    /\d/,
    '-',
    /\d/,
    /\d/,
  ],
} as MaskitoOptions;

export const cnpjMask = {// 67.576.495/0001-72
  mask: [
    /\d/,
    /\d/,
    '.',
    /\d/,
    /\d/,
    /\d/,
    '.',
    /\d/,
    /\d/,
    /\d/,
    '/',
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    '-',
    /\d/,
    /\d/,
  ],
} as MaskitoOptions;

export const DataNascimento = {
  mask: [
    /\d/,
    /\d/,
    '/',
    /\d/,
    /\d/,
    '/',
    /\d/,
    /\d/,
    /\d/,
    /\d/,
  ],
} as MaskitoOptions;

export const cep = {
  mask: [
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    /\d/,
    '-',
    /\d/,
    /\d/,
    /\d/,
  ],
} as MaskitoOptions;

export const cardMask = {
  mask: [
    ...new Array(4).fill(/\d/),
    ' ',
    ...new Array(4).fill(/\d/),
    ' ',
    ...new Array(4).fill(/\d/),
    ' ',
    ...new Array(4).fill(/\d/),
    ' ',
    ...new Array(3).fill(/\d/),
  ],
} as MaskitoOptions;

export const cvvMask = {
  mask: [...new Array(3).fill(/\d/)],
}

export const numberMask = {
  mask: /^\d+$/
}

export const noneMask = {
  mask: /^/,
} as MaskitoOptions;

export const textMask = {
  mask: /[0-9A-Za-záàâãéèêíïóôõöúçñÁÀÂÃÉÈÍÏÓÔÕÖÚÇÑ\s!"#$%&'()*+,-./:;<=>?@^_`{|}~]+$/
}

export const boletoMask = {
  mask: [
    ...new Array(12).fill(/\d/),
    ' ',
    ...new Array(12).fill(/\d/),
    ' ',
    ...new Array(12).fill(/\d/),
    ' ',
    ...new Array(12).fill(/\d/)
  ],
} as MaskitoOptions;

export const pixCopiaColaMask = {
  mask: /^[a-zA-Z0-9@:$%*()+,\-.\/\s]+$/
}
