export function converterFormdataUtil(formData: FormData, data: any, parentKey: string | number  = '') {
  if (data && typeof data === 'object' && !(data instanceof Date) && !(data instanceof File)) {
    Object.keys(data).forEach(key => {
      converterFormdataUtil(formData, data[key], parentKey ? `${parentKey}[${key}]` : key);
    });
  } else {
    const value = data == null ? '' : data;
    if (typeof parentKey === "string") {
      formData.append(parentKey, value);
    }
  }
}
