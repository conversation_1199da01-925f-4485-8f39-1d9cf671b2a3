export function dateBrToEn(value: string) {
  if (!value) {
    return '';
  }
  const parts = value.split('/');
  return parts[2] + '-' + parts[1] + '-' + parts[0];
}

export function getNextBusinessDay(date: Date) {
  // Copy date so don't affect original
  date = new Date(+date);
  // Add days until get not Sat or Sun
  do {
    date.setDate(date.getDate() + 1);
  } while (!(date.getDay() % 6));
  return date;
}

export function parseFormatoApresentecao(date: Date) {
  var month = "" + (date.getMonth() + 1),
    day = "" + date.getDate(),
    year = date.getFullYear();

  if (month.length < 2) month = "0" + month;
  if (day.length < 2) day = "0" + day;

  return [day, month, year].join("/");
}

export function parseFormatoServico(
  data: string,
  split: string = "/",
  separador: string = "-"
) {
  if (!data) {
    return "";
  }

  const retorno = data.split(split);
  return retorno[2] + separador + retorno[1] + separador + retorno[0];
}
