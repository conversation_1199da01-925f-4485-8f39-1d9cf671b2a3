import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { dateBrToEn } from './date.util';
import { isAfter, isBefore, isValid } from 'date-fns';

interface ValidationResult {
  [key: string]: boolean;
}

export class ValidatorsApp {
  static birthday(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null;
    }
    if (control?.value?.length !== 10) {
      return { invalid_date: true };
    }
    const date = new Date(dateBrToEn(control.value));
    if (!isValid(date)) {
      return { invalid_date: true };
    }
    const today = new Date();
    if (isBefore(today, date)) {
      return { invalid_date: true };
    }

    return null;
  }

  static dateBr(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null;
    }
    if (control?.value?.length !== 10) {
      return { invalid_date: true };
    }
    const date = new Date(dateBrToEn(control.value));
    if (!isValid(date)) {
      return { invalid_date: true };
    }

    return null;
  }

  static validityCard(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null;
    }
    if (control?.value?.length !== 7) {
      return { invalid_date: true };
    }
    const parts = control.value.split('/');
    const date = new Date(parts[1] + '-' + parts[0] + '-01');
    const today = new Date();
    if (!isValid(date)) {
      return { invalid_date: true };
    }
    if (isAfter(today, date)) {
      return { invalid_date: true };
    }

    return null;
  }

  static cpfCnpj(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (eCNPJ(control.value)) {
        if (!validarCNPJ(control.value)) {
          return { 'invalid': true };
        } else {
          return null;
        }
      }
      if (eCPF(control.value)) {
        if (!validarCPF(control.value)) {
          return { 'invalid': true };
        }
        return null;
      }
      return null;
    };
  }

  static cpf(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      const cpf = control.value.replace(/\D/gi, '');
      if (!cpf) {
        return null;
      }
      let numeros, digitos, soma, i, resultado;
      let digitosIguais = 1;

      if (cpf.length < 11) {
        return { 'length': true };
      }

      for (i = 0; i < cpf.length - 1; i++) {
        if (cpf.charAt(i) !== cpf.charAt(i + 1)) {
          digitosIguais = 0;
          break;
        }
      }

      if (!digitosIguais) {
        numeros = cpf.substring(0, 9);
        digitos = cpf.substring(9);
        soma = 0;

        for (i = 10; i > 1; i--) {
          soma += numeros.charAt(10 - i) * i;
        }

        resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;

        if (resultado.toString() !== digitos.charAt(0)) {
          return { 'invalid': true };
        }

        numeros = cpf.substring(0, 10);
        soma = 0;

        for (i = 11; i > 1; i--) {
          soma += numeros.charAt(11 - i) * i;
        }

        resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;

        if (resultado.toString() !== digitos.charAt(1)) {
          return { 'invalid': true };
        } else {
          return null;
        }
      } else {
        return { 'invalid': true };
      }
    };
  }

  static cnpj(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const cnpj = control.value.replace(/\D/gi, '');
      if (!cnpj) {
        return null;
      }

      if (cnpj.length < 14) {
        return { 'length': true };
      }

      if (!validarCNPJ(cnpj)) {
        return { 'invalid': true };
      } else {
        return null;
      }
    };
  }

  static senhasIguaisValidator(AC: AbstractControl | null) {
    if (AC !== null) {
      const senha = AC?.get('novaSenha')?.value;
      const confirmarSenha = AC?.get('confirmarSenha')?.value;
      if (senha !== confirmarSenha) {
        AC?.get('confirmarSenha')?.setErrors({ senhasIguaisValidator: true });
      } else {
        return null;
      }
    }
  }

  static pixValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const codigoPix = control.value;
      if (!codigoPix) {
        return null;
      }

      const regexPix = /^[a-zA-Z0-9@:$%*()+,\-.\/\s|]+$/;

      const tamanhoMinimo = 25;
      const tamanhoMaximo = 250;

      const valid = regexPix.test(codigoPix) && codigoPix.length >= tamanhoMinimo;
      return valid ? null : { pixInvalid: true };
    };
  }
}

export function eCPF(cpf: string): boolean {
  cpf = cpf.replace(/\D/g, '');
  return cpf.length === 11;
}

export function validarCPF(cpf: string): boolean {
  cpf = cpf.replace(/\D/g, '');
  if (cpf.length !== 11) {
    return false;
  }
  if (/^(\d)\1{10}$/.test(cpf)) {
    return false;
  }
  let soma = 0;
  let resto;
  for (let i = 1; i <= 9; i++) {
    soma += parseInt(cpf[i - 1]) * (11 - i);
  }
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) {
    resto = 0;
  }
  if (resto !== parseInt(cpf[9])) {
    return false;
  }
  soma = 0;
  for (let i = 1; i <= 10; i++) {
    soma += parseInt(cpf[i - 1]) * (12 - i);
  }
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) {
    resto = 0;
  }
  return resto === parseInt(cpf[10]);
}

export function eCNPJ(cnpj: string): boolean {
  cnpj = cnpj.replace(/\D/g, '');
  return cnpj.length > 11;
}

export function validarCNPJ(cnpj: string): boolean {
  cnpj = cnpj.replace(/\D/g, '');
  if (cnpj.length !== 14) {
    return false;
  }
  if (/^(\d)\1{13}$/.test(cnpj)) {
    return false;
  }
  let soma = 0;
  let peso = 5;
  for (let i = 0; i < 12; i++) {
    soma += parseInt(cnpj[i]) * peso;
    peso = peso === 2 ? 9 : peso - 1;
  }
  let resto = soma % 11;
  const digito1 = resto < 2 ? 0 : 11 - resto;
  if (digito1 !== parseInt(cnpj[12])) {
    return false;
  }
  soma = 0;
  peso = 6;
  for (let i = 0; i < 13; i++) {
    soma += parseInt(cnpj[i]) * peso;
    peso = peso === 2 ? 9 : peso - 1;
  }
  resto = soma % 11;
  const digito2 = resto < 2 ? 0 : 11 - resto;
  if (digito2 !== parseInt(cnpj[13])) {
    return false;
  }
  return true;
}

export function validarEmail(email: string): boolean {
  const regexEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regexEmail.test(email);
}

export function validarCelular(telefone: string): boolean {
  const numeroLimpo = telefone.replace(/\D/g, '');
  if (numeroLimpo.length < 10) {
    return false;
  }
  const regexTelefone = /^([14689][0-9]|2[12478]|3([1-5]|[7-8])|5([13-5])|7[193-7])9[0-9]{8}$/;
  return regexTelefone.test(numeroLimpo);
}

export function validarUUID(str: string): boolean {
  const regexUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return regexUUID.test(str);
}

export function greaterThanValidator(minValue: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (value !== null && value !== undefined && value <= minValue) {
      return { 'greaterThan': { 'requiredValue': minValue, 'actualValue': value } };
    }
    return null;
  };
}

export function notEqualValidator(valueToCompare: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    if (value !== null && value !== undefined && value === valueToCompare) {
      return { 'notEqual': { 'requiredValue': valueToCompare, 'actualValue': value } };
    }
    return null;
  };
}
