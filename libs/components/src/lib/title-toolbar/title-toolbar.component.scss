
article {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
}

ion-back-button {
  position: absolute;
  left: 1rem;
  top: 0;
  margin: 0 auto;
  bottom: 0;
}

ion-back-button::part(native) {
  width: 20px;
}

.btn {
  position: absolute;
  left: 0.5rem;
  top: -0.3rem;
  margin: 0 auto;
  font-size: 20px;
  //margin: 0 0 0 calc(2rem - 12px);
  bottom: 0;
  --padding-bottom: 0;
  --padding-top: 0;
  --padding-end: 0;
  --padding-start: 0;
  //min-width: 22px;
  //min-height: 22px;
  width: 22px;
  height: 22px;
}

.btn::part(native) {
  width: 22px;
  height: 22px;

  .button-inner {
    width: 22px;
    height: 22px;
  }

  &::after {
    width: 22px;
    height: 22px;
  }
}

.titulo {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}

h1 {
  color: var(--ion-color-text-default);
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

ion-img {
  width: 27.755px;
  height: 4px;
}

.btn-right {
  position: absolute;
  right: 0;
}
