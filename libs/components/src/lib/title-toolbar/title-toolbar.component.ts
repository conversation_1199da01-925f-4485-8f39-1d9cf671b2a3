import { Component, EventEmitter, Input, Output } from '@angular/core';
import { environment } from '@corporativo/shared';

@Component({
  selector: 'mobile-title-toolbar',
  templateUrl: './title-toolbar.component.html',
  styleUrls: ['./title-toolbar.component.scss'],
  standalone: false,
})
export class TitleToolbarComponent {
  @Input() rotaPadrao = '/inicio';
  @Input() buttonModal = false;
  @Output() closeEmitter = new EventEmitter();
  @Input() mostrarVoltar = true;
  @Input() mostrarBotaoFim = false;
  @Output() acionarBotaoFim = new EventEmitter();

  showTitleDots = environment.showTitleDots;

  closeAction() {
    this.closeEmitter.emit();
  }

  acionarBotaoFimAction() {
    this.acionarBotaoFim.emit();
  }
}
