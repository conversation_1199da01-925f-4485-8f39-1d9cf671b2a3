<article>
  <ng-container *ngIf="mostrarVoltar">
    <ion-back-button
        *ngIf="!buttonModal"
        [defaultHref]="rotaPadrao"
        text="" color="secondary">
    </ion-back-button>
    <ion-button class="btn" slot="start" color="secondary" fill="clear" *ngIf="buttonModal" (click)="closeAction()">
      <ion-icon name="chevron-back-outline" slot="icon-only"></ion-icon>
    </ion-button>
  </ng-container>
  <ng-container *ngIf="mostrarBotaoFim">
    <ion-button (click)="acionarBotaoFimAction()" class="btn-right" slot="end" fill="clear" size="small" color="primary">
      <ion-icon name="share-outline" slot="icon-only"></ion-icon>
    </ion-button>
  </ng-container>
  <div class="titulo">
    <h1 class="cor-titulo-toolbar">
      <ng-content></ng-content>
    </h1>
    <ion-img src="assets/svgs/destaque-titulo.svg" *ngIf="showTitleDots"></ion-img>
  </div>
</article>
