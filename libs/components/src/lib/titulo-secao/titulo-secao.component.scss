article {
  display: flex;
  align-items: center;
  justify-content: space-between;

  div {
    display: flex;
    align-items: center;
    justify-content: center;

    h2 {
      margin: 0;
      color: var(--ion-color-text-subtitulo);
      font-weight: var(--ion-font-weight-subtitulo);
      font-size: 16px;
    }
  }

  ion-button {
    margin: 0;
    height: 24px;
    width: 32px;
    min-width: 22px;
    --padding-bottom: 0;
    --padding-top: 0;
    --padding-start: 0;
    --padding-end: 0;
    --color: var(--ion-color-text-subtitulo);
    font-weight: var(--ion-font-weight-subtitulo);
    font-size: 10px;
    line-height: normal;
    --border-radius: 8px;

    ion-icon {
      font-size: 16px;
    }
  }

}
