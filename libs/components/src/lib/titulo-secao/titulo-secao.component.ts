import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'mobile-titulo-secao',
  templateUrl: './titulo-secao.component.html',
  styleUrls: ['./titulo-secao.component.scss'],
  standalone: false
})
export class TituloSecaoComponent {
  @Input() mostrarOcultarValor!: boolean;
  @Output() ocultarValor = new EventEmitter();
  oculto =  false;

  ocultarValorAction() {
    this.oculto = !this.oculto;
    this.ocultarValor.emit(this.oculto);
  }
}
