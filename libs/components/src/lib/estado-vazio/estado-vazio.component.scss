section {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 1.5rem;

  article {
    background: var(--ion-color-medium);
    height: 98px;
    width: 98px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    ion-icon {
      font-size: 36px;
      color: var(--ion-color-primary);
    }
  }

  .light {
    background: var(--ion-color-gray-900);
  }
}
