import { Component, Input } from '@angular/core';
import { PopoverController } from '@ionic/angular';
import { format } from 'date-fns';

@Component({
  selector: 'mobile-popover-data',
  templateUrl: './popover-data.component.html',
  styleUrls: ['./popover-data.component.scss'],
  standalone: false
})
export class PopoverDataComponent {
  @Input() max!: string;
  @Input() min!: string;
  value = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss");

  constructor(private popoverController: PopoverController) {
  }

  selecionar(event: CustomEvent) {
    this.popoverController.dismiss(event.detail.value);
  }

}
