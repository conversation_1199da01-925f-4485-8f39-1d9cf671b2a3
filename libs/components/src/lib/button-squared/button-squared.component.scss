
.button {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: var(--ion-color-secondary);
  border-radius: 4.25px;
  padding: 16px;
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 5px;

  ion-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    color: var(--ion-color-primary);
  }

  p {
    color: var(--ion-color-primary);
    font-size: 12px;
    text-align: left;
    margin: 0;
    bottom: 10px;
    position: absolute;
    left: 7px;
    width: 88%;
  }
}

ion-button {
  --ion-color-base: transparent !important;
}
