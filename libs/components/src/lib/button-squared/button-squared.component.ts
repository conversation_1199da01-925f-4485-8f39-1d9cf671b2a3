import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'mobile-button-squared',
  templateUrl: './button-squared.component.html',
  styleUrls: ['./button-squared.component.scss'],
  standalone: false
})
export class ButtonSquaredComponent {
  @Input() color = 'medium';
  @Input() icon = '';
  @Input() nome = '';
  @Input() carregando = false;
  @Output() action = new EventEmitter();

  buttonAction() {
    this.action.emit();
  }

}
