import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'mobile-button-rounded',
  templateUrl: './button-rounded.component.html',
  styleUrls: ['./button-rounded.component.scss'],
  standalone: false
})
export class ButtonRoundedComponent {
  @Input() color = 'medium';
  @Input() icon = '';
  @Input() nome = '';
  @Input() carregando = false;
  @Output() action = new EventEmitter();

  buttonAction() {
    this.action.emit();
  }

}
