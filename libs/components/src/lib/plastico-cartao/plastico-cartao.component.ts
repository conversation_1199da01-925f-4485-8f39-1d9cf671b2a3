import { Component, Input, OnInit } from '@angular/core';
import { lastValueFrom, map } from "rxjs";
import { CredencialService } from "@corporativo/shared";
import { Platform } from '@ionic/angular';
import { blobToBase64 } from '@utils/app.util';

@Component({
  selector: 'component-plastico-cartao',
  templateUrl: './plastico-cartao.component.html',
  styleUrls: ['./plastico-cartao.component.scss'],
  standalone: false
})
export class PlasticoCartaoComponent implements OnInit {
  @Input() credencial: any;
  imageBase64!: any;
  orientacaoImagem: string = '';

  constructor(private credencialService: CredencialService, private platform: Platform) {
  }

  ngOnInit() {
    return this.buscarPlastico();
  }

  async buscarPlastico() {
    const request$ = this.platform.is('hybrid') ?
      this.credencialService.baixarPlasticoNative(this.credencial.idPlastico) :
      this.credencialService.buscarPlastico(this.credencial.idPlastico);
    this.imageBase64 = await lastValueFrom(
      request$
        .pipe(
          map( async (x: any) => {
            if (this.platform.is('hybrid')) {
              return `data:image/png;base64,${x.toString()}`;
            }
            const t: string = await blobToBase64(x);
            return `data:image/png;base64,${t.replace('data:application/octet-stream;base64,', '')}`;
          })
        )
    );
    this.verificarOrientacaoImagem();
  }

  verificarOrientacaoImagem() {
    const img = new Image();
    img.src = this.imageBase64;
    img.onload = () => {
      const width = img.width;
      const height = img.height;
      this.orientacaoImagem = width > height ? 'horizontal' : 'vertical';
    };
  }
}
