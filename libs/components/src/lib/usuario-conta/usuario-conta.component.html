<section class="usuario-conta">
  <ion-item [class]="inicio ? 'usuario-inicio' : 'usuario-login'" lines="none" *ngIf="usuario" [button]="mostrarAlterarImagem" [detail]="false" (click)="buttonAction()">
    <ion-avatar slot="start" *ngIf="usuario.imagemPerfil">
      <ion-img [src]="usuario.imagemPerfil"/>
      <div *ngIf="mostrarAlterarImagem" class="escolher-imagem">
        <ion-icon name="chevron-down-outline" color="light"></ion-icon>
      </div>
    </ion-avatar>
    <div *ngIf="!usuario.imagemPerfil" class="iniciais">
      <p>{{usuario.nomeCompleto | iniciais}}</p>
      <div *ngIf="mostrarAlterarImagem" class="escolher-imagem" [ngClass]="chevronBackground">
        <ion-icon name="chevron-down-outline" color="light"></ion-icon>
      </div>
    </div>
    <ion-label *ngIf="mostrarNome">
      <h1>Olá, {{usuario.nomeCompleto | firstName | titlecase}}</h1>
    </ion-label>
    <ion-buttons slot="end" *ngIf="mostrarAtalhos">
      <ion-button fill="clear" (click)="irParaAction('pix')">
        <ion-icon name="pix" slot="icon-only"></ion-icon>
      </ion-button>
      <ion-button fill="clear" (click)="irParaAction('qrcode-elo')">
        <ion-icon name="ler-qrcode" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-item>
</section>
