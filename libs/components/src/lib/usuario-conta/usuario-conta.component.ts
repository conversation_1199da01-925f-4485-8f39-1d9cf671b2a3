import { Component, EventEmitter, Input, Output } from '@angular/core';
import { environment, Usuario } from '@corporativo/shared';

@Component({
  selector: 'mobile-usuario-conta',
  templateUrl: './usuario-conta.component.html',
  styleUrls: ['./usuario-conta.component.scss'],
  standalone: false
})
export class UsuarioContaComponent {
  @Input() usuario!: Usuario;
  @Input() mostrarAlterarImagem = true;
  @Input() mostrarAtalhos = false;
  @Input() mostrarNome = false;
  @Input() inicio = false;
  @Output() irPara = new EventEmitter<string>();
  @Output() clicar = new EventEmitter<string>();

  chevronBackground = environment.homeVersion === 'v2' ? 'chevron-background' : '';

  irParaAction(rota: string) {
    this.irPara.emit(rota);
  }

  buttonAction() {
    this.clicar.emit();
  }

}
