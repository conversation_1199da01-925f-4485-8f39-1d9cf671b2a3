import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { debounceTime } from 'rxjs';

@Component({
  selector: 'mobile-searchbar',
  templateUrl: './searchbar.component.html',
  styleUrls: ['./searchbar.component.scss'],
  standalone: false
})
export class SearchbarComponent implements OnInit, OnChanges {
  searchControl = new FormControl();
  @Input() debounceTime = 500;
  @Input() clear = false;
  @Input() disable = false;
  @Input() placeholder = 'Digite um valor para pesquisar';
  @Output() changeInput = new EventEmitter();
  @Output() enterPressed = new EventEmitter();
  @Input() escuro = false;
  @Input() showSearchIcon = true;

  ngOnInit() {
    this.searchControl.valueChanges.pipe(debounceTime(this.debounceTime)).subscribe(x => {
      this.changeInput.emit(x);
    });
  }

  ngOnChanges() {
    if (this.searchControl.value) {
      this.searchControl.reset();
    }
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.enterPressed.emit(this.searchControl.value);
    }
  }
}
