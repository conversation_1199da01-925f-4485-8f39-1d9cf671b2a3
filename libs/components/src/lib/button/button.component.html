<div>
  <ng-container *ngIf="buttonType === 'rounded'; else squaredButton">
    <mobile-button-rounded
      [color]="color"
      [icon]="icon"
      [nome]="nome"
      [carregando]="carregando">
    </mobile-button-rounded>
  </ng-container>
  <ng-template #squaredButton>
    <mobile-button-squared
      [color]="color"
      [icon]="icon"
      [nome]="nome"
      [carregando]="carregando">
    </mobile-button-squared>
  </ng-template>
</div>
