import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { SkeletonModule } from '../skeleton/skeleton.module';
import { ButtonComponent } from './button.component';
import { ButtonRoundedModule } from '../button-rounded/button-rounded.module';
import { ButtonSquaredModule } from '../button-squared/button-squared.module';

@NgModule({
  declarations: [ButtonComponent],
  exports: [
    ButtonComponent,
  ],
  imports: [
    ButtonRoundedModule,
    ButtonSquaredModule,
    CommonModule,
    IonicModule,
    SkeletonModule,
  ]
})
export class ButtonModule {
}
