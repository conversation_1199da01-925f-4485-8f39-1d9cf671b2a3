import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { environment } from '@corporativo/shared';

@Component({
  selector: 'mobile-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
  standalone: false
})
export class ButtonComponent implements OnInit {

  @Input() color = 'medium';
  @Input() icon = '';
  @Input() nome = '';
  @Input() carregando = false;
  @Output() action = new EventEmitter();

  buttonType = environment.buttonType;

  constructor() {
    //
  }

  ngOnInit() {
    if (!environment.corFuncionalidades) {
      this.color = 'medium';
    }
  }
}
