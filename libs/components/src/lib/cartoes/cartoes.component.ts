import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { AuthService, Credencial, Usuario } from '@corporativo/shared';
import { Observable } from 'rxjs';

@Component({
  selector: 'mobile-cartoes',
  templateUrl: './cartoes.component.html',
  styleUrls: ['./cartoes.component.scss'],
  standalone: false
})
export class CartoesComponent implements OnInit, OnChanges {
  credenciais!: Credencial[];
  credenciaisFiltradas: Credencial[] = [];
  @Input() idCredencial!: number;
  @Input() showNext = true;
  @Input() refresh = false;
  @Output() trocar = new EventEmitter();
  cartoes$!: Observable<Credencial[]>;
  credencial!: Credencial;

  constructor(private authService: AuthService) {
  }

  ngOnInit() {
    const usuario: Usuario = this.authService.getUser();
    this.credenciais = [...usuario.credenciais];
    this.setCredenciais();
  }

  private setCredenciais() {
    let cartoes: Credencial[] = [];
    this.credenciaisFiltradas = [];
    for (const credencial of this.credenciais) {
      cartoes = this.credenciais.filter(x => x.idConta == credencial.idConta);
      if (cartoes.length >= 2) {
        const cartaoVirtual = cartoes.find(x => x.virtual);
        if (cartaoVirtual) {
          cartaoVirtual.removerListaInicial = true;
        }
      }
    }
    this.credenciaisFiltradas = this.credenciais.filter(r => !r.removerListaInicial);
    const credencial = this.credenciaisFiltradas[0];
    if (this.idCredencial) {
      const credencial = this.credenciaisFiltradas.find(x => x.idCredencial == this.idCredencial);
      if (credencial) {
        this.credencial = credencial;
        this.trocar.emit(credencial);
      }
    } else {
      this.credencial = credencial;
      this.trocar.emit(credencial);
    }
  }

  next(cartao: Credencial) {
    const indexof = this.credenciaisFiltradas.indexOf(cartao);
    let index = indexof + 1;
    if (this.credenciaisFiltradas.length == index) {
      index = 0;
    }
    this.credencial = this.credenciaisFiltradas[index];
    this.trocar.emit(this.credenciaisFiltradas[index]);
  }

  prev(cartao: Credencial) {
    this.trocar.emit(cartao);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['refresh'] && changes['refresh'].currentValue) {
      this.atualizarSaldo();
    }
  }

  atualizarSaldo() {
    this.authService.buscarSaldoContas().subscribe(x => {
      const user: Usuario = this.authService.getUser();
      this.credenciais = [...user.credenciais];
      this.setCredenciais();
    });
  }

}
