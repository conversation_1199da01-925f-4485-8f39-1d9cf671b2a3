.cartao {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  ion-icon {
    font-size: 24px;
    margin-right: 1rem;
  }

  h1 {
    margin: 0;
    color: var(--ion-color-label);
    font-size: 14px;
    font-weight: var(--ion-font-weight-label);
    line-height: 1;
  }
}

ion-item {
  width: 100%;
  --background: var(--ion-color-medium);
  --min-height: 40px;
  --padding-start: 0;
  --padding-top: 0;
  --border-radius: 8px;
  --inner-padding-end: 8px;
  --inner-padding-start: 8px;

  ion-label.right {
    margin-right: 0;
  }

  .itens {
    width: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .buttons {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;

      ion-button {
        margin: 0;
        --padding-start: 0;
        --padding-end: 0;
        --padding-bottom: 0;
        --padding-top: 0;
        height: 30px;
      }

      ion-button:last-child {
        height: 22px;
        --padding-start: 1rem;
        --padding-end: 1rem;
        --border-radius: 4px;
      }
    }

    &.left {
      padding: 1rem 1rem 1rem 0;
    }
  }

  ion-icon {
    margin: 0;
  }
}
