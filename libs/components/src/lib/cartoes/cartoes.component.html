<ng-container *ngIf="credencial; else carregando">
  <ion-item lines="none">
    <div class="itens">
      <ion-label class="right">
        <div class="cartao">
          <ion-icon name="cartoes-elo" color="quartenary"></ion-icon>
          <h1>{{ credencial.credencialMascarada }}</h1>
        </div>
      </ion-label>
    </div>
    <ion-icon *ngIf="credenciaisFiltradas.length > 1 && showNext" slot="end" color="primary" name="chevron-forward-outline" size="small"
              (click)="next(credencial)"></ion-icon>
  </ion-item>
</ng-container>
<ng-template #carregando>
  <ion-item lines="none">
    <div class="itens">
      <ion-label class="right">
        <div class="valor">
          <h1>
            <mobile-skeleton [animated]="true" height="20px" width="120px"></mobile-skeleton>
          </h1>
        </div>
      </ion-label>
    </div>
<!--    <ion-icon slot="end" size="small" color="primary" name="chevron-forward-outline"></ion-icon>-->
  </ion-item>
</ng-template>
