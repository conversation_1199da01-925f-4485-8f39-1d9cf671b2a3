import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {SaldoService} from '../../../../shared/src/lib/services/saldo.service';
import {Observable} from 'rxjs';
import {Saldo} from '@corporativo/shared';


@Component({
  selector: 'mobile-saldo',
  templateUrl: './saldo.component.html',
  styleUrls: ['./saldo.component.scss'],
  standalone: false,
})
export class SaldoComponent implements OnInit {
  @Input() menu: boolean = false;
  @Input() idConta!: number;
  @Output() abrirExtrato = new EventEmitter();
  @ViewChild('swiper')
  swiperRef: ElementRef | undefined;
  verSaldo: boolean = true;
  saldo$!: Observable<Saldo>;

  constructor(private saldoService: SaldoService) {}

  next() {
    this.swiperRef?.nativeElement.swiper.slideNext();
  }

  prev() {
    this.swiperRef?.nativeElement.swiper.slidePrev();
  }

  ngOnInit() {
    this.saldo$ = this.saldoService.buscarSaldo(this.idConta);
  }

  abrirExtratoAction() {
    this.abrirExtrato.emit();
  }
}
