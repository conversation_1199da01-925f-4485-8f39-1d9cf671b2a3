.content-menu {
  padding: 1rem;
  margin-bottom: 1rem;

p {
  margin: 0 0 .3rem 0;
  color: var(--ion-color-label);
  font-weight: var(--ion-font-weight-label);
}

h1 {
  margin: 0;
  color: var(--ion-color-label);
  font-size: var(--ion-font-size-h1);
  font-weight: var(--ion-font-weight-h1);
}
}

.valor {

p {
  color: var(--ion-color-label);
  font-weight: var(--ion-font-weight-label);
}

h1 {
  margin: 0;
  color: var(--ion-color-label);
  font-size: var(--ion-font-size-h1);
  font-weight: var(--ion-font-weight-text);
  line-height: 1;

b {
  font-weight: var(--ion-font-weight-h1);
  font-size: var(--ion-font-size-h1);
}
}
}


ion-item {
  width: 100%;
  --background: var(--ion-color-background-saldo);
  --border-radius: var(--ion-border-radius-saldo);
  --padding-start: 0;

ion-label.right {
  margin-right: 0;
}

ion-icon {
  margin: 3px
}

.itens {
  width: 100%;
  padding: 1rem 0 1rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;

.buttons {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  justify-content: center;

ion-button {
  margin: 0;
  --padding-start: 0;
  --padding-end: 0;
  --padding-bottom: 0;
  --padding-top: 0;
  height: 30px;
}

ion-button:last-child {
  height: 22px;
  --padding-start: 1rem;
  --padding-end: 1rem;
  --border-radius: 4px;
}
}

&.left {
   padding: 1rem 1rem 1rem 0;
 }
}
}
