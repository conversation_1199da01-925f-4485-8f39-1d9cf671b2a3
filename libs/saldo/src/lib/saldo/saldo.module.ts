import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { SaldoComponent } from './saldo.component';
import { PipesModule } from '@corporativo/shared';

@NgModule({
  declarations: [SaldoComponent],
  exports: [SaldoComponent],
  imports: [CommonModule, IonicModule, PipesModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SaldoModule {}
