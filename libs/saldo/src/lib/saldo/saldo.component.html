<ng-container *ngIf="saldo$ | async as saldo; else carregando">
  <div class="content" *ngIf="!menu">
<!--    <swiper-container #swiper slidesPerView="3" loop="false">-->
<!--      <swiper-slide>-->
        <ion-item lines="none">
          <div class="itens">
            <ion-label class="right">
              <div class="valor">
                <p>Saldo em reais </p>
                <h1 *ngIf="verSaldo">R$ <b>{{saldo.dinheiro | currencyBr }}</b></h1>
                <h1 *ngIf="!verSaldo">●●●●</h1>
              </div>
            </ion-label>
            <div class="buttons">
              <ion-button fill="clear" (click)="verSaldo = !verSaldo">
                <ion-icon [name]="verSaldo ? 'eye-outline': 'eye-off-outline' " slot="icon-only"></ion-icon>
              </ion-button>
              <ion-button color="secondary" size="small" (click)="abrirExtratoAction()">Extrato</ion-button>
            </div>
          </div>
          <ion-icon slot="end" color="quartenary" name="chevron-forward-outline" size="small"
                    (click)="next()"></ion-icon>
        </ion-item>
<!--      </swiper-slide>-->
<!--      <swiper-slide>-->
<!--        <ion-item lines="none">-->
<!--          <div class="itens right">-->
<!--            <ion-label class="right">-->
<!--              <div class="valor">-->
<!--                <p>Saldo em pontos </p>-->
<!--                <h1 *ngIf="verSaldo">Ps&nbsp;<b>{{saldo.pontos | number }}</b></h1>-->
<!--                <h1 *ngIf="!verSaldo">●●●●</h1>-->
<!--              </div>-->
<!--            </ion-label>-->
<!--            <div class="buttons">-->
<!--              <ion-button fill="clear" (click)="verSaldo = !verSaldo">-->
<!--                <ion-icon [name]="verSaldo ? 'eye-outline': 'eye-off-outline' " slot="icon-only"></ion-icon>-->
<!--              </ion-button>-->
<!--              <ion-button color="secondary" size="small" (click)="abrirExtratoAction()">Extrato</ion-button>-->
<!--            </div>-->
<!--          </div>-->
<!--          <ion-icon slot="end" color="quartenary" name="chevron-forward-outline" size="small"-->
<!--                    (click)="prev()"></ion-icon>-->
<!--        </ion-item>-->
<!--      </swiper-slide>-->
<!--    </swiper-container>-->
  </div>
<!--  <div class="content-menu" *ngIf="menu">-->
<!--    <div class="valor ion-margin-bottom">-->
<!--      <p>Saldo em reais </p>-->
<!--      <h2 class="dinheiro">R$ {{saldo.dinheiro | currencyBr }}</h2>-->
<!--    </div>-->
<!--    <div class="valor">-->
<!--      <p>Saldo em pontos </p>-->
<!--      <h2 class="pontos">Ps {{saldo.pontos | number }}</h2>-->
<!--    </div>-->
<!--  </div>-->
</ng-container>
<ng-template #carregando>
  <ion-item lines="none">
    <div class="itens">
      <ion-label class="right">
        <div class="valor">
          <p>Saldo em reais</p>
          <h1>
            <ion-skeleton-text [animated]="true" style="height: 20px; width: 120px"></ion-skeleton-text>
          </h1>
        </div>
      </ion-label>
      <div class="buttons">
        <ion-button fill="clear">
          <ion-skeleton-text [animated]="true" style="height: 20px; width: 50px"></ion-skeleton-text>
        </ion-button>
      </div>
    </div>
    <ion-icon slot="end" color="quartenary" name="chevron-forward-outline" size="small"></ion-icon>
  </ion-item>
</ng-template>
