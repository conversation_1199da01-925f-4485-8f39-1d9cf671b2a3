import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild
} from '@angular/core';
import { Conta } from '@corporativo/shared';

@Component({
  selector: 'mobile-contas-migradas',
  templateUrl: './contas-migradas.component.html',
  styleUrls: ['./contas-migradas.component.scss'],
  standalone: false
})
export class ContasMigradasComponent implements OnInit, OnChanges {
  @Input() showNext = true;
  @Input() idConta!: number;
  @Input() contas!: Conta[];
  @Output() trocarConta = new EventEmitter();
  @ViewChild('swiper') swiperRef!: ElementRef;

  constructor() {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['contas'] && changes['contas'].firstChange) {
      this.setContaActive(changes['contas'].currentValue[0]);
    }
  }

  next(conta: Conta) {
    const indexof = this.contas.indexOf(conta);
    let index = indexof + 1
    if (this.contas.length == index) {
      index = 0;
    }
    this.swiperRef?.nativeElement.swiper.update();
    this.swiperRef?.nativeElement.swiper.slideNext();
    this.trocarConta.emit(this.contas[index]);
  }

  prev(conta: Conta) {
    this.trocarConta.emit(conta);
    this.swiperRef?.nativeElement.swiper.slidePrev();
  }

  setContaActive(conta?: Conta) {
    if (conta) {
      const indexof = this.contas.indexOf(conta);
      setTimeout(() => {
        this.swiperRef?.nativeElement.swiper.slideTo(indexof);
      }, 200);
      this.trocarConta.emit(conta);
    }
  }

  ngOnInit() {
    setTimeout(() => {
      this.swiperRef.nativeElement.swiper.allowTouchMove = false;
    }, 200);
    const conta = this.contas.find((x: any) => x.idContaDestino == this.idConta);
    this.setContaActive(conta);
  }

}
