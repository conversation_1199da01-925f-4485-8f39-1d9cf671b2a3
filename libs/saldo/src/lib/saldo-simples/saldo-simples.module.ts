import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { SaldoSimplesComponent } from './saldo-simples.component';
import { PipesModule } from '@corporativo/shared';
import { SkeletonModule } from '@corporativo/components';

@NgModule({
  declarations: [SaldoSimplesComponent],
  exports: [SaldoSimplesComponent],
  imports: [
    CommonModule,
    IonicModule,
    PipesModule,
    SkeletonModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SaldoSimplesModule {
}
