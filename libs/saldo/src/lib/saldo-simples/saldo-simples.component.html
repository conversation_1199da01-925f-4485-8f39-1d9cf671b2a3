<ng-container *ngIf="contas && contas.length; else carregando">
  <swiper-container #swiper slidesPerView="1" loop="true">
    <swiper-slide *ngFor="let conta of contas">
      <ion-item lines="none">
        <div class="itens">
          <ion-label class="right">
            <div class="valor">
              <p>{{ conta.descProdutoInstituicao }}</p>
              <ng-container *ngIf="conta.tipoProduto == 'MOEDEIRO' else outros">
                <h1>Ps <b>{{ conta.saldoConta.saldoDisponivel | currencyBr }}</b></h1>
              </ng-container>
              <ng-template #outros>
                <h1>R$ <b>{{ conta.saldoConta.saldoDisponivel | currencyBr }}</b></h1>
              </ng-template>
            </div>
          </ion-label>
        </div>
        <ion-icon *ngIf="showNext && contas.length > 1" class="ion-align-self-end" slot="end" color="primary"
                  name="chevron-forward-outline" size="small" (click)="next(conta)"></ion-icon>
      </ion-item>
    </swiper-slide>
  </swiper-container>
</ng-container>
<ng-template #carregando>
  <ion-item lines="none">
    <div class="itens">
      <ion-label class="right">
        <div class="valor">
          <p>Saldo em reais</p>
          <h1>
            <mobile-skeleton height="20px" width="120px"></mobile-skeleton>
          </h1>
        </div>
      </ion-label>
    </div>
  </ion-item>
</ng-template>
