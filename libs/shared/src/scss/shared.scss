
// Icones button rounded/squared

.button-rounded {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  mobile-button-rounded {
    display: block;
    margin-right: 1rem;
    width: 80px;
  }
}

.carousel {
  overflow: scroll !important;
  -webkit-overflow-scrolling: touch !important; /* para rolagem suave no iOS */
  scrollbar-width: none !important;
}

.button-squared {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;

  mobile-button-squared {
    display: block;
  }
}

.button-rounded::-webkit-scrollbar, .button-squared::-webkit-scrollbar {
  display: none;
}

.buttons-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
  gap: 20px !important;
}

@media (min-width: 340px) {
  .buttons-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}
