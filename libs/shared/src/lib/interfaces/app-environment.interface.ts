import {AppHostConfig} from './app-host-config.interface';

export interface AppEnvironment {
  production: boolean;
  app?: string;
  appId: string;
  appName: string;
  env: string;
  default?: string;
  idInstituicao: number;
  idProcessadora: number;
  idProgramaFidelidade: number;
  idProdutoInstituicao: number;
  isbp: number;
  tokenCaf: string;
  tokenFaceLiveness: string;
  tokenCafBeta: string;
  idGrupoAcesso: any,
  idGrupoAcessoPj: any,
  idAplicativo: number,
  urlMotiva: string;
  onesignalAppId: string;
  hosts?: {
    [name: string]: AppHostConfig;
  };
  buttonType: 'rounded' | 'squared';
  buttonLayout: 'carousel' | 'grid';
  homeVersion: 'v1' | 'v2';
  showChat: boolean;
  showIcon: boolean;
  showTitleDots: boolean;
  tituloAjuda: string;
  tipoLoginPj: string;
  tipoLoginPf: any;
  urlApple: string;
  corFuncionalidades: boolean;
  telefoneContato: string;
}
