import { Credencial } from './credencial.interface';
import { Conta } from './conta.interface';

export interface Usuario {
  dataNascimento: Date;
  dataFundacao: Date;
  dddTelefoneCelular: number;
  dddTelefoneResidencial: number;
  descEstadoCivil: string;
  documento: string;
  documentoRepresentante: string;
  documentoAcesso: string;
  razaoSocial: string;
  nomeFantasia: string;
  dddTelefoneCelularRepresentante: number;
  telefoneCelularRepresentante: number;
  dddTelefoneResidencialRepresentante: number;
  telefoneResidencialRepresentante: number;
  email: string;
  emailProfissional: string;
  enderecoResidencial: EnderecoResidencial;
  bairro: string;
  cep: string;
  // cidade: string;
  complemento: string;
  dtHrConfirmacao: string;
  idEndereco: number;
  logradouro: string;
  numero: string;
  origemConfirmacao: string;
  uf: string;
  estadoCivil: number;
  idSexo: number;
  nacionalidade: string;
  naturalidade: string;
  nomeCompleto: string;
  nomeMae: string;
  nomePai: string;
  orgaoExpedidor: string;
  pais: string;
  rg: string;
  rgDataEmissao: string;
  telefoneCelular: number
  telefoneResidencial: number;
  credenciais: Credencial[];
  credencial: Credencial;
  conta: Conta;
  imagemPerfil?: string | null | undefined;
}

export interface EnderecoResidencial {
  cep: string;
  logradouro: string;
  numero: string;
  complemento: string;
  bairro: string;
  cidade: string;
  uf: string;
  dtHrConfirmacao?: string;
  idEndereco?: number;
}
