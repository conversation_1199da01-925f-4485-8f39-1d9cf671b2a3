export interface Agendamento {
  benChaveEnderecamento: string;
  benCodAgencia: string;
  benContaTipoId: number;
  benEndToEnd: string;
  benInscricaoNacional: string;
  benInstituicao: string;
  benIspb: string;
  benNome: string;
  benNroConta: string;
  campoLivre: string;
  cancelado: boolean;
  dataAgendamento: string;
  dataPagamento: string;
  dataAtual: string;
  idAgendamento: number;
  idConta: number;
  pagCodAgencia: string;
  pagContaTipoId: number;
  pagInscricaoNacional: string;
  pagIspb: string;
  pagNome: string;
  pagNroConta: string;
  pagPessoaTipoId: string;
  referenciaInterna: string;
  valorOperacao: number;
  valorOperacaoPontos: number;
  descricao: string;
  enviado: boolean;
  agendado: boolean;
}

export interface Agenda {
  agendamentos: Agendamento[];
  data: string;
}
