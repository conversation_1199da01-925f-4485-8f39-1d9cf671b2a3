export interface Extrato {
  dataTransacao: Date;
  dataTransacaoFmt: string;
  dataTransacaoFmtMes: string;
  dataTransacaoStr: string;
  descTransacao: string;
  descFunctionCode: string;
  functionCode: string;
  hasComprovante: boolean;
  idTranlog: number;
  idTransacao: string;
  isTransacaoPontos: boolean;
  nsu: string;
  quatroUltimosNumeros: string;
  sinal: number;
  statusTransacao: string;
  transacaoEstornada: boolean;
  transacaoEstorno: boolean;
  valorConversao: number;
  valorPontosEmReais: number;
  valorTransacao: number;
  valordoPonto: number;
  descTransacaoMinima: string;
  descLocal: string;
  ss: string;
  possuiComprovante: boolean;
  notaFiscalSituacao?: number;
  idNotaFiscal?: number;
}

export interface Transacao {
  dataFmt: string;
  data: Date;
  operacoes: Extrato[];
}
