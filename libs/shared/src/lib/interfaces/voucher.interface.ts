export interface Dimensoes {
  altura: string;
  largura: string;
  profundidade: string;
  peso: string;
}

export interface Estoque {
  controleEstoque: string;
}

export interface Fotos {
  p: string;
  m: string;
  g: string;
}

export interface Imagens {
  pp: string;
  p: string;
  m: string;
  g: string;
  gg: string;
}

export interface Voucher {
  codigo: string;
  fabricanteId: number;
  fabricanteNome: string;
  fornecedorId: number;
  fornecedorNome: string;
  departamentoId: number;
  departamentoNome: string;
  categoriaId: number;
  categoriaNome: string;
  preco: string;
  precoDe: string;
  precoBTD: string;
  precoBTDI: string;
  precoBTDIsemFrete: string;
  precoInteiro: number;
  taxa: string;
  impostosTaxa: string;
  desconto: string;
  produtoNome: string;
  descricao: string;
  tipoProduto: string;
  frete: string;
  impostosFrete: string;
  estoque: Estoque;
  imagens: Imagens;
  fotos: Fotos;
  dimensoes: Dimensoes;
  valorTotal: number;
}
