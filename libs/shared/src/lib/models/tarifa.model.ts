export class TarifaModel {

  idPerfilTarifario: number;
  descPerfil: string;
  valorTarifa: number;
  descTransacaoReduzida: string;
  descTransacaoEstendida: string;
  codTransacao: number;

  constructor(data: any) {
    this.idPerfilTarifario = data.idPerfilTarifario;
    this.descPerfil = data.descPerfil;
    this.valorTarifa = data.valorTarifa;
    this.descTransacaoReduzida = data.descTransacaoReduzida;
    this.descTransacaoEstendida = data.descTransacaoEstendida;
    this.codTransacao = data.codTransacao;
  }

  getLabelTarifa() {
    if (this.descTransacaoEstendida && this.descTransacaoEstendida !== '') {
      return this.descTransacaoEstendida;
    } else if (this.descTransacaoReduzida && this.descTransacaoReduzida !== '') {
      return this.descTransacaoReduzida;
    } else {
      return this.descPerfil;
    }
  }

  getValorTarifaReal(): string {
    this.valorTarifa = this.valorTarifa * 0.01
    return 'R$ ' + this.valorTarifa.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

}
