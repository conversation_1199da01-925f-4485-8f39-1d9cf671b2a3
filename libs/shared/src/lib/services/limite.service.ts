import { Injectable } from '@angular/core';
import { AbstractService, Credencial, environment } from '@corporativo/shared';
import { HttpClient } from '@angular/common/http';
import { LimitePagamento } from '../interfaces';

@Injectable({
  providedIn: 'root'
})
export class LimiteService extends AbstractService<LimitePagamento> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'limites', http);
  }

  buscarAtual(idConta: number, idProdutoInstituicao: number, idTipoTransacao: number) {
    const relativeUrl = `consulta-limite-atual/${idConta}/${idProdutoInstituicao}/${idTipoTransacao}`;
    return this.getOne(relativeUrl);
  }

  solicitarAlteracao(solicitacao: {
    idConta: number,
    valorAtual: number,
    valorSolicitado: number,
    idProdutoInstituicao: number,
    idTipoTransacao: number,
    nomeCampoLimite: number
  }) {
    return this.post([solicitacao] as any, 'solicitar-novo-limite');
  }

  verificarLimitePorTransacao(credencial: Credencial, valor: number, tipoTransacao: number) {
    const dados: any = {
      idConta: credencial.idConta,
      idInstituicao: environment.idInstituicao,
      idProdInstituicao: credencial.idProdutoInstituicao,
      valorTransferencia: valor,
      idTipoTransacao: tipoTransacao
    };
    return this.post(dados, 'verificar-limite');
  }
}
