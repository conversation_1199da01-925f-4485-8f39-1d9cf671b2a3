import { Injectable } from '@angular/core';
import { environment, Usuario } from '@corporativo/shared';
import { HttpClient } from '@angular/common/http';
import { AbstractService } from './abstract.service';
import { TextUtil } from '@utils/text.util';

@Injectable({
  providedIn: 'root',
})
export class TokenFuncionalidadeService extends AbstractService<any> {
  constructor(protected override http: HttpClient) {
    super('valloo', 'tokenFuncionalidade', http);
  }

  enviarSms(usuario: Usuario) {
    const dados = {
      idProcessadora: environment.idProcessadora,
      idInstituicao: environment.idInstituicao,
      idFuncionalidade: 1,
      documento: TextUtil.removeNotDigit(usuario.documento),
      documentoAcesso: usuario.documentoRepresentante,
      grupoAcesso: environment.idGrupoAcesso,
      tipoLogin: environment.tipoLoginPf,
      chave: `${usuario.dddTelefoneCelular}` + usuario.telefoneCelular
    };
    return this.post(dados, 'enviar-sms');
  }

  validar(chaveExterna: string, token: string) {
    const obj: any = {
      chaveExterna,
      token,
    };
    return this.post(obj, 'usar');
  }
}
