import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import {
  AbstractService,
  Agenda,
  Agendamento,
  ConsultaQrCodePix,
  Credencial,
  environment,
  TipoChaveEnum,
  TipoPessoaEnum,
  Usuario,
  Valor
} from '@corporativo/shared';
import { TextUtil } from '@utils/text.util';
import {BehaviorSubject, concatMap, forkJoin, of, Subject} from 'rxjs';
import { resolve } from '@utils/resolve.util';
import { dateBrToEn } from '@utils/date.util';
import { format, getMonth, parseISO } from 'date-fns';
import { Strings } from '@utils/strings.util';
import * as lodash from 'lodash';
import { Limite } from '../interfaces';
import { CurrencyPipe } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class PixService extends AbstractService<any> {
  private agendamentos: Agendamento[] = [];
  private agendamentosFiltrados: Agendamento[] = [];
  private agendamentos$ = new Subject<any[]>();

  constructor(protected httpClient: HttpClient) {
    super('valloo', 'portadorPix', httpClient);
  }

  buscarLimite(idConta: number) {
    const url = resolve('pix');
    return this.http.get(`${url}/conta-transacional/limite/${idConta}`).pipe(map((x: any) => {
      return {
        diurnoMaximo: x.limiteDiurnoMaximo,
        statusLimiteDiurnoMaximo: x.statusLimiteDiurnoMaximo,
        diurnoPorTransacao: x.limiteDiurnoPorTransacao,
        statusLimiteDiurnoPorTransacao: x.statusLimiteDiurnoPorTransacao,
        instituicaoDiurnoMaximo: x.limiteInstituicaoDiurnoMaximo,
        instituicaoDiurnoPorTransacao: x.limiteInstituicaoDiurnoPorTransacao,
        instituicaoNoturnoMaximo: x.limiteInstituicaoNoturnoMaximo,
        instituicaoNoturnoPorTransacao: x.limiteInstituicaoNoturnoPorTransacao,
        noturnoMaximo: x.limiteNoturnoMaximo,
        statusLimiteNoturnoMaximo: x.statusLimiteNoturnoMaximo,
        noturnoPorTransacao: x.limiteNoturnoPorTransacao,
        statusLimiteNoturnoPorTransacao: x.statusLimiteNoturnoPorTransacao
      } as Limite;
    }));
  }

  solicitarLimite(dados: any) {
    const url = resolve('pix');
    return this.http.post(`${url}/limite/solicitar-limite`, dados);
  }

  buscarChaves(idConta: number) {
    return this.getOne(`chave/listar-chaves/${idConta}`).pipe(map(x => x ? x.value : []));
  }

  buscarChavesReivindicadas(status: string, idConta: number) {
    return this.getAllBy({ status }, `reivindicacao/listar/${idConta}`);
  }

  buscarTodasChaves(idConta: number) {
    return forkJoin({
      proprias: this.buscarChaves(idConta).pipe(map((data) => {
        if (!data) {
          return data;
        }
        const chaves: any[] = [];
        for (let value of data) {
          chaves.push({
            pendente: value.chave.reivindicadaDoacao,
            tipo: value.chave.tipo.id.toLowerCase(),
            valor: value.chave.valor,
            situacao: value.chave.reivindicadaDoacao ? 'Aguardando confirmação' : '',
            idReivindicacao: null
          });
        }
        return chaves;
      })),
      reivindicadas: this.buscarChavesReivindicadas('Waiting_Resolution', idConta).pipe(map((data) => {
        if (!data.length) {
          return data;
        }
        const chaves: any[] = [];
        for (let value of data) {
          chaves.push({
            pendente: true,
            tipo: value.tipoChave.toLowerCase(),
            valor: value.valorChave,
            situacao: 'Aguardando contraparte',
            idReivindicacao: value.idReivindicacao
          });
        }
        return chaves;
      })),
      confirmadas: this.buscarChavesReivindicadas('Confirmed', idConta).pipe(map((data) => {
        if (!data.length) {
          return data;
        }
        const chaves: any[] = [];
        for (let value of data) {
          chaves.push({
            pendente: true,
            tipo: value.tipoChave.toLowerCase(),
            valor: value.valorChave,
            situacao: 'Aguardando confirmação',
            idReivindicacao: value.idReivindicacao
          });
        }
        return chaves;
      }))
    }).pipe(map((data) => {
      return [...data.proprias, ...data.reivindicadas, ...data.confirmadas];
    }));
  }

  incluirChave(usuario: Usuario, idTipoChave: number, credencial: Credencial, idConta: number, valor: string) {
    const dados: any = this.montarDadosChave(usuario, idTipoChave, valor, credencial, idConta);
    // @ts-ignore
    dados.idProdInstituicao = credencial.contas.find(x => x.idConta == idConta).idProdutoInstituicao;
    return this.post(dados, 'chave/incluir-chave');
  }

  consultarChave(chave: string) {
    return this.getOneBy({ chave }, `chave/consultar-chave`);
  }

  consultarReivindicacao(chave: string, idConta: number) {
    const dados = {
      chave,
      idConta
    };
    return this.post(dados, 'reivindicacao/consultar').pipe(map((x: any) => x.value));
  }

  reivindicarChave(usuario: Usuario, idTipoChave: number, credencial: Credencial, idConta: number, valor: string, isPortabilidade = true) {
    const dados: any = this.montarDadosChave(usuario, idTipoChave, valor, credencial, idConta);
    // dados.tipoReivindicacao = isPortabilidade ? 2 : 1;
    dados.tipoReivindicacao = 1;
    dados.isPortabilidade = isPortabilidade;
    return this.post(dados, 'reivindicacao/solicitar');
  }

  excluirChave(chave: string) {
    const dados = {
      ispbParticipante: environment.isbp,
      motivo: 'SolicitacaoUsuario'
    };
    return this.post(dados, `chave/excluir-chave/${chave}`);
  }

  alterarStatusReivindicacao(idConta: number, status: string, chave: string) {
    return this.consultarReivindicacao(chave, idConta).pipe(
      concatMap((resposta: any) => {
        const dados = {
          ispbParticipante: environment.isbp,
          statusFila: status,
          motivo: 'SolicitacaoUsuario',
          reivindicacao: resposta.reivindicacao,
          idConta
        };
        return this.post(dados, 'reivindicacao/alterar-status');
      })
    );
  }

  private montarDadosChave(usuario: any, idTipoChave: number, valor: string, credencial: Credencial, idConta: Number) {
    console.log(usuario.contas);
    console.log(idConta);

    const conta = {
      ispbParticipante: environment.isbp,
      agencia: environment.idInstituicao,
      tipoId: 1,
      dataAbertura: credencial.dataHoraInclusao,
      numero: idConta
    };
    const pessoa = {
      inscricaoNacional: usuario.documentoRepresentante,
      nome: TextUtil.removeCaracteres(usuario.razaoSocial),
      tipoId: usuario.documentoRepresentante.length > 11 ? TipoPessoaEnum.Juridica : TipoPessoaEnum.Fisica
    };
    const chave = {
      tipoId: idTipoChave,
      valor
    };
    switch (idTipoChave) {
      case TipoChaveEnum.CPF:
      case TipoChaveEnum.CNPJ:
        chave.valor = TextUtil.removeNotDigit(valor);
        break;
      case TipoChaveEnum.Telefone:
        chave.valor = '+55' + TextUtil.removeNotDigit(valor);
        break;
    }
    return {
      conta,
      pessoa,
      chave
    };
  }

  salvarContato(usuario: any, idConta: number, destinatario: any) {
    const contato: any = {};
    contato.nome = destinatario.nome;
    contato.idConta = idConta;
    contato.valorChave = destinatario.chave;
    contato.tipoChave = this.getTipoChave(destinatario.tipo);
    contato.participante = destinatario.instituicao;
    return this.post(contato, 'contatos/save');
  }

  private getTipoChave(descricao: string) {
    const tipos: any = {
      'CPF': TipoChaveEnum.CPF,
      'CNPJ': TipoChaveEnum.CNPJ,
      'TELEFONE': TipoChaveEnum.Telefone,
      'PHONE': TipoChaveEnum.Telefone,
      'EMAIL': TipoChaveEnum.Email,
      'AUTOMATICA': TipoChaveEnum.Automatica,
      'EVP': TipoChaveEnum.Automatica,
      'QRCODE': TipoChaveEnum.QrCode
    };
    return tipos[descricao];
  }

  buscarContatos(idConta: number) {
    return this.getAll(`contatos/find-by-idConta/${idConta}`);
  }

  excluirContato(id: number) {
    return this.delete(`contatos/delete/${id}`);
  }

  buscarParticipantes() {
    return this.getAll('participantes/listar-participantes').pipe(map((x: any) => {
        if (x.value && x.value.participantes) {
          return x.value.participantes.map((p: any) => p.participante);
        }
      })
    );
  }

  enviarPix(usuario: Usuario, idConta: number, destinatario: any, dadosEnvio: any) {
    const pagador: any = {};
    pagador.conta = {
      ispbParticipante: environment.isbp,
      agencia: environment.idInstituicao,
      tipoId: 1,
      numero: idConta
    };
    pagador.pessoa = {
      inscricaoNacional: usuario.documentoRepresentante,
      nome: usuario.razaoSocial,
      nomeFantasia: usuario.nomeFantasia,
      tipoId: usuario.documentoRepresentante.length > 11 ? TipoPessoaEnum.Juridica : TipoPessoaEnum.Fisica
    };

    const dadosOperacao: any = {};
    dadosOperacao.campoLivre = dadosEnvio.mensagem;
    dadosOperacao.referenciaInterna = dadosEnvio.referencia;
    dadosOperacao.dataPagamento = null;
    dadosOperacao.valorOperacao = dadosEnvio.valor;

    const beneficiario: any = {};
    if (destinatario.chave) {
      beneficiario.endToEnd = destinatario.endToEnd;
      beneficiario.chave = destinatario.chave;
      beneficiario.conta = {
        ispbParticipante: destinatario.ispbParticipante,
        agencia: destinatario.agencia,
        tipoId: this.getTipoChave(destinatario.tipo),
        numero: destinatario.conta
      };
      beneficiario.pessoa = {
        inscricaoNacional: destinatario.documento,
        nome: destinatario.nome,
        nomeFantasia: null,
        tipoId: destinatario.documento.length > 11 ? TipoPessoaEnum.Juridica : TipoPessoaEnum.Fisica
      };
      dadosOperacao.nsu = 'x365784';
    } else {
      beneficiario.endToEnd = '';
      beneficiario.chave = '';
      beneficiario.conta = {
        ispbParticipante: destinatario.ispbParticipante,
        agencia: destinatario.agencia,
        tipoId: destinatario.tipo,
        numero: destinatario.conta
      };
      beneficiario.pessoa = {
        inscricaoNacional: TextUtil.removeNotDigit(destinatario.documento),
        nome: destinatario.nome,
        nomeFantasia: null,
        tipoId: destinatario.documento.length > 11 ? TipoPessoaEnum.Juridica : TipoPessoaEnum.Fisica
      };
      dadosOperacao.nsu = 'x698763';
    }
    const dados: any = { pagador, beneficiario, dadosOperacao, idIdempotente: null };
    if (dadosEnvio.agendamento) {
      delete dadosOperacao.nsu;
      dados.dataAgendamento = dateBrToEn(dadosEnvio.data);
      dados.cancelado = false;
      dados.idConta = idConta;
      dados.benInstituicao = destinatario.instituicao;
      dados.estornado = false;
      return this.post(dados, 'agendamento/incluir');
    }
    return this.http.post(`${resolve('pix')}/pagamento/incluir`, dados);
  }

  buscarComprovante(conta: { agencia: number, idConta: number }, endToEnd: string | null) {
    if (!endToEnd) {
      return new BehaviorSubject({}).asObservable();
    }
    const dados = {
      agencia: conta.agencia,
      conta: conta.idConta,
      dataInicio: format(new Date(), 'yyyy-MM-dd'),
      dataFim: format(new Date(), 'yyyy-MM-dd'),
      endToEnd
    };
    return this.http.post(`${resolve('pix')}/extratos`, dados).pipe(
      map((x: any) => {
        if (x.isSuccess) {
          return { status: 200, pix: x.value.movimentos[0] };
        } else {
          return { staus: 0 };
        }
      })
    );
  }

  devolver(usuario: any, devolucao: any, operacao: any, idConta: number) {
    const dados: any = {};
    dados.endToEnd = operacao.endToEnd;
    dados.inscricaoNacional = usuario.documento;
    dados.motivosDevolucao = [
      { codigo: 'MD06', informacaoAdicional: devolucao.motivo }
    ];
    dados.valor = devolucao.valor;
    return this.http.post(`${resolve('pix')}/pagamento/devolucao/${idConta}`, dados);
  }

  consultarQrCode(qrcode: string) {
    const qrcodeTratado = qrcode.replace(' ', '%20');
    return this.getOne(`qrcode/consultar-qrcode/${qrcodeTratado}`).pipe(
      map(r => this.tratarRetornoConsultaQrCode(r))
    );
  }

  tratarRetornoConsultaQrCode(retorno: ConsultaQrCodePix) {
    if (!retorno.isSuccess) {
      throw retorno.erroMessage || 'Ocorreu um erro inesperado.';
    }
    const chave: any = retorno.value.beneficiario;
    const destino = {
      nome: chave.pessoa.nome,
      agencia: chave.conta.agencia,
      conta: chave.conta.numero,
      documento: chave.pessoa.inscricaoNacional,
      instituicao: chave.conta.nomePsp,
      ispbParticipante: chave.conta.ispbParticipante,
      chave: chave.chave,
      endToEnd: chave.endToEnd,
      tipo: chave.tipoChave
    };
    const currencyPipe = new CurrencyPipe('pt-BR');
    let valor = 0;
    if (retorno.value.tipoQRCode == 'DINAMICO') {
      valor = (retorno.value.valor as Valor).final;
    } else {
      valor = retorno.value.valor as number;
    }
    const dados = {
      mensagem: retorno.value.descricao,
      valor: valor,
      valorFormatado: currencyPipe.transform(valor, 'BRL'),
      data: format(new Date(), 'dd/MM/yyyy'),
      agendamento: false,
      tipoQRCode: retorno.value.tipoQRCode,
      referencia: retorno.value.referencia
    };
    const valorChave = chave.chave;
    chave.chave = {
      tipo: {
        descricao: chave.tipoChave
      },
      valor: valorChave
    };
    chave.conta.nomeParticipante = chave.conta.nomePsp;
    return { dados, destino, chave };
  }

  gerarQrCode(usuario: Usuario, dados: any) {
    const payload: any = {
      formatoqrcode: '1',
      beneficiario: {
        nome: Strings.removerAcentos(dados.chave.pessoa.nome),
        cidade: Strings.removerAcentos(usuario?.enderecoResidencial?.cidade?.toLowerCase()),
        cep: '',
        chave: dados.chave.chave.valor,
        validachave: true,
        ispbparticipante: dados.chave.conta.ispbParticipante
      },
      transacao: {
        identificador: '',
        descricao: dados.descricao
      }
    };
    if (dados.valor) {
      payload.transacao.valor = dados.valor;
    }
    return this.post(payload, `qrcode/gera-qrcode-estatico/${dados.valor ? 2 : 1}`);
  }

  buscarTotalAgendado(idConta: number) {
    return this.getOne(`agendamento/count/${idConta}`).pipe(
      map(x => {
        if (x == 0) {
          return { pontos: 0, reais: x };
        }
        return { pontos: x * 100, reais: x };
      })
    );
  }

  buscarAgendamentos(idConta: number, mes: number) {
    return this.getAll(`agendamento/consultar/${idConta}`).pipe(
      map(retorno => {
          this.agendamentos = retorno;
          this.agendamentosFiltrados = retorno;
          const agendamentos = this.transformarParaAgenda(retorno);
          return agendamentos.filter(x => getMonth(parseISO(x.data)) == mes);
        }
      )
    );
  }

  private transformarParaAgenda(agendamentos: Agendamento[]): Agenda[] {
    const transacoes: any[] = [];
    for (const agendamento of agendamentos) {
      let descricao = 'Pix agendado';
      agendamento.agendado = true;
      if (agendamento.cancelado) {
        descricao = 'Pix cancelado';
        agendamento.agendado = false;
        agendamento.enviado = false;
      }

      if (agendamento.dataPagamento && !agendamento.cancelado) {
        descricao = 'Pix enviado';
        agendamento.enviado = true;
        agendamento.agendado = false;
      }
      agendamento.descricao = descricao;
      agendamento.valorOperacaoPontos = Math.ceil(agendamento.valorOperacao * 100);
      if (!transacoes.filter(x => format(parseISO(x.data), 'yyyy-MM-dd') ==
        format(parseISO(agendamento.dataAgendamento), 'yyyy-MM-dd')).length) {
        transacoes.push({
          data: agendamento.dataAgendamento
        });
      }
    }
    for (const transacao of transacoes) {
      transacao.agendamentos = agendamentos.filter(x => x.dataAgendamento == transacao.data);
    }
    return lodash.orderBy(transacoes, ['data'], 'desc');
  }

  getAgendamentosFiltrados() {
    return this.agendamentos$.asObservable();
  }

  filtrarAgendamentos(valor: string) {
    if (!valor) {
      this.agendamentosFiltrados = [...this.agendamentos];
      this.agendamentos$.next(this.transformarParaAgenda(this.agendamentos));
      return;
    }
    const agendamentos = this.agendamentosFiltrados.filter(agendamento =>
      agendamento.benInstituicao.toLowerCase().indexOf(valor.toString().toLowerCase()) !== -1 ||
      agendamento?.benNome?.toString()?.toLowerCase()?.indexOf(valor.toString().toLowerCase()) !== -1);
    this.agendamentos$.next(this.transformarParaAgenda(agendamentos));
  }

  buscarAgendamentosPorMes(mes: number) {
    this.agendamentos$.next(
      this.transformarParaAgenda(
        this.agendamentosFiltrados.filter(a => getMonth(parseISO(a.dataAgendamento)) == mes)
      )
    );
  }

  cancelarAgendamento(agendamento: Agendamento) {
    agendamento.cancelado = true;
    return this.put(agendamento, agendamento.idAgendamento, 'agendamento/update');
  }

}
