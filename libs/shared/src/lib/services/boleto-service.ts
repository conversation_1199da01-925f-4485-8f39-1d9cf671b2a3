import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { resolve } from '@utils/resolve.util';
import { AbstractService, environment } from '@corporativo/shared';

@Injectable({
  providedIn: 'root'
})
export class BoletoService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient
  ) {
    super('valloo', 'boleto', http);
  }
  gerarLinhaDigitavel(request: any): Observable<any> {
    const url = resolve('valloo://gerarLinhaDigitavel');
    return this.http.post<any>(url, request);
  }

  enviarBoletoEmail(idCobrancaBancaria: number, emailDestino: string): Observable<any> {
    const url = resolve('valloo://enviarBoletoEmail');
    return this.http.post<any>(url, { idCobrancaBancaria, emailDestino });
  }

  gerarLinhaDigitavelFatura(idConta: number, anoMes: string): Observable<any> {
    const url = resolve('valloo://gerarLinhaDigitavelFatura', { idConta, anoMes });
    return this.http.get<any>(url);
  }

  baixarFaturaPDF(idCredencial: number, idConta: number, anoMes: string): Observable<any> {
    const boletoRequest = {
      idCredencial,
      idInstituicao: environment.idInstituicao,
      idProcessadora: environment.idProcessadora
    };
    const url = resolve('valloo://baixarFaturaPDF', { idConta, anoMes });
    return this.http.post<any>(url, boletoRequest);
  }
}
