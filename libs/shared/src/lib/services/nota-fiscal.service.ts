import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AbstractService } from './abstract.service';
import { Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import * as lodash from 'lodash';
import { format } from 'date-fns';
import { CurrencyBrPipe } from '../pipes/currency-br.pipe';
import {TextUtil} from '@utils/text.util';
import {NotaFiscal} from '../interfaces/nota-fiscal.interface';

@Injectable({
  providedIn: 'root'
})
export class NotaFiscalService extends AbstractService<NotaFiscal> {
  private notasFiscais: any[] = [];
  private notasFiscaisFiltradas: any[] = [];
  private notasFiscais$ = new Subject<any[]>();

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'notaFiscal', http);
  }

  buscarPorId(id: number) {
    return this.getById(id);
  }

  anexar(operacao: any, arquivo: Blob, filename: string) {
    const formData = new FormData();
    formData.append('notaFiscalDTO', new Blob([JSON.stringify(operacao)], {
      type: 'application/json'
    }));
    formData.append('arquivo', arquivo, filename);
    console.log('==> formData', formData);
    return this.postFormData(formData);
  }

  buscarPor(idConta: number, idSituacao: number, dataInicio: string, dataFim: string) {
    return this.getAll(`${idConta}/${idSituacao}/${dataInicio}/${dataFim}`).pipe(
      map(notasFiscais => {
          this.notasFiscais = notasFiscais;
          this.notasFiscaisFiltradas = [...notasFiscais];
          return this.agrupePorData(notasFiscais);
        }
      )
    );
  }

  buscarPorPeriodo(idConta: number, dataInicio: string, dataFim: string) {
    return this.getAll(`${idConta}/${dataInicio}/${dataFim}`);
  }

  private agrupePorData(notasFiscais: NotaFiscal[]) {
    const notasPorDia: any[] = [];
    for (const notaFiscal of notasFiscais) {
      if (!notasPorDia.filter(x => x.dataFmt == format(notaFiscal.dataHoraInclusao, 'yyyy-MM-dd').toString()).length) {
        notasPorDia.push({
          data: notaFiscal.dataHoraInclusao,
          dataFmt: format(notaFiscal.dataHoraInclusao, 'yyyy-MM-dd').toString()
        });
      }
    }
    for (const nota of notasPorDia) {
      const notas = notasFiscais.filter(x => format(x.dataHoraInclusao, 'yyyy-MM-dd').toString() == nota.dataFmt);
      nota.notasFiscais = lodash.orderBy(notas, ['dataInclusao'], 'desc');
    }
    return lodash.orderBy(notasPorDia, ['data'], 'desc');
  }

  getNotasFiscaisFiltradas() {
    return this.notasFiscais$.asObservable();
  }

  filtrarNotasFiscais(valor: string) {
    if (!valor) {
      this.notasFiscaisFiltradas = [...this.notasFiscais];
      this.notasFiscais$.next(this.agrupePorData(this.notasFiscais));
      return;
    }
    const currencyPipe = new CurrencyBrPipe();
    const notas = this.notasFiscaisFiltradas.filter(notaFiscal =>
      TextUtil.compareTextIndexOf(currencyPipe.transform(notaFiscal.valor), valor));
    this.notasFiscais$.next(this.agrupePorData(notas));
  }
}
