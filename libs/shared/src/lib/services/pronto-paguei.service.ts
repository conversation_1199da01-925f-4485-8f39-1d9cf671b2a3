import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { AbstractService } from '@corporativo/shared';
import { TextUtil } from '@utils/text.util';

@Injectable({
  providedIn: 'root',
})
export class ProntoPagueiService extends AbstractService<any> {
  constructor(protected override http: HttpClient) {
    super('valloo', 'prontoPaguei', http);
  }

  consultarConvenio(idConta: number) {
    const url = resolve('valloo://prontoPaguei');
    return this.http.get(`${url}/listar-convenios/${idConta}`);
  }

  consultarDebitos(dados: any) {
    const request = {
      convenio_id: dados.convenio,
      placa: dados.placa,
      renavam: dados.renavan,
      email: dados.email == null ? dados.emailPessoa : dados.email,
      telefone:
        dados.celular == null
          ? TextUtil.removeNotDigit(dados.telefone)
          : TextUtil.removeNotDigit(dados.celular),
      sub_convenio_id: dados.tributo,
      numero_identificador: dados.numero,
      nome: dados.nome,
      documento: dados.cpf == null ? null : TextUtil.removeNotDigit(dados.cpf),
      grecaptcha: 'InGRUPO@Pp2509',
      aceitouTermos: dados.termoUso,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(`${url}/consultar-debitos/${dados.idConta}`, request);
  }

  consultarParcelas(dados: any, boleto: any) {
    const request = {
      uuid: boleto.uuid,
      debitos: boleto.debitos,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(
      `${url}/consultar-parcelas/${dados.idConta}`,
      request
    );
  }

  pagarDebitos(
    dados: any,
    cartao: any,
    endereco: any,
    parcelaSelecionada: any,
    termoUso: boolean,
    boleto: any
  ) {
    const request = {
      isCienteTermoAceite: true,
      aceitouTermos: termoUso,
      cliente_parceiro_id: dados.idConta,
      pagamento: {
        nome_titular: cartao.nome,
        documento_titular: cartao.cpf,
        tipo_documento_titular: 1,
        numero_cartao: TextUtil.removeNotDigit(cartao.numero),
        validade_cartao: cartao.data,
        cvv_cartao: cartao.cvv,
        cep_fatura: endereco.cep,
        endereco: {
          additionalInformation: '',
          city: endereco.bairroInicio.localidade.nome,
          county: endereco.bairroInicio.localidade.uf.sigla,
          number: endereco.enderecoNumero,
          state: endereco.bairroInicio.localidade.uf.sigla,
          street: endereco.nome,
        },
      },
      parcelar_em: {
        quantidade: parcelaSelecionada.quantidade,
        juros: parcelaSelecionada.juros,
        valor_parcela: parcelaSelecionada.valor_parcela,
        total: parcelaSelecionada.total.replace(',', '.'),
        taxa_administrativa: null,
      },
      uuid: boleto.uuid,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(`${url}/pagar-debitos/${dados.idConta}`, request);
  }

  validarBoleto(dados: any) {
    const request = {
      erro_add: true,
      grecaptcha: 'InGRUPO@Pp2509',
      linha: TextUtil.removeNotDigit(dados.codigo),
      aceitouTermos: dados.aceiteTermos,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(`${url}/validar-boleto/${dados.idConta}`, request);
  }

  consultarBoleto(dados: any, usuario: any, boleto: any) {
    const request = {
      convenio_id: 9,
      email: usuario.email,
      telefone:
        usuario.dddTelefoneCelular.toString() +
        usuario.telefoneCelular.toString(),
      grecaptcha: 'InGRUPO@Pp2509',
      linhas: boleto,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(`${url}/consultar-boleto/${dados.idConta}`, request);
  }

  recuperarTransacoesCliente(dados: any) {
    const request = {
      cliente_parceiro_id: dados.idConta,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(
      `${url}/recuperar-transacoes-cliente/${dados.idConta}`,
      request
    );
  }

  consultarProtocolo(dados: any) {
    const request = {
      protocolo: dados.protocolo,
    };
    const url = resolve('valloo://prontoPaguei');
    return this.http.post(
      `${url}/consultar-protocolo/${dados.idConta}`,
      request
    );
  }
}
