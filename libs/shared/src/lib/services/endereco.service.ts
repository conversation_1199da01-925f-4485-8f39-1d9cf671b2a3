import { AbstractService } from './abstract.service';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '@corporativo/shared';
import { resolve } from '@utils/resolve.util';
import { Observable } from 'rxjs';
import { Endereco } from '../request/endereco-request';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class EnderecoService extends AbstractService<any> {

  constructor(protected override http: HttpClient) {
    super('valloo', 'endereco', http);
  }

  buscarEnderecoPorStatus(documento: string) {
    const tipoPessoa = documento.length > 11 ? 2 : 1;
    const idProcessadora = environment.idProcessadora;
    const idInstituicao = environment.idInstituicao;
    const status = 1;
    const url = resolve('endereco');
    return this.http.get<any[]>(`${url}/${documento}/pessoa/${tipoPessoa}/processadora/${idProcessadora}/instituicao/${idInstituicao}/status/${status}/`);
  }

  buscarCep(cep: any): Observable<any> {
    const url = resolve('valloo://cep', {
      cep: cep
    });
    return this.http.get(url).pipe(
      map((data: any) => new Endereco(data && data instanceof Array ? data[0] : null)
      ));
  }
}
