import {
  AbstractService,
  environment,
  StorageService
} from '@corporativo/shared';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { Platform } from '@ionic/angular';
import { Device } from '@capacitor/device';
import { resolve } from '@utils/resolve.util';
import { Observable } from 'rxjs';
import { CadastroLoginRequest } from '../request/cadastro-login-request';
import { getBackBase64CAF, getFrontBase64CAF, getSelfieBase64CAF, jwtMock } from '@utils/mock-caf.util';
import { Geolocation } from '@capacitor/geolocation';

@Injectable({
  providedIn: 'root'
})
export class OnboardingService extends AbstractService<any> {

  deviceInfo: any;
  idDevice: any;
  appInfo: any;

  constructor(
    protected httpClient: HttpClient,
    private router: Router,
    private platform: Platform,
    private storageService: StorageService
  ) {
    super('valloo', 'corporativo', httpClient);
    this.deviceDispostivo();
  }

  async deviceDispostivo() {
    this.deviceInfo = await Device.getInfo();
    this.idDevice = await Device.getId();
  }

  validarCadastro(documento: string, telefone: string, documentoRepresentante: string) {
    const idProcessadora = environment.idProcessadora;
    const idInstituicao = environment.idInstituicao;
    let architectureInfo = 'DESENVOLVIMENTO';
    let deviceId = '9CED84B6-862B-4249-80CE-F43D2279F0A2';
    let model = 'DESENVOLVIMENTO';
    let platformName = 'DESENVOLVIMENTO';
    let plataformVersion = 'DESENVOLVIMENTO';
    let sistemaOperacional = 1;
    let pushNotificationDeviceId = '';

    if (this.platform.is('hybrid')) {
      architectureInfo = this.deviceInfo.manufacturer;
      deviceId = this.idDevice.identifier;
      model = this.deviceInfo.model;
      sistemaOperacional = this.platform.is('ios') ? 1 : 2;
      platformName = this.deviceInfo.name;
      plataformVersion = this.deviceInfo.osVersion;
    }

    const request = {
      architectureInfo,
      deviceId,
      model,
      platformName,
      sistemaOperacional,
      plataformVersion,
      pushNotificationDeviceId,
      documento,
      telefone,
      idInstituicao,
      grupoAcesso: documentoRepresentante ? environment.idGrupoAcessoPj : environment.idGrupoAcesso,
      idProcessadora,
      documentoRepresentante,
      tipoLogin: documentoRepresentante ? environment.tipoLoginPj : environment.tipoLoginPf
    };
    return this.http.post(resolve('valloo://validarCadastro'), request);
  }

  enviarDadosMock(dados: any): Observable<any> {
    const files = dados.type === 'cnh' ?
      [
        {data: getFrontBase64CAF(), type: 'CNH_FRONT'},
        {data: getBackBase64CAF(), type: 'CNH_BACK'}
      ] :
      [
        {data: dados.imageFrontURL, type: 'RG_FRONT'},
        {data: dados.imageBackURL, type: 'RG_BACK'}
      ];
    files.push({data: getSelfieBase64CAF(), type: 'SELFIE'});
    const data = {
      files,
      attributes: {
        cpf: dados.cpf
      },
    };

    const request = {
      requestCaf: data,
      idInstituicao: environment.idInstituicao,
      documento: dados.cpf,
      documentoRepresentante: dados.documentoRepresentante,
      jwt: jwtMock(),
      arquivo: dados.arquivo
    };

    return this.validarCaf(request);
  }

  enviarDados(dados: any, jwt: string): Observable<any> {
    const files = dados.type === 'cnh' ?
      [
        {data: dados.imageFrontURL, type: 'CNH_FRONT'},
        {data: dados.imageBackURL, type: 'CNH_BACK'}
      ] :
      [
        {data: dados.imageFrontURL, type: 'RG_FRONT'},
        {data: dados.imageBackURL, type: 'RG_BACK'}
      ];
    files.push({data: dados.selfie, type: 'SELFIE'});
    const data = {
      files,
      attributes: {
        cpf: dados.documentoRepresentante ? dados.documentoRepresentante : dados.documento
      },
    };

    const request = {
      requestCaf: data,
      idInstituicao: environment.idInstituicao,
      documento: dados.documento,
      documentoRepresentante: dados.documentoRepresentante,
      jwt: jwt
    };
    return this.validarCaf(request);
  }

  validarCaf(request: any) {
    const url = resolve('valloo://validarCaf')
    return this.http.post(url, request);
  }

  criarPortadorLogin(cadastro: CadastroLoginRequest) {
    let architectureInfo = 'DESENVOLVIMENTO';
    let deviceId = 'DESENVOLVIMENTO';
    let model = 'DESENVOLVIMENTO';
    let platformName = 'DESENVOLVIMENTO';
    let plataformVersion = 'DESENVOLVIMENTO';

    if (this.platform.is('hybrid')) {
      architectureInfo = this.deviceInfo.manufacturer;
      deviceId = this.idDevice.identifier;
      model = this.deviceInfo.model;
      platformName = this.deviceInfo.platform;
      plataformVersion = this.deviceInfo.osVersion;
    }
    cadastro.architectureInfo = architectureInfo;
    cadastro.deviceId = deviceId;
    cadastro.model = model;
    cadastro.plataformVersion = plataformVersion;
    cadastro.platformName = platformName;

    const url = resolve('valloo://portadorLogin');
    return this.http.post(url, cadastro)
  }

  registraValidacaoFacial(cpf: string, idObjetivo: any, jwt: string) {
    const idInstituicao = environment.idInstituicao;
    const request = {
      jwt: jwt
    };
    const antifraude = resolve('valloo://antifraude');
    const url = `${antifraude}/caf/registrar-validacao-facial/${idInstituicao}/${cpf}/${idObjetivo}`
    return this.http.post(url, request);
  }

  getStatusCaf(documento: string, request: any, documentoRepresentante: string) {
    const idInstituicao = environment.idInstituicao;
    const antifraude = resolve('valloo://antifraude');
    const url = documentoRepresentante ? `${antifraude}/get-status/caf/${documento}/${idInstituicao}/${documentoRepresentante}` :
      `${antifraude}/get-status/caf/${documento}/${idInstituicao}`
    return this.http.post(url, request);
  }

  encontrarCafNecessario(documento: string) {
    const idInstituicao = environment.idInstituicao;
    const portadorLogin = resolve('portadorLogin');
    const url = `${portadorLogin}/encontra-caf-necessario/instituicao/${idInstituicao}/documento/${documento}`
    return this.http.get(url);
  }

  alterarDispositivo(documento: string, tokenCaf: string) {
    let architectureInfo = 'DESENVOLVIMENTO';
    let deviceId = '9CED84B6-862B-4249-80CE-F43D2279F0A2';
    let model = 'DESENVOLVIMENTO';
    let platformName = 'DESENVOLVIMENTO';
    let plataformVersion = 'DESENVOLVIMENTO';
    let sistemaOperacional = 1;

    if (this.platform.is('hybrid')) {
      architectureInfo = this.deviceInfo.manufacturer;
      deviceId = this.idDevice.identifier;
      model = this.deviceInfo.model;
      platformName = this.deviceInfo.platform;
      plataformVersion = this.deviceInfo.osVersion;
      sistemaOperacional = this.platform.is('ios') ? 1 : 2;
    }

    const request = {
      dispositivo: {
        architectureInfo,
        deviceId,
        model,
        platformName,
        sistemaOperacional,
        plataformVersion
      },
      tokenCaf,
      documento
    };
    const url = resolve('portadorLogin');
    return this.http.post(`${url}/alterar-dispositivo-validacao-facial`, request);
  }

  encontraCafNecessarioPorConta(idConta: number) {
    const url = resolve('portadorLogin');
    return this.http.get(`${url}/encontra-caf-necessario/conta/${idConta}`);
  }

  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const possui = this.storageService.verificarPossuiContaInicioApp();
    if (!possui) {
      this.router.navigate(['inicio-app']);
    }
    return true;
  }

  cadastrarPortadorLogin(request: any) {
    let architectureInfo = 'DESENVOLVIMENTO';
    let deviceId = 'DESENVOLVIMENTO';
    let model = 'DESENVOLVIMENTO';
    let platformName = 'DESENVOLVIMENTO';
    let plataformVersion = 'DESENVOLVIMENTO';
    let versaoAplicativo = 'DESENVOLVIMENTO';
    let latitude = 0;
    let longitude = 0;

    if (this.platform.is('hybrid')) {
      architectureInfo = this.deviceInfo.manufacturer;
      deviceId = this.idDevice.identifier;
      model = this.deviceInfo.model;
      platformName = this.deviceInfo.platform;
      plataformVersion = this.deviceInfo.osVersion;
      versaoAplicativo = this.appInfo.version;
      const geo: any = this.getGeolocation();
      latitude = geo.latitude;
      longitude = geo.longitude;
    }

    request.deviceId = deviceId;
    request.architectureInfo = architectureInfo;
    request.model = model;
    request.platformName = platformName;
    request.plataformVersion = plataformVersion;
    request.versaoAplicativo = versaoAplicativo;
    request.latitude = latitude;
    request.longitude = longitude;

    return this.post(request, 'cadastrar-portador-login');
  }

  async getGeolocation() {
    try {
      const coordinates = await Geolocation.getCurrentPosition();
      return { latitude: coordinates.coords.latitude, longitude: coordinates.coords.longitude };
    } catch (e) {
      return { latitude: -15.784330, longitude: -47.886546 };
    }
  }

  alterarSenhaPortador(request: any) {
    return this.put(request, 'redefinir-senha-login');
  }

  buscarStatusCaf(cpf: string, request: any) {
    const idInstituicao = environment.idInstituicao;
    const documentoRepresentante = null;
    const antifraude = resolve('valloo://antifraude');
    const url = `${antifraude}/get-status/caf/${cpf}/${idInstituicao}/${documentoRepresentante}/${environment.idAplicativo}`;
    return this.http.post(url, request);
  }
}
