import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { Credencial } from '../interfaces';
import { StorageService } from './storage.service';
import { AppIdEnum } from '../enums/app-id.enum';
import { environment, FuncionalidadeCartaoEnum } from '@corporativo/shared';

@Injectable({
  providedIn: 'root'
})
export class ProdutoService {
  private produtos: any[] = [
    {
      nome: 'Extrato',
      icone: 'extrato',
      url: 'extrato',
      codigo: 'extrato',
      ativo: false,
      color: 'secondary'
    },
    {
      nome: 'Transferir<br/>saldo',
      icone: 'transferir-saldo',
      url: 'transferir-saldo',
      codigo: 'transferir_saldo',
      ativo: false,
      color: 'primary'
    },
    {
      nome: 'Pix',
      icone: 'pix',
      url: 'pix',
      codigo: 'pix',
      ativo: false,
      color: 'secondary'
    },
    {
      nome: 'Campanhas',
      icone: 'trofeu',
      url: 'campanhas',
      codigo: 'campanhas',
      ativo: false,
      color: 'primary'
    },
    {
      nome: 'Pagar contas',
      icone: environment.corFuncionalidades ? 'pagar-contas' : 'barcode',
      url: 'pagar-contas',
      codigo: 'pagar_contas',
      ativo: false,
      color: 'primary'
    },
    {
      nome: 'Recarga celular',
      icone: 'recarga-celular',
      url: 'recarga',
      codigo: 'recarga_celular',
      ativo: false,
      color: 'primary'
    },
    {
      nome: 'Vale presente',
      icone: 'vale-presente',
      url: 'vale-presente',
      codigo: 'voucher',
      ativo: false,
      color: 'secondary'
    },
    {
      nome: 'Ted',
      icone: 'transferir-cc',
      url: 'ted',
      codigo: 'ted',
      ativo: false,
      color: 'primary'
    },
    {
      nome: 'Transferir pontos',
      icone: 'transferir-pontos',
      url: 'transferir-pontos',
      codigo: 'transferir_pontos',
      ativo: false,
      color: 'secondary'
    },
    {
      nome: 'Depositar',
      icone: 'depositar',
      url: 'inserir-carga',
      codigo: 'depositar',
      ativo: false,
      color: 'secondary'
    },
    {
      nome: 'Dados de uso',
      icone: 'bar-chart-outline',
      url: 'dados-consumo',
      codigo: 'dados_consumo',
      ativo: false,
      color: 'secondary',
    },
    {
      nome: 'Dados de uso',
      icone: 'bar-chart-outline',
      url: 'dados-consumo',
      codigo: 'dados_consumo_pagina_inicial',
      ativo: false,
      color: 'secondary',
    },
    {
      nome: 'Notas fiscais',
      icone: 'reader',
      url: 'nota-fiscal',
      codigo: 'nota_fiscal',
      ativo: false,
      color: 'secondary',
    }
  ];
  private functionalidadesPix: any[] = [
    {
      nome: 'Enviar',
      icone: '',
      url: 'pix_enviar',
      codigo: 'pix_enviar',
      ativo: false
    },
    {
      nome: 'Meus agendamentos',
      icone: '',
      url: 'pix_meus_agendamentos',
      codigo: 'pix_meus_agendamentos',
      ativo: false
    },
    {
      nome: 'Ler QRCode',
      icone: '',
      url: 'pix_ler_qrcode',
      codigo: 'pix_ler_qrcode',
      ativo: false
    },
    {
      nome: 'Copia e Cola',
      icone: '',
      url: 'pix_copia_cola',
      codigo: 'pix_copia_cola',
      ativo: false
    },
    {
      nome: 'Gerar QRCode',
      icone: '',
      url: 'pix_gerar_qrcode',
      codigo: 'pix_gerar_qrcode',
      ativo: false
    },
    {
      nome: 'Minhas chaves',
      icone: '',
      url: 'pix_minhas_chaves',
      codigo: 'pix_minhas_chaves',
      ativo: false
    },
    {
      nome: 'Meus Limites',
      icone: '',
      url: 'pix_meus_limites',
      codigo: 'pix_meus_limites',
      ativo: false
    },
    {
      nome: 'Pix Automático',
      icone: '',
      url: 'pix_automatico',
      codigo: 'pix_automatico',
      ativo: false
    }
  ];
  private funcionalidadesCartao: any[] = [
    {
      nome: 'Cartões',
      icone: environment.corFuncionalidades ? 'cartao-recorrente' : 'cartoes',
      url: 'cartoes',
      codigo: 'cartoes',
      ativo: true,
      color: 'secondary',
      indice: FuncionalidadeCartaoEnum.Cartao
    }
  ];
  private maisServicos: any[] = [
    {
      nome: 'Saque FGTS',
      icone: 'fgts',
      url: 'saque-fgts',
      codigo: 'saque_fgts',
      ativo: true
    },
    {
      nome: 'Parcelar débitos',
      icone: 'parcelar',
      url: 'parcelar-debitos',
      codigo: 'parcelar_debitos',
      ativo: true
    }
  ];
  private funcionalidadesCartaoFisico = [
    {
      nome: 'Alterar a<br/> senha',
      icone: 'senha',
      codigo: 'senha',
      ativo: true,
      color: 'secondary',
      indice: FuncionalidadeCartaoEnum.CartaoFisicoAlterarSenha,
      virtual: false
    }
  ];
  private funcionalidadesCartaoVirtual = [
    {
      nome: 'Alterar a<br/> senha',
      icone: 'senha',
      codigo: 'senha',
      ativo: true,
      color: 'secondary',
      indice: FuncionalidadeCartaoEnum.CartaoVirtualAlterarSenha,
      virtual: true
    },

    {
      nome: 'Bloquear cartão e gerar novo',
      icone: 'cadeado-fechado',
      codigo: 'bloquear',
      ativo: true,
      color: 'primary',
      indice: FuncionalidadeCartaoEnum.CartaoVirtualBloquearCartao,
      virtual: true
    },
    {
      nome: 'Apagar cartão<br/> virtual',
      icone: 'cartao',
      codigo: 'apagar-cartao',
      ativo: true,
      color: 'secondary',
      indice: FuncionalidadeCartaoEnum.CartaoVirtualApagar,
      virtual: true
    }
  ];
  private produtos$: BehaviorSubject<any[]> = new BehaviorSubject<any[]>(this.produtos);
  private servicos: any[] = [];

  constructor(private storageService: StorageService) {
  }

  getFuncionalidades(idConta: number) {
    this.servicos = this.storageService.getProdutosPermissoes();
    return this.setProdutos(idConta);
  }

  getFuncionalidadesCartao(credencial: Credencial) {
    this.buscarAproximacao(credencial);
    return this.funcionalidadesCartao;
  }

  getMaisServicos() {
    return this.maisServicos;
  }

  getFuncionalidadesPix() {
    return this.functionalidadesPix;
  }

  setProdutos(idConta: number) {
    const servico = this.servicos.find(x => x.idConta == idConta);
    if (servico == null) {
      this.produtos$.next([]);
    }
    for (const produto of this.produtos) {
      produto.ativo = !!servico.funcionalidades.find((x: any) => x.nomeServico == produto.codigo);
    }

    for (const funcionalidade of this.functionalidadesPix) {
      funcionalidade.ativo = !!servico.funcionalidades.find((x: any) => x.nomeServico == funcionalidade.codigo);
    }

    this.produtos$.next(this.produtos);
    return this.produtos$.asObservable();
  }

  buscarAproximacao(credencial: Credencial) {
    let ativo = true;
    const listaDesativados = [AppIdEnum.rp3Bank];
    for (const desativar of listaDesativados) {
      if (desativar == environment.appId) {
        ativo = false;
      }
    }
    this.removeOpcaoMenuDuplicada();
    let aproximacao = {
      nome: credencial.statusNfc == null || credencial.statusNfc == 0 ? 'Habilitar<br/> aproximação' : 'Desabilitar<br/> aproximação',
      icone: credencial.statusNfc == null || credencial.statusNfc == 0 ? 'nfc-desativado' : 'nfc-ativado',
      codigo: 'aproximacao',
      url: 'aproximacao',
      ativo: ativo,
      color: 'secondary'
    };
    this.funcionalidadesCartao.splice(4, 0, aproximacao);
  }

  removeOpcaoMenuDuplicada() {
    let opcaoNfc: any;
    let index: any;

    for (let i = 0; i < this.funcionalidadesCartao.length; i++) {
      opcaoNfc = this.funcionalidadesCartao.find((option) => option.codigo == 'aproximacao');
      index = this.funcionalidadesCartao.indexOf(opcaoNfc);
    }

    if (index != -1) {
      this.funcionalidadesCartao.splice(index, 1);
    }
  }

  getFuncionalidadesCartaoFisico() {
    return this.funcionalidadesCartaoFisico;
  }

  getFuncionalidadesCartaoVirtual() {
    return this.funcionalidadesCartaoVirtual;
  }
}
