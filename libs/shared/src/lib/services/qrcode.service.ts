import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AbstractService } from './abstract.service';
import { resolve } from '@utils/resolve.util';

@Injectable({
  providedIn: 'root'
})
export class QrcodeService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'qrcode', http);
  }

  verificarQrCodeElo(qrcode: string) {
    const request = {
      qrCode: qrcode
    }
    const url = resolve('valloo://verificarQrcode');
    return this.http.post(url, request);
  }

  pagarQrCodeElo(qrcode: any, idCredencial: number) {
    const request = {
      qrCode: qrcode,
      idCredencial: idCredencial
    }
    const url = resolve('valloo://pagarQrcode');
    return this.http.post(url, request);
  }
}
