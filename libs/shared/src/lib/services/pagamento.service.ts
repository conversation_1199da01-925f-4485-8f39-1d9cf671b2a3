import { Injectable } from '@angular/core';
import { AbstractService } from '@corporativo/shared';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PagamentoService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'pagamento', http);
  }

  buscarContratoAtivo(tipoTransacao: number) {
    return this.getOne(`consultar-contrato-ativo/${tipoTransacao}`);
  }

  consultarTitulo(data: { idConta: number, linhaDigitavel: string }) {
    return this.post(data, 'consultar-titulo');
  }

  consultarConvenio(linhaDigitavel: string){
    return this.post({ linhaDigitavel }, 'consultar-convenio');
  }

  pagarTitulo(pagamento: { dataVencimento: string, protocoloInterno: number, valor: number }) {
    return this.post(pagamento, 'pagar-titulo').pipe(map((x: any) => {
        if (!x.sucesso) {
          throw {mensagem: x.mensagemErro};
        }
        return x;
      })
    );
  }
}
