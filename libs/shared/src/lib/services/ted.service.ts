import { AbstractService, environment } from '@corporativo/shared';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { TextUtil } from '@utils/text.util';

const codTransacao = 558;
const idParceiroResgate = 1;

@Injectable({
  providedIn: 'root'
})
export class TedService extends AbstractService<any> {

  constructor(protected override http: HttpClient) {
    super('valloo', 'ted', http);
  }

  listarBancos() {
    const url = resolve('valloo://banco');
    return this.http.get<any>(`${url}`);
  }

  verificaLimiteAoRealizarTransacao(idConta: number, valor: any, contaSelecionada: any) {
    const request = {
      idConta: idConta,
      idInstituicao: environment.idInstituicao,
      idProdInstituicao: contaSelecionada.idProdutoInstituicao,
      valorTransferencia: valor.valor,
      idTipoTransacao: 2
    };
    const url = resolve('valloo://limite');
    return this.http.post(url, request);
  }

  efetuarTransferenciaTed(idConta: number, valor: any, dados: any) {
    const contaFormatado = dados.conta.slice(0, length - 1) + '-' + dados.conta.slice(length - 1);
    const request = {
      idConta: idConta,
      codTransacao: codTransacao,
      idParceiroResgate: idParceiroResgate,
      valor: valor.valor,
      bancoFavorecido: dados.banco.idBanco,
      agenciaFavorecido: dados.agencia,
      contaFavorecido: contaFormatado,
      numDocumentoFavorecido: TextUtil.removeNotDigit(dados.documento),
      nomeFavorecido: dados.nome,
      dataTransacao: valor.data,
      tipoContaFavorecido: dados.tipo,
      finalidade: '00010',
      idPessoaContaBancaria: null
    };
    const url = resolve('valloo://ted');
    return this.http.post<any>(url, request);
  }

}
