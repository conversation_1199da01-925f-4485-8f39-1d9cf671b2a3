import { Injectable } from '@angular/core';
import { AbstractService } from '@corporativo/shared';
import { HttpClient, HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class GoogleService extends AbstractService<any> {

  constructor(protected override http: HttpClient) {
    super('valloo', 'google', http);
  }

  buscarLugaresProximos(texto: any = null, latitude: any = null, longitude: any = null, pagetoken: any = null, tipos: string[] = []) {
    let params = new HttpParams();
    if (texto && texto.trim().length) {
      params = params.append('texto', texto);
    }
    if (latitude && longitude) {
      params = params.append('latitude', latitude);
      params = params.append('longitude', longitude);
    }
    if ((pagetoken && pagetoken.trim().length)) {
      params = params.append('pageToken', pagetoken);
    }
    if (tipos && tipos.length > 0) {
      tipos.forEach(tipo => {
        params = params.append('tipos', tipo);
      });
    }
    return this.getAllBy(params, 'buscar-lugares?');
  }
}
