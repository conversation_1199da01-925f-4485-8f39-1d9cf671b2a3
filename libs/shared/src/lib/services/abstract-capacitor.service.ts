import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpOptions, HttpResponse } from '@capacitor/core';
import { CapacitorHttpPlugin } from '@capacitor/core/types/core-plugins';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';
import { resolve } from '@utils/resolve.util';

export interface RepositoryInterface<T> {
  getAll(): Observable<HttpResponse>;

  getById(id: number): Observable<HttpResponse>;

  getOne(): Observable<HttpResponse>;

  getAllBy(): Observable<HttpResponse>;

  post(entity: T): Observable<HttpResponse>;

  postList(entity: T[]): Observable<HttpResponse>;

  postFormData(entity: FormData): Observable<HttpResponse>;

  putFormData(entity: FormData, id: number | string): Observable<HttpResponse>;

  put(entity: T, id: number): Observable<HttpResponse>;

  patch(entity: T, id: number): Observable<HttpResponse>;

  delete(id: number): Observable<HttpResponse>;
}

export abstract class AbstractCapacitorService<T> implements RepositoryInterface<T> {
  private readonly baseUrl: string;
  private readonly dominio: string;

  protected constructor(dominio: string, baseUrl: string, protected http: CapacitorHttpPlugin) {
    this.dominio = dominio;
    this.baseUrl = baseUrl;
  }

  public getAll(relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl);
    return fromPromise(this.http.get(options)).pipe(map(this.extractData));
  }

  public getById(id?: number | string, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl, id);
    return fromPromise(this.http.get(options)).pipe(map(this.extractData));
  }

  public getAllBy(params?: any, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl);
    options.params = params;
    return fromPromise(this.http.get(options)).pipe(map(this.extractData));
  }

  public getOne(relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl);
    return fromPromise(this.http.get(options)).pipe(map(this.extractData));
  }

  public post(entity: T, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl);
    options.data = entity;
    return fromPromise(this.http.post(options)).pipe(map(this.extractData));
  }

  public postFormData(entity: FormData, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl);
    options.data = entity;
    return fromPromise(this.http.post(options)).pipe(map(this.extractData));
  }

  public putFormData(entity: FormData, id: number | string, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl, id);
    options.data = entity;
    return fromPromise(this.http.put(options)).pipe(map(this.extractData));
  }

  public postList(entity: T[], relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl);
    options.data = entity;
    return fromPromise(this.http.post(options)).pipe(map(this.extractData));
  }

  public put(entity: T, id: number | string, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl, id);
    options.data = entity;
    return fromPromise(this.http.put(options)).pipe(map(this.extractData));
  }

  public patch(entity: T, id: number | string, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl, id);
    options.data = entity;
    return fromPromise(this.http.patch(options)).pipe(map(this.extractData));
  }

  public delete(id: any, relativeUrl?: string): Observable<HttpResponse> {
    const options: HttpOptions = this.getOptions(relativeUrl, id);
    return fromPromise(this.http.delete(options));
  }

  private getOptions(relativeUrl?: string, id?: string | number) {
    let url = this.getUrl(relativeUrl);
    if (id) {
      url = `${this.getUrl(relativeUrl)}/${id}`;
    }
    return {
      url,
      headers: this.headers(),
    };
  }

  protected getUrl(relativeUrl?: string, args?: any): string {
    let target = `${this.dominio}://${this.baseUrl}`;
    if (relativeUrl !== null && relativeUrl !== undefined) {
      target = `${this.dominio}://${relativeUrl}`;
    }
    return resolve(target, args);
  }

  protected headers() {
    return {'Content-Type': 'application/json'};
  }

  protected extractData(res: any) {
    let body: any = res;
    if ('resposta' in body) {
      body = body.resposta;
    }
    if ('data' in body) {
      body = body.data;
    }
    return body;
  }
}
