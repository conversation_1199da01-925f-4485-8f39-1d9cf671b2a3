import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';
import { AbstractService, Saldo } from '@corporativo/shared';

@Injectable({
  providedIn: 'root',
})
export class SaldoService extends AbstractService<Saldo> {
  constructor(protected httpClient: HttpClient) {
    super('valloo', 'portador', httpClient);
  }

  buscarSaldo(idConta: number) {
    if (!idConta) {
      return of();
    }
    return this.getOne('conta/buscar-saldo-conta/' + idConta);
  }
}
