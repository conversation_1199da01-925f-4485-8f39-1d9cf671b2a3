import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AbstractService } from './abstract.service';
import { Versao } from '../interfaces';
import { environment, StorageService } from '@corporativo/shared';
import { Platform } from '@ionic/angular';
import { resolve } from '@utils/resolve.util';
import { App } from '@capacitor/app';
import { concatMap, from, Observable, of } from 'rxjs';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class VersaoService extends AbstractService<Versao> {

  constructor(
    protected override http: HttpClient,
    private platform: Platform,
    private storageService: StorageService,
    private router: Router) {
    super('valloo', 'versao', http);
  }

  verificarVersao(): Observable<Versao> {
    const payload = {
      idProcessadora: environment.idProcessadora,
      idInstituicao: environment.idInstituicao,
      sistemaOperacional: this.platform.is('ios') ? 1 : 2,
      versaoConhecida: '',
      versaoInstalada: '',
      idApp: environment.idAplicativo
    }
    return from(this.getAppInfo()).pipe(concatMap((info) => {
      payload.versaoConhecida = info.version;
      payload.versaoInstalada = info.version;
      return this.http.post<Versao>(resolve('valloo://verificarVersao'), payload);
    }));
  }

  private getAppInfo() {
    if (this.platform.is('hybrid')) {
      return from(App.getInfo());
    }
    return of({
      name: '',
      id: '',
      build: '2.0.0',
      version: '2.0.0'
    })
  }

  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (this.storageService.atualizarVersao()) {
      const versaoApp = this.storageService.getVersaoApp();
      this.router.navigate(['atualizar-versao'], {state: {mensagem: versaoApp.mensagem}, replaceUrl: true})
    }
    return true;
  }
}
