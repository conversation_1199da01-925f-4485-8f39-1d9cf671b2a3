import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { AbstractService, environment } from '@corporativo/shared';
import {Diretriz} from '../interfaces/diretriz.interface';

@Injectable({
  providedIn: 'root'
})
export class DiretrizService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'diretrizes', http);
  }

  buscarTermosUso() {
    return this.http.get<Diretriz>(resolve('valloo://diretrizes', {
      idAplicativo: environment.idAplicativo,
      tipo: 2
    }));
  }

  buscarPoliticaPrivacidade() {
    return this.http.get<any>(resolve('valloo://diretrizes', {
      idAplicativo: environment.idAplicativo,
      tipo: 1
    }));
  }

  consultarDiretrizes(tipo: string) {
    return this.http.get<any>(resolve('valloo://diretrizes', {
      idApp: environment.idAplicativo,
      tipo: tipo
    }))
  }
}
