import { Injectable } from '@angular/core';
import { AbstractService, environment, ParametroEnum } from '@corporativo/shared';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { TextUtil } from '@utils/text.util';
import { AtualizarDadosRequest } from '../request/atualizar-dados-request';
import { EnderecoUsuario } from '../request/endereco-usuario-request';
import { forkJoin } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class FuncionalidadeService extends AbstractService<any> {

  constructor(protected override http: HttpClient) {
    super('valloo', 'funcionalidade', http);
  }

  enviarTokenSenha(documento: string, celular: string) {
    const request = {
      documento: documento,
      cpfRepresentante: null,
      dataNascimento: celular,
      idInstituicao: environment.idInstituicao,
      idProcessadora: environment.idProcessadora,
      ddd: Number(celular.substring(0, 2)),
      celular: Number(celular.substring(2)),
    };
    const url = resolve('valloo://enviarTokenSenhaCorporativo');
    return this.http.post(url, request);
  }

  atualizarDados(form: any, dados: any, dadosPessoais: any) {
    const request = new AtualizarDadosRequest();
    request.documento = dados.documento;
    request.nomeCompleto = dadosPessoais.nome;
    request.dataNascimento = TextUtil.converteData(dadosPessoais.dataNascimento);
    request.idSexo = Number(dadosPessoais.sexo);
    request.estadoCivil = Number(dadosPessoais.estadoCivil);
    request.dddTelefoneCelular = dados.dddTelefoneCelular;
    request.telefoneCelular = dados.telefoneCelular;
    request.email = form.email;
    request.nacionalidade = dadosPessoais.nacionalidade;
    request.origemConfirmacao = 'APLICATIVO';

    const endereco = new EnderecoUsuario();
    endereco.cep = TextUtil.removeNotDigit(form.cep);
    endereco.logradouro = form.logradouro;
    endereco.numero = Number(form.numero);
    endereco.complemento = form.complemento;
    endereco.bairro = form.bairro;
    endereco.cidade = form.cidade;
    endereco.uf = form.uf;
    request.enderecoResidencial = endereco;

    const url = resolve('valloo://dadosUsuario', { idProgramaFidelidade: environment.idProgramaFidelidade });
    return this.http.put(url, request);
  }

  faleConosco() {
    const url = resolve('valloo://faleConosco');
    return this.http.get(url);
  }

  buscarParametroValor(chave: string) {
    const idProcessadora = environment.idProcessadora;
    const idInstituicao = environment.idInstituicao;
    const url = resolve('valloo://parametro');
    return this.http.get(`${url}/${chave}/processadora/${idProcessadora}/instituicao/${idInstituicao}`);
  }

  buscarServicos() {
    const chaveFgts = ParametroEnum.HabBotaoFgts;
    const chaveParcelamentoDebitos = ParametroEnum.HabBotaoParcDeb;
    const idProcessadora = environment.idProcessadora;
    const idInstituicao = environment.idInstituicao;
    const url = resolve('valloo://parametro');
    const fgts = this.http.get(`${url}/${chaveFgts}/processadora/${idProcessadora}/instituicao/${idInstituicao}`);
    const parcelamento = this.http.get(`${url}/${chaveParcelamentoDebitos}/processadora/${idProcessadora}/instituicao/${idInstituicao}`);
    return forkJoin([fgts, parcelamento]).pipe(
      map(([fgtsResult, parcelamentoDebitosResult]) => {
        return { fgtsResult, parcelamentoDebitosResult };
      })
    );
  }

  buscarFuncionalidesCartao(funcionalidades: any[]) {
    return this.buscarParametroValor(ParametroEnum.FuncionalidadesApp).pipe(
      map((x: any) => x.shift()),
      map((parametro: any) => {
        const indices = parametro.valorParametro.split(',');
        for (const funcionalidade of funcionalidades) {
          if (indices[funcionalidade.indice]) {
            funcionalidade.ativo = indices[funcionalidade.indice] == 1;
          }
        }
        return funcionalidades;
      })
    );
  }
}
