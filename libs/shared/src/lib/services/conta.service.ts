import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AbstractService } from '@corporativo/shared';
import { resolve } from '@utils/resolve.util';

@Injectable({
  providedIn: 'root'
})
export class ContaService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'conta', http);
  }

  transferir(data: any) {
    const url = resolve('valloo://tranferirMesmoBolso');
    return this.http.post(url, data);
  }

  buscarDadosConsumo(idConta: number) {
    const url = resolve('valloo://dadosConsumo', { idConta });
    return this.http.get(url);
  }
}
