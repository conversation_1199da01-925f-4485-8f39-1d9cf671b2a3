import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { AbstractService } from '@corporativo/shared';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class NotificacoesService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient
  ) {
    super('valloo', 'notificacoes', http);
  }

  buscarNotificacoes(page = 1, pageSize = 20) {
    return this.getOne(`mensagens-paginadas?page=${page}&pageSize=${pageSize}`);
  }

  marcarMensagensComoLidas() {
    const url = resolve('notificacoes');
    return this.http.put(`${url}/marcar-todas-notificacoes`, {});
  }

  marcarMensagemComoLida(idMensagem: number) {
    const url = resolve('notificacoes');
    return this.http.put(`${url}/mensagem/${idMensagem}`, null);
  }

  buscarNotificacaoDestaque(documento: string) {
    return this.getOne(`busca-notificacao/ultimo-destaque/${documento}`);
  }

  buscarCountNotificacoes() {
    return this.getOne(`mensagens-paginadas?page=${1}&pageSize=${1}&msgLida=false`).pipe(
      map(m => m.totalElements)
    );
  }
}
