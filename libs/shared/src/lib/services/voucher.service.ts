import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { AbstractService, environment, Usuario, Voucher } from '@corporativo/shared';
import { map } from 'rxjs/operators';
import * as lodash from 'lodash';
import { format } from 'date-fns';

@Injectable({
  providedIn: 'root'
})
export class VoucherService extends AbstractService<Voucher> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'voucher', http);
  }

  buscarProdutos() {
    return this.getAll(`buscar-produtos/${environment.idProcessadora}/${environment.idInstituicao}`).pipe(
      map((data: any) => {
        data.produtos.map((i: any) => i.precoBTDIsemFrete = +i.precoBTDIsemFrete);
        data.produtos = data.produtos.filter((m: any) => m.precoBTDIsemFrete < 1000);
        const grupos = lodash.groupBy(data.produtos, 'fabricanteNome');
        let produtos: any[] = [];
        Object.keys(grupos).forEach(e => {
          const items = grupos[e];
          const p = {
            nome: e,
            items: lodash.orderBy(items, ['precoBTDIsemFrete'], ['asc']),
            foto: items[0].fotos.p || ''
          };
          produtos.push(p);
        });
        produtos = lodash.orderBy(produtos, ['nome'], ['asc']);
        return produtos;
      })
    );
  }

  resgatar(produto: Voucher, usuario: Usuario, idConta: number) {
    const data: any = {
      bairroEndPrincipal: usuario.enderecoResidencial.bairro,
      cepEndPrincipal: usuario.enderecoResidencial.cep,
      cidadeEndPrincipal: usuario.enderecoResidencial.cidade,
      complementoEndPrincipal: usuario.enderecoResidencial.complemento,
      estadoEndPrincipal: usuario.enderecoResidencial.uf,
      logradouroEndPrincipal: usuario.enderecoResidencial.logradouro,
      numeroEndPrincipal: usuario.enderecoResidencial.numero,
      dddEndPrincipal: usuario.dddTelefoneCelular.toString(),
      telefoneEndPrincipal: usuario.telefoneCelular.toString(),
      codProduto: produto.codigo,
      codTransacao: 846,
      cpf: usuario.documento,
      dataNascimento: format(new Date(usuario.dataNascimento), 'dd/MM/yyyy'),
      descricaoProduto: produto.produtoNome,
      email: usuario.email,
      formaPagamento: 'FATURAMENTO',
      idConta,
      nome: usuario.nomeCompleto,
      nomeProduto: produto.produtoNome,
      qtdProduto: 1,
      sexo: usuario.idSexo === 1 ? 'M' : 'F',
      tipoPessoa: 'FISICA',
      valorUnitarioProduto: parseInt(produto.precoBTDIsemFrete, 10)
    };
    return this.post(data, 'inserir-pedido');
  }
}
