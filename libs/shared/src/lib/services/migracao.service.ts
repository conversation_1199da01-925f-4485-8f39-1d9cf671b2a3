import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import {
  AbstractService,
  AuthService,
  Extrato,
  StatusTedEnum,
} from '@corporativo/shared';
import { map } from 'rxjs/operators';
import * as lodash from 'lodash';
import { Observable } from 'rxjs';
import { CapacitorHttp, HttpOptions } from '@capacitor/core';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';

@Injectable({
  providedIn: 'root',
})
export class MigracaoService extends AbstractService<Extrato> {
  private transacoes: any[] = [];
  private transacoesFiltradas: any[] = [];

  constructor(
    protected override http: HttpClient,
    private authService: AuthService
  ) {
    super('valloo', 'migracao', http);
  }

  buscarContasMigradas(documento: string) {
    const url = resolve('migracao');
    return this.http.get(`${url}/buscar-contas/${documento}`);
  }

  buscarExtratoMigrado(
    idConta: number,
    idContaOrigem: number,
    dataInicio: string,
    dataFim: string
  ) {
    const relativeUrl = `${idConta}/extrato/data_inicial/${dataInicio}/data_final/${dataFim}`;
    return this.getAll(relativeUrl).pipe(
      map((extratos) => {
        extratos = extratos.filter((x: any) => x.idConta == idContaOrigem);
        this.transacoes = extratos;
        this.transacoesFiltradas = [...extratos];
        return this.transformeToTransacoes(extratos);
      })
    );
  }

  private transformeToTransacoes(extratos: any[]) {
    const transacoes: any[] = [];
    const functionCodesComprovantes = [
      '558',
      '670',
      '880',
      '884',
      '698',
      '558',
    ];
    for (const extrato of extratos) {
      functionCodesComprovantes.includes(extrato.functionCode, 1);
      extrato.possuiComprovante = functionCodesComprovantes.includes(
        extrato.functionCode,
        1
      );
      if (
        !transacoes.filter((x) => x.dataFmt == extrato.dataTransacaoFmt).length
      ) {
        transacoes.push({
          data: extrato.dataTransacao,
          dataFmt: extrato.dataTransacaoFmt,
        });
      }
    }
    for (const transacao of transacoes) {
      transacao.operacoes = extratos.filter(
        (x) => x.dataTransacaoFmt == transacao.dataFmt
      );
    }
    return lodash.orderBy(transacoes, ['data'], 'desc');
  }

  buscarComprovanteMigrados(transacao: any) {
    return this.http
      .post<any>(resolve('valloo://consultarComprovanteMigracao'), transacao)
      .pipe(
        map((x) => {
          const mensagem: any = {};
          if (x.statusOperacao == StatusTedEnum.Reprovado) {
            mensagem.titulo = x.tituloStatusModal;
            mensagem.descricao = x.motivo;
            x.mensagem = mensagem;
          }
          if (
            x.statusOperacao == StatusTedEnum.Rejeitado ||
            x.statusOperacao == StatusTedEnum.AguardandoAprovacao
          ) {
            mensagem.titulo = x.tituloStatusModal;
            mensagem.descricao = x.descricaoStatus;
            x.mensagem = mensagem;
          }
          if (x.status == 404) {
            mensagem.titulo = x.tituloStatusModal;
            mensagem.descricao = x.descricaoStatus;
            x.mensagem = mensagem;
          }
          return x;
        })
      );
  }

  baixarExtratoContasMigradas(data: any): Observable<any> {
    const options: HttpOptions = this.getOptions();
    options.data = data;
    return fromPromise(CapacitorHttp.post(options)).pipe(
      map(this.extractDataNative)
    );
  }

  private getOptions() {
    let url = resolve('valloo://downloadExtratoMigracao');

    return {
      url,
      headers: {
        'Content-Type': 'application/json',
        AuthorizationPortador: 'Bearer ' + this.authService.getToken(),
      },
      webFetchExtra: {
        credentials: 'include',
      },
      responseType: 'arraybuffer',
    } as HttpOptions;
  }

  extractDataNative(res: any) {
    if (res.error) {
      throw 'Ocorreu um erro inesperado.';
    }
    let body: any = res;
    if ('resposta' in body) {
      body = body.resposta;
    }
    if ('data' in body) {
      body = body.data;
    }
    return body;
  }

  baixarExtrato(dadosExtrato: any) {
    const options: any = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      responseType: 'arraybuffer',
    };
    return this.http.post(
      resolve('valloo://downloadExtratoMigracao'),
      dadosExtrato,
      options
    );
  }

  cancelarCredenciaisMigradas(credenciais: any) {
    const request = {
      credenciais: credenciais,
      statusDestino: 30,
    };
    return this.http.post(
      resolve('valloo://cancelarCredenciaisMigradas'),
      request
    );
  }
}
