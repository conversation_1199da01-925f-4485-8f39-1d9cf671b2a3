import { Injectable } from '@angular/core';
import { Usuario } from '../interfaces';

const ATUALIZAR_VERSAO = '_corporativo_atualizar_app';
const VERSAO_APLICATIVO = '_corporativo_versao_app';
const VALLOO_TOKEN = '_corporativo_token';
const VALLOO_USER = '_corporativo_user';
const VALLOO_BIOMETRIA = '_corporativo_biometria';
const VALLOO_PRODUTOS_PERMISSOES = '_corporativo_produtos_permissoes';
const VERIFICAR_POSSUI_CONTA_INICIO_APP = '_corporativo_possui_conta';
const IMAGEM_AVATAR = 'imagem-avatar';
const ID_LOGIN = '_corporativo_id_login';
const FORCAR_ONBOARD = '_corporativo_forcar_oboard';
const ONE_SIGNAL_ID = '_corporativo_one_signal_id';
const DADOS_PESSOAIS = '_corporativo_dados_pessoais';
const DOCUMENTO_ACESSO = 'documentoAcesso';

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  constructor() {
  }

  setProdutosPermissoes(permissoes: any[]) {
    localStorage.setItem(VALLOO_PRODUTOS_PERMISSOES, JSON.stringify(permissoes));
  }

  getProdutosPermissoes() {
    return JSON.parse(localStorage.getItem(VALLOO_PRODUTOS_PERMISSOES) as any);
  }

  setAtualizarVersao(atualizar: boolean) {
    localStorage.setItem(ATUALIZAR_VERSAO, JSON.stringify(atualizar));
  }

  atualizarVersao() {
    return JSON.parse(localStorage.getItem(ATUALIZAR_VERSAO) as any);
  }

  setVersaoApp(versaoApp: any) {
    localStorage.setItem(VERSAO_APLICATIVO, JSON.stringify(versaoApp));
  }

  getVersaoApp() {
    return JSON.parse(localStorage.getItem(VERSAO_APLICATIVO) as any);
  }

  setToken(token: string) {
    localStorage.setItem(VALLOO_TOKEN, token);
  }

  getToken() {
    return localStorage.getItem(VALLOO_TOKEN);
  }

  clearToken() {
    localStorage.removeItem(VALLOO_TOKEN);
  }

  setUser(usuario: Usuario) {
    localStorage.setItem(VALLOO_USER, JSON.stringify(usuario));
  }

  getUser() {
    const user: any = localStorage.getItem(VALLOO_USER);
    return JSON.parse(user);
  }

  clearUser() {
    this.clearToken()
    localStorage.removeItem(VALLOO_USER);
    localStorage.removeItem(VALLOO_BIOMETRIA);
    localStorage.removeItem(IMAGEM_AVATAR);
    localStorage.removeItem(VALLOO_PRODUTOS_PERMISSOES);
    localStorage.removeItem(ID_LOGIN);
  }

  setBiometricActive(biometric: any = null) {
    if (!biometric) {
      biometric = {
        first: true,
        active: false
      };
    }
    localStorage.setItem(VALLOO_BIOMETRIA, JSON.stringify(biometric));
  }

  getBiometricActive() {
    const biometria: any = localStorage.getItem(VALLOO_BIOMETRIA);
    return JSON.parse(biometria);
  }

  verificarPossuiContaInicioApp() {
    return JSON.parse(localStorage.getItem(VERIFICAR_POSSUI_CONTA_INICIO_APP) as any);
  }

  marcarPossuiContaInicioApp() {
    localStorage.setItem(VERIFICAR_POSSUI_CONTA_INICIO_APP, JSON.stringify(true));
  }

  setIdLogin(idLogin: any) {
    localStorage.setItem(ID_LOGIN, idLogin);
  }

  getIdLogin() {
    return localStorage.getItem(ID_LOGIN);
  }

  setAvatarImage(img: any, documento: string) {
    localStorage.setItem(IMAGEM_AVATAR + '_' + documento, img);
  }

  getAvatarImage(documento: string) {
    return localStorage.getItem(IMAGEM_AVATAR + '_' + documento);
  }

  setForcarOnboard(forcar: boolean) {
    localStorage.setItem(FORCAR_ONBOARD, forcar.toString());
  }

  getForcarOnboard() {
    return localStorage.getItem(FORCAR_ONBOARD);
  }

  setOneSignalId(oneSignalId: string | null) {
    if (!oneSignalId) {
      return;
    }
    localStorage.setItem(ONE_SIGNAL_ID, oneSignalId);
  }

  getOneSignalId() {
    return localStorage.getItem(ONE_SIGNAL_ID);
  }

  setDadosPessoais(dadosPessoais: any) {
    console.log('==> dadosPessoais: ', dadosPessoais);
    localStorage.setItem(DADOS_PESSOAIS, JSON.stringify(dadosPessoais));
  }

  getDadosPessoais() {
    return JSON.parse(localStorage.getItem(DADOS_PESSOAIS) as any);
  }

  removeStorage(id: string) {
    localStorage.removeItem(id);
  }

  setDocumentoAcesso(documentoAcesso: any){
    localStorage.setItem(DOCUMENTO_ACESSO, documentoAcesso);
  }

  getDocumentoAcesso(){
    return localStorage.getItem(DOCUMENTO_ACESSO || '');
  }
}
