import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { AbstractService, environment } from '@corporativo/shared';

@Injectable({
  providedIn: 'root'
})
export class SaqueFgtsService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient) {
    super('valloo', 'saqueFgts', http);
  }

  buscarTermosUso() {
    return this.http.get<any>(resolve('valloo://diretrizes', {
      idAplicativo: environment.idAplicativo,
      tipo: 2
    }));
  }

  buscarPoliticaPrivacidade() {
    return this.http.get<any>(resolve('valloo://diretrizes', {
      idAplicativo: environment.idAplicativo,
      tipo: 1
    }));
  }

  registrarEnvioMensagemQista(idPortadorLogin: number) {
    const url = resolve('valloo://saqueFgts');
    return this.http.post(url, {idPortadorLogin})
  }
}
