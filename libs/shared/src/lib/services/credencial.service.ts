import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { TipoPessoaEnum } from '../enums';
import { map, pluck } from 'rxjs/operators';
import { Credencial, environment, StorageService } from '@corporativo/shared';
import { AbstractService } from './abstract.service';
import { HashUtil } from '@utils/hash.util';
import { resolve } from '@utils/resolve.util';
import { Observable } from 'rxjs';
import { CapacitorHttp, HttpOptions } from '@capacitor/core';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';

@Injectable({
  providedIn: 'root'
})
export class CredencialService extends AbstractService<Credencial> {

  constructor(protected override http: HttpClient, private storageService: StorageService) {
    super('valloo', 'credencial', http);
  }

  buscarCredenciaisDisponiveis(documento: string) {
    const idProcessadora = environment.idProcessadora;
    const idInstituicao = environment.idInstituicao;
    const tipoPessoa = documento.length > 11 ? TipoPessoaEnum.Juridica : TipoPessoaEnum.Fisica;
    const relativeUrl = `${documento}/pessoa/${tipoPessoa}/processadora/${idProcessadora}/instituicao/${idInstituicao}/disponiveis`;
    return this.getAll(relativeUrl).pipe(pluck('credenciais'));
  }

  validarSenhaCartao(data: any) {
    return this.post(data, 'validar-senha-cartao');
  }

  buscarCartoes() {
    return this.getAll('grupo/credencial-conta/listar/0').pipe(map((x: any) => x.credenciaisConta));
  }

  buscarCredenciais() {
    return this.getAll('grupo/credencial-conta/listar').pipe(map((x: any) => x.credenciaisConta));
  }

  buscarPlastico(idPlastico: number) {
    const url = resolve('valloo://plastico', { idPlastico });
    const responseType: any = 'blob';
    return this.http.get(url, { responseType });
  }

  buscarCartaoFisico(idCredencial: number) {
    const url = resolve('valloo://credencialDetalhe', {
      idCredencial: idCredencial
    });
    return this.http.get(url);
  }

  buscarCredencialVirtual(idConta: number) {
    const relativeUrl = `virtual/conta/${idConta}`;
    return this.getAll(relativeUrl).pipe(map((value: any) => {
      const cartoes = [];
      for (const item of value.credenciais) {
        item.credencialMascarada = item.credencialMascarada.replaceAll('X', '●').replaceAll('-', ' ');
        cartoes.push(item);
      }
      return cartoes;
    }));
  }

  validarEtapasCredencial(idCredencial: number, senhaNumerica: number | string, token: string) {
    const pin = senhaNumerica + token;
    const senha = HashUtil.hash(pin);
    const url = resolve('valloo://validarEtapasCredencial');
    return this.http.post(url, { idCredencial, senha });
  }

  desbloquearCredencial(credencial: any, dados: any): Observable<any> {
    const numeroMeioCredencial = dados.numeroCartao.substring(5, 14).replace(/\s/g, '');
    const request = {
      idUsuario: 999999,
      idCredencial: credencial.idCredencial,
      numeroMeioCredencial: numeroMeioCredencial,
      codigoCvv: dados.cve,
      mes: dados.dataValidade.substring(0, 2),
      ano: dados.dataValidade.substring(3, 5)
    };
    const url = resolve('valloo://desbloqueio');
    return this.http.post(url, request);
  }

  salvarSenha(idCredencial: number, senhaNumerica: number | string, token: string) {
    const pin = senhaNumerica + token;
    const senha = HashUtil.hash(pin);
    const url = resolve('valloo://salvarSenha');
    return this.http.post(url, { idCredencial, senha });
  }

  requisitarCartaoVirtual(credencial: any) {
    const request = {
      idConta: credencial.idConta,
      idPessoa: credencial.idPessoa,
      idUsuario: 999999,
      virtual: true,
      virtualApelido: 'VALLOO VIRTUAL',
      virtualMesesValidade: 12,
      onboard: true
    };
    const url = resolve('valloo://criarCartaoVirtual');
    return this.http.post(url, request);
  }

  bloquearDesbloquearNFC(estado: number, idCredencial: number) {
    const request = {
      estado: estado,
      idCredencial: idCredencial
    };
    const url = resolve('credencial');
    return this.http.post(`${url}/alterar-nfc-credencial`, request);
  }

  bloquearCredencial(credencial: any) {
    const request = {
      tipoEstado: '5',
      idConta: credencial.idConta,
      idCredencial: credencial.idCredencial
    };
    const url = resolve('credencial');
    return this.http.post(`${url}/bloqueio-desbloqueio-portador`, request);
  }

  solicitarSegundaVia(idCredencial: number, idConta: number, virtual: boolean) {
    const request = {
      idCredencial,
      idConta,
      deveForcarCobranca: !virtual,
      cobrarTarifa: !virtual
    };
    const url = resolve('credencial');
    return this.http.post(`${url}/segunda-via`, request);
  }

  solicitarCredencial(idCredencial: number, idConta: number, primeiraViaFisica = false) {
    const request = {
      idCredencial,
      idConta,
      deveForcarCobranca: true,
      cobrarTarifa: true,
      primeiraViaFisica
    };
    const url = resolve('credencial');
    return this.http.post(`${url}/solicitar-credencial`, request);
  }


  buscarTarifaTransacao(idPerfilTarifario: number, codTransacao: number) {
    const url = resolve('valloo://buscarTarifa', { idPerfil: idPerfilTarifario, codTransacao: codTransacao });
    return this.http.get(url);
  }

  gerarCartaoVirtual(credencial: any) {
    const request = {
      idConta: credencial.idConta,
      idPessoa: credencial.idPessoa,
      idUsuario: 999999,
      virtual: true,
      virtualApelido: 'MULTICONTA VIRTUAL',
      virtualMesesValidade: 12,
      onboard: true
    };
    const url = resolve('valloo://gerarCartaoVirtual');
    return this.http.post(url, request);
  }

  cancelarCredencial(status: number, idCredencial: number, idUsuario: string) {
    const url = resolve('credencial');
    return this.http.post(`${url}/cancelar-credencial`, { idCredencial, status, idUsuario });
  }

  buscarPerfilTarifario(idConta: number) {
    const uri = resolve('valloo://perfilTarifario', { idConta: idConta });
    return this.http.get(uri);
  }

  desbloquearCredencialTemporario(idCredencial: number) {
    const request = {
      idCredencial: idCredencial,
      idUsuario: 99999,
      tipoEstado: 1
    };
    const url = resolve('credencial');
    return this.http.post(`${url}/bloqueio-desbloqueio-portador`, request);
  }

  baixarPlasticoNative(idPlastico: string): Observable<any> {
    const options: HttpOptions = this.getOptions(idPlastico);
    return fromPromise(CapacitorHttp.get(options)).pipe(map(this.extractDataNative));
  }

  private getOptions(idPlastico: string) {
    const url = resolve('valloo://plastico', { idPlastico });
    return {
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorizationportador': 'Bearer ' + this.storageService.getToken()
      },
      webFetchExtra: {
        credentials: 'include'
      },
      responseType: 'arraybuffer'
    } as HttpOptions;
  }

  private extractDataNative(res: any) {
    let body: any = res;
    if ('resposta' in body) {
      body = body.resposta;
    }
    if ('data' in body) {
      body = body.data;
    }
    return body;
  }

  consultarContadorSenhaInvalida(idCredencial: number){
    const url = resolve('credencial');
    return this.http.get(`${url}/contador-senha/${idCredencial}`);
  }

  desbloquearPortador(idCredencial: number) {
    const request = {
      tipoEstado: '1',
      idCredencial: idCredencial,
      idUsuario: 99999,
    }
    const url = resolve('credencial');
    return this.http.post(`${url}/bloqueio-desbloqueio-portador`, request)
  }

}
