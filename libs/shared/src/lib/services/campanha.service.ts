import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AbstractService, AuthService, Campanha, environment } from '@corporativo/shared';
import { resolve } from '@utils/resolve.util';
import { Observable } from 'rxjs';
import { CapacitorHttp, HttpOptions } from '@capacitor/core';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CampanhaService extends AbstractService<Campanha> {

  constructor(
    private authService: AuthService,
    protected override http: HttpClient
  ) {
    super('valloo', 'campanha', http);
  }

  buscarTodas(documento: string, idPontoRelacionamento?: number) {
    return this.getAll(`buscar-campanhas-adesao/${documento}/${idPontoRelacionamento}`);
  }

  aceitar(documento: string, campanha: Campanha) {
    return this.post(campanha, `aceitar/${documento}/${campanha.idCampanha}`);
  }

  cancelar(idCampanha: number, documento: string, campanha: Campanha) {
    return this.post(campanha, `ciente-cancelamento/${documento}/${idCampanha}`);
  }

  baixarRegulamento(idCampanha: number, nomeArquivo: string) {
    const options: any = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      responseType: 'arraybuffer',
    }
    const url = resolve('valloo://campanha') + `/regulamento/download/${idCampanha}/${nomeArquivo}`;
    return this.http.get(url, options);
  }

  baixarRegulamentoNative(idCampanha: number, nomeArquivo: string): Observable<any> {
    const options: HttpOptions = this.getOptions(idCampanha, nomeArquivo);
    return fromPromise(CapacitorHttp.get(options)).pipe(map(this.extractDataNative));
  }

  private getOptions(idCampanha: number, nomeArquivo: string) {
    const url = resolve('valloo://campanha') + `/regulamento/download/${idCampanha}/${nomeArquivo}`
    return {
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorizationportador': 'Bearer ' + this.authService.getToken()
      },
      webFetchExtra: {
        credentials: 'include'
      },
      responseType: 'arraybuffer'
    } as HttpOptions;
  }

  extractDataNative(res: any) {
    let body: any = res;
    if ('resposta' in body) {
      body = body.resposta;
    }
    if ('data' in body) {
      body = body.data;
    }
    return body;
  }

  gerarTokenMotiva(documento: string) {
    const dados = {
      documento: documento,
      idFuncionalidade: 5,
      idProcessadora: environment.idProcessadora,
      idInstituicao: environment.idInstituicao,
    }
    const url = resolve('valloo://gerarTokenMotiva');
    return this.http.post(url, dados)
  }
}
