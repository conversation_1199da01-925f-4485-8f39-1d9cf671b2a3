import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { concatMap, map, mergeMap, pluck, tap } from 'rxjs/operators';
import { Platform } from '@ionic/angular';
import { Device } from '@capacitor/device';
import { App } from '@capacitor/app';
import { Geolocation } from '@capacitor/geolocation';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';
import { NativeBiometric } from 'capacitor-native-biometric';
import { CredencialService } from './credencial.service';
import { TextUtil } from '@utils/text.util';
import { resolve } from '@utils/resolve.util';
import { calcularDigitoVerificadorConta } from '@utils/app.util';
import { environment, Servicos, StorageService, Usuario } from '@corporativo/shared';
import { AbstractService } from './abstract.service';
import { BehaviorSubject, lastValueFrom } from 'rxjs';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService extends AbstractService<Usuario> {
  private usuario$: BehaviorSubject<Usuario> = new BehaviorSubject<Usuario>({} as Usuario);
  private pinCredencial = '';

  constructor(
    protected httpClient: HttpClient,
    private platform: Platform,
    private credencialService: CredencialService,
    private storageService: StorageService,
    private router: Router
  ) {
    super('valloo', 'auth', httpClient);
  }

  entrar(auth: any) {
    auth.cpf = TextUtil.removeNotDigit(auth.cpf);
    return this.autenticar(auth);
  }

  private setBiometricCredentials(auth: any) {
    if (!this.platform.is('hybrid')) {
      return;
    }
    NativeBiometric.isAvailable().then(x => {
      if (x && x.isAvailable) {
        NativeBiometric.setCredentials({
          username: auth.cpf || '',
          password: auth.senha || '',
          server: this.getUrl() + '?' + environment.appId
        }).then();
      }
    });
  }

  autenticar(auth: any) {
    return fromPromise(this.getDeviceInfo())
      .pipe(
        concatMap((data: any) => {
          data.cpf = auth.cpf;
          if (auth.cnpj) {
            data.cnpj = TextUtil.removeNotDigit(auth.cnpj);
            data.documentoAcesso = TextUtil.removeNotDigit(auth.documento);
            this.storageService.setDocumentoAcesso(data.documentoAcesso);
          }
          if (environment.idGrupoAcesso) {
            data.grupoAcesso = environment.idGrupoAcesso;
          }
          data.senha = auth.senha;
          data.tipoLogin = environment.tipoLoginPj;
          data.idInstituicao = environment.idInstituicao;
          data.idProcessadora = environment.idProcessadora;
          return this.post(data).pipe(tap((x: any) => this.storageService.setToken(x.token))).pipe(
            concatMap((auth) => {
              if (!this.getBiometricActive()) {
                this.setBiometricActive();
              }
              this.setBiometricCredentials(data);
              return this.buscarDados().pipe(map(x => {
                return auth;
              }));
            })
          );
        })
      );

  }

  getBiometricCredentials() {
    if (!this.platform.is('hybrid')) {
      return;
    }
    return NativeBiometric.getCredentials({
      server: this.getUrl() + '?' + environment.appId
    });
  }

  private async getDeviceInfo() {
    let architectureInfo = 'DESENVOLVIMENTO';
    let deviceId = 'DESENVOLVIMENTO';
    let imei = 'DESENVOLVIMENTO';
    let model = 'DESENVOLVIMENTO';
    let platformName = 'DESENVOLVIMENTO';
    let plataformVersion = 'DESENVOLVIMENTO';
    let sistemaOperacional = 1;
    let pushNotificationDeviceId = '';
    let versaoInstalada = '1.4.07';
    let versaoConhecida = '1.4.07';
    let versaoAplicativo = '1.0.0';
    let latitude = 0;
    let longitude = 0;

    if (this.platform.is('hybrid')) {
      const deviceInfo = await Device.getInfo();
      const idDevice = await Device.getId();
      architectureInfo = deviceInfo.manufacturer;
      deviceId = idDevice.identifier;
      model = deviceInfo.model;
      sistemaOperacional = this.platform.is('ios') ? 1 : 2;
      platformName = deviceInfo.platform;
      plataformVersion = deviceInfo.osVersion;
      const appInfo = await App.getInfo();
      versaoAplicativo = appInfo.version;
      const geo = await this.getGeolocation();
      latitude = geo.latitude;
      longitude = geo.longitude;
      pushNotificationDeviceId = '';
    }

    return {
      architectureInfo,
      deviceId,
      imei,
      latitude,
      longitude,
      model,
      origemAcesso: 1,
      plataformVersion,
      platformName,
      sistemaOperacional,
      pushNotificationDeviceId,
      versaoInstalada,
      versaoConhecida,
      versaoAplicativo,
      novoOnboard: true,
      idApp: environment.idAplicativo
    };
  }

  async getGeolocation() {
    try {
      const coordinates = await Geolocation.getCurrentPosition();
      return { latitude: coordinates.coords.latitude, longitude: coordinates.coords.longitude };
    } catch (e) { // -15.784330, -47.886546 brasilia
      return { latitude: -15.784330, longitude: -47.886546 };
    }
  }

  getToken() {
    return this.storageService.getToken();
  }

  getUser(): Usuario {
    const user = this.storageService.getUser();
    this.usuario$.next(user);
    return user;
  }

  limparUsuario() {
    this.storageService.clearUser();
  }

  clearToken() {
    return this.storageService.clearToken();
  }

  public setCredenciais(credenciais: any[]) {
    const usuario: Usuario = this.getUser();
    const cartoes = [];
    for (const item of credenciais) {
      const cartao = item.credencial;
      cartao.credencialMascarada = cartao.credencialMascarada.replaceAll('X', '●').replaceAll('-', ' ');
      item.listaContaPagamentos.sort((a: any, b: any) => a.ordermApresentacao - b.ordermApresentacao);
      cartao.contas = item.listaContaPagamentos.map((x: any) => {
        x.digitoVerificador = calcularDigitoVerificadorConta(x.idConta);
        x.agencia = x.idInstituicao;
        return x;
      });
      cartoes.push(cartao);
    }
    usuario.credenciais = cartoes;
    this.storageService.setUser(usuario);
  }

  buscarDados() {
    return this.httpClient.get(
      resolve('valloo://dadosUsuario', {idProgramaFidelidade: environment.idProgramaFidelidade})
    ).pipe(
      mergeMap((usuario: any) => {
        this.storageService.setUser(usuario);
        return this.credencialService.buscarCredenciais().pipe(
          tap((u: any) => this.setCredenciais(u)),
          concatMap(() => {
            return this.buscarFuncionalidades();
          })
        );
      })
    );
  }

  setBiometricActive(biometric: any = null) {
    if (!biometric) {
      biometric = {
        first: true,
        active: false
      };
    }
    this.storageService.setBiometricActive(biometric);
  }

  getBiometricActive() {
    return this.storageService.getBiometricActive();
  }

  buscarPermissoes(idConta: number) {
    return this.httpClient.get<any>(
      resolve('valloo://permissoesPortador', { idConta, nomeApp: environment.appName })
    ).pipe(
      pluck('authorities'),
      tap((x: any[]) => this.storageService.setProdutosPermissoes(x))
    );
  }

  getUsuarioAsync() {
    this.usuario$.next(this.getUser());
    return this.usuario$.asObservable();
  }

  buscarFuncionalidades() {
    const usuario: Usuario = this.getUser();
    let idsConta: any[] = [];
    for (const credencial of usuario.credenciais) {
      idsConta = idsConta.concat(credencial.contas.map(x => x.idConta));
    }
    return this.httpClient
      .post<Servicos[]>(resolve('valloo://funcionalidades', { idApp: environment.idAplicativo }), { contas: idsConta })
      .pipe(
        map((d: any) => {
          d.dados;
          return d.dados.map((conta: any) => ({
            ...conta,
            temNotaFiscal: conta.funcionalidades.some((f: any) => f.nomeServico === 'nota_fiscal')
          }));
        }),
        tap((x: Servicos[]) => this.storageService.setProdutosPermissoes(x))
      );
  }

  setPinCredencial(pinCredencial: string) {
    this.pinCredencial = pinCredencial;
  }

  getPinCredencial() {
    return this.pinCredencial;
  }

  buscarDadosUsuario() {
    return this.httpClient.get(
      resolve('valloo://dadosUsuario', { idProgramaFidelidade: environment.idProgramaFidelidade })
    );
  }

  buscarNotificacoes() {
    const url = resolve('notificacoes');
    return this.httpClient.get(`${url}/mensagens`).pipe(
      map((n: any) => n.filter((m: any) => m.blLido === false).length)
    );
  }

  buscarSaldoContas() {
    return this.credencialService.buscarCredenciais().pipe(
      tap((u: any) => this.setCredenciais(u))
    );
  }

  isLoggedIn(next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (this.storageService.getToken() && !this.storageService.getForcarOnboard()) {
      this.router.navigate(['/inicio'], { replaceUrl: true });
      return false;
    }
    return true;
  }

  isLoggedOut(next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    if (!this.storageService.getToken()) {
      this.router.navigate(['/login'], { replaceUrl: true });
      return false;
    }
    return true;
  }

  async habilitarReceberNotificacao(idLogin: number) {
    let deviceId = 'DESENVOLVIMENTO';
    if (this.platform.is('hybrid')) {
      const device = await Device.getId();
      deviceId = device.identifier;
    }
    const data: any = {
      idLogin,
      deviceId: deviceId.toUpperCase(),
      serviceDeviceId: this.storageService.getOneSignalId()
    };
    const url = resolve('valloo://habilitarReceberNotificacao');
    lastValueFrom(this.http.post(url, data));
  }
}
