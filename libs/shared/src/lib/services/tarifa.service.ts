import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { resolve } from '@utils/resolve.util';
import { TarifaModel } from '../models';
import { AbstractService } from './abstract.service';

@Injectable({
  providedIn: 'root'
})
export class TarifaService extends AbstractService<any> {

  public readonly CODE_TARIFA_CARTAO_CREDITO = 570;
  public readonly CODE_TARIFA_BOLETO_CARGA = 644;

  constructor(
    protected override http: HttpClient
  ) {
    super('valloo', 'tarifa', http);
  }

  listarTarifas(idConta: number): Observable<Array<TarifaModel>> {
    const url = resolve('valloo://buscarTarifas', { idConta });

    return this.http.get<{ perfilsTarifarios: any[] }>(url)
      .pipe(
        map(data => data.perfilsTarifarios.map(d => new TarifaModel(d)))
      );
  }

  getTarifaByCode(idConta: number, codigoTarifa: number): Observable<TarifaModel | undefined> {
    return this.listarTarifas(idConta).pipe(
      map(tarifas => tarifas.find(t => t.codTransacao === codigoTarifa))
    );
  }

  getTarifaBoletoCarga(idConta: number): Observable<TarifaModel | undefined> {
    return this.getTarifaByCode(idConta, this.CODE_TARIFA_BOLETO_CARGA);
  }

  getTarifaTransacaoCartaoCredito(idConta: number): Observable<TarifaModel | undefined> {
    return this.getTarifaByCode(idConta, this.CODE_TARIFA_CARTAO_CREDITO);
  }
}
