import { Injectable } from '@angular/core';
import { Extrato, StatusTedEnum } from '@corporativo/shared';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import {concatMap, map} from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { CapacitorHttp, HttpOptions } from '@capacitor/core';
import { fromPromise } from 'rxjs/internal/observable/innerFrom';
import { AuthService } from './auth.service';
import { resolve } from '@utils/resolve.util';
import { AbstractService } from './abstract.service';
import * as lodash from 'lodash';
import { TextUtil } from '@utils/text.util';
import {NotaFiscalService} from './nota-fiscal.service';

@Injectable({
  providedIn: 'root'
})
export class ExtratoService extends AbstractService<Extrato> {
  private transacoes: any[] = [];
  private transacoesFiltradas: any[] = [];
  private transacoes$ = new Subject<any[]>();

  constructor(
    protected override http: HttpClient,
    private authService: AuthService,
    private notaFiscalService: NotaFiscalService
  ) {
    super('valloo', 'extrato', http);
  }

  buscarExtrato(idConta: number, dataInicio: string, dataFim: string) {
    const relativeUrl = `data_inicial/${dataInicio}/data_final/${dataFim}`;
    return this.getAll(relativeUrl, {idConta}).pipe(
      map(extratos => {
          this.transacoes = extratos;
          this.transacoesFiltradas = [...extratos];
          return this.transformeToTransacoes(extratos);
        }
      )
    );
  }

  private transformeToTransacoes(extratos: any[]) {
    const transacoes: any[] = [];
    const functionCodesComprovantes = ['558', '670', '880', '884', '698', '558', '981', '172', '178', '180'];
    for (const extrato of extratos) {
      functionCodesComprovantes.includes(extrato.functionCode, 1);
      extrato.possuiComprovante = functionCodesComprovantes.includes(extrato.functionCode, 1);
      if (!transacoes.filter(x => x.dataFmt == extrato.dataTransacaoFmt).length) {
        transacoes.push({
          data: extrato.dataTransacao,
          dataFmt: extrato.dataTransacaoFmt
        });
      }
    }
    for (const transacao of transacoes) {
      transacao.operacoes = extratos.filter(x => x.dataTransacaoFmt == transacao.dataFmt);
    }
    return lodash.orderBy(transacoes, ['data'], 'desc');
  }

  filtrarTransacoes(valor: string) {
    if (!valor) {
      this.transacoesFiltradas = [...this.transacoes];
      this.transacoes$.next(this.transformeToTransacoes(this.transacoes));
      return;
    }
    const extratos = this.transacoesFiltradas.filter(transacao =>
      TextUtil.compareTextIndexOf(transacao.descTransacaoMinima, valor) ||
      TextUtil.compareTextIndexOf(transacao.descFunctionCode, valor) ||
      TextUtil.compareTextIndexOf(TextUtil.removeNotDigit(transacao.valorTransacao.toString()), TextUtil.removeNotDigit(valor)));
    this.transacoes$.next(this.transformeToTransacoes(extratos));
  }

  getTransacoesFiltradas() {
    return this.transacoes$.asObservable();
  }

  baixarExtrato(dadosExtrato: any) {
    const options: any = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      }),
      responseType: 'arraybuffer',
    }
    return this.http.post(resolve('valloo://downloadExtrato'), dadosExtrato, options);
  }

  buscarComprovante(transacao: any) {
    return this.http.post<any>(resolve('valloo://buscarComprovante'), transacao)
      .pipe(map((x) => {
          const mensagem: any = {}
          if (x.statusOperacao == StatusTedEnum.Reprovado) {
            mensagem.titulo = x.tituloStatusModal;
            mensagem.descricao = x.motivo;
            x.mensagem = mensagem;
          }
          if (x.statusOperacao == StatusTedEnum.Rejeitado || x.statusOperacao == StatusTedEnum.AguardandoAprovacao) {
            mensagem.titulo = x.tituloStatusModal;
            mensagem.descricao = x.descricaoStatus;
            x.mensagem = mensagem;
          }
          if (x.status == 404) {
            mensagem.titulo = x.tituloStatusModal;
            mensagem.descricao = x.descricaoStatus;
            x.mensagem = mensagem;
          }
          return x;
        })
      );
  }

  baixarExtratoNative(data: any): Observable<any> {
    const options: HttpOptions = this.getOptions();
    options.data = data;
    return fromPromise(CapacitorHttp.post(options)).pipe(map(this.extractDataNative));
  }

  private getOptions() {
    let url = resolve('valloo://downloadExtrato');

    return {
      url,
      headers: {
        'Content-Type': 'application/json',
        'AuthorizationPortador': 'Bearer ' + this.authService.getToken()
      },
      webFetchExtra: {
        credentials: 'include'
      },
      responseType: 'arraybuffer'
    } as HttpOptions;
  }

  headersNative() {
    return {'Content-Type': 'application/json'};
  }

  extractDataNative(res: any) {
    if (res.error) {
      throw 'Ocorreu um erro inesperado.';
    }
    let body: any = res;
    if ('resposta' in body) {
      body = body.resposta;
    }
    if ('data' in body) {
      body = body.data;
    }
    return body;
  }

  buscarExtratoComNotaFiscal(idConta: number, dataInicio: string, dataFim: string) {
    const relativeUrl = `data_inicial/${dataInicio}/data_final/${dataFim}`;
    return this.notaFiscalService.buscarPorPeriodo(idConta, dataInicio, dataFim).pipe(
      concatMap(notasFiscais => {
        return this.getAll(relativeUrl, {idConta}).pipe(
          map(extratos => {
              for (const extrato of extratos) {
                const notaFiscal = notasFiscais.find(x => x.idTranlog == extrato.idTranlog);
                if (notaFiscal) {
                  extrato.idNotaFiscal = notaFiscal.id;
                  extrato.notaFiscalSituacao = notaFiscal.idSituacao;
                }
              }
              this.transacoes = extratos;
              this.transacoesFiltradas = [...extratos];
              return this.transformeToTransacoes(extratos);
            }
          )
        );
      })
    );
  }
}
