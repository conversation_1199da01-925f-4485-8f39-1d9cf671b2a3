import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { resolve } from '@utils/resolve.util';
import { AbstractService, Conta } from '@corporativo/shared';

@Injectable({
  providedIn: 'root'
})
export class RecargaService extends AbstractService<any> {

  constructor(
    protected override http: HttpClient
  ) {
    super('valloo', 'recarga', http);
  }

  consultarOperadoras(ddd: string) {
    const url = resolve('recarga');
    return this.http.get<any>(`${url}/consultar-operadora-ddd/${ddd}?voucher=false`);
  }

  consultarValores(ddd: string, operadoraId: any) {
    const url = resolve('recarga');
    return this.http.get<any>(`${url}/consultar-valor-operadora/${ddd}/operadora/${operadoraId}`);
  }

  efetuarRecarga(ddd: string, conta: Conta, numero: string, operadoraSelecionada: any, recargaSelecionada: any) {
    const request = {
      ddd: Number(ddd),
      idConta: conta.idConta,
      numero: Number(numero),
      operadoraId: Number(operadoraSelecionada.operadoraId),
      valor: Number(recargaSelecionada.valor),
      operadora: operadoraSelecionada.nome
    };

    const url = resolve('recarga');
    return this.http.post(`${url}/efetuar-recarga?voucher=false`, request);
  }

}
