import { AbstractService, environment } from '@corporativo/shared';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Platform } from '@ionic/angular';
import { TextUtil } from '@utils/text.util';
import { resolve } from '@utils/resolve.util';
import { Device } from '@capacitor/device';
import { isEmpty } from '@utils/object.util';

@Injectable({
  providedIn: 'root',
})
export class SegurancaService extends AbstractService<any> {
  deviceInfo: any;
  idDevice: any;

  constructor(protected override http: HttpClient, private platform: Platform) {
    super('valloo', 'seguranca', http);
    this.deviceDispostivo();
  }

  async deviceDispostivo() {
    this.deviceInfo = await Device.getInfo();
    this.idDevice = await Device.getId();
  }

  redefinirSenhaLogin(isFluxoOCR: boolean, cpf: string, form: any, dados: any) {
    let architectureInfo = 'DESENVOLVIMENTO';
    let deviceId = 'DESENVOLVIMENTO';
    let model = 'DESENVOLVIMENTO';
    let platformName = 'DESENVOLVIMENTO';
    let plataformVersion = 'DESENVOLVIMENTO';

    if (this.platform.is('hybrid')) {
      architectureInfo = this.deviceInfo.manufacturer;
      deviceId = this.idDevice.identifier;
      model = this.deviceInfo.model;
      platformName = this.deviceInfo.platform;
      plataformVersion = this.deviceInfo.osVersion;
    }

    const request: any = {
      isFluxoOCR: isFluxoOCR,
      cpf: cpf,
      cpfRepresentante: dados.documentoRepresentante
        ? dados.documentoRepresentante
        : null,
      novaSenha: form.value.novaSenha,
      idProcessadora: environment.idProcessadora,
      idInstituicao: environment.idInstituicao,
      token: form.value.token,
      celular: TextUtil.removeNotDigit(String(dados.telefone)),
      grupoAcesso: dados.documentoRepresentante
        ? environment.idGrupoAcessoPj
        : environment.idGrupoAcesso,
      tipoLogin: dados.documentoRepresentante
        ? environment.tipoLoginPj
        : environment.tipoLoginPf,
      novoOnboard: true,
      architectureInfo: architectureInfo,
      deviceId: deviceId,
      model: model,
      sistemaOperacional: this.platform.is('ios') ? 1 : 2,
      platformName: plataformVersion,
      plataformVersion: platformName,
    };

    const url = resolve('valloo://redefinirSenha');
    return this.http.put(url, request);
  }

  recadastrarSenha(form: any, documento: string, documentoAcesso: any) {
    const request = {
      cpf: documentoAcesso != null ? documentoAcesso : documento,
      cnpj: documentoAcesso != null ? documento : null,
      idInstituicao: environment.idInstituicao,
      idProcessadora: environment.idProcessadora,
      senha: form.senhaAtual,
      novaSenha: form.novaSenha,
      grupoAcesso: !isEmpty(documentoAcesso)
        ? environment.idGrupoAcessoPj
        : environment.idGrupoAcesso,
      tipoLogin: !isEmpty(documentoAcesso)
        ? environment.tipoLoginPj
        : environment.tipoLoginPf,
    };
    const url = resolve('portadorLogin');
    return this.http.post(`${url}/recadastrar-senha`, request);
  }

  trocarEmail(email: string, documento: string) {
    const request = {
      email,
      documento: TextUtil.removeNotDigit(documento),
      idInstituicao: environment.idInstituicao,
      idProcessadora: environment.idProcessadora,
    };
    const url = resolve('valloo://portadorLogin');
    return this.http.put(`${url}/trocar-email`, request);
  }

  buscarEmail(documento: string) {
    const idInstituicao = environment.idInstituicao;
    const idProcessadora = environment.idProcessadora;
    const url = resolve('valloo://buscarEmail', {
      idProcessadora,
      idInstituicao,
      documento,
    });
    return this.http.get<any>(url);
  }

  validarOnboardingCaf(request: any) {
    const url = resolve('valloo://validarCadastroOnboarding');
    return this.http.post(url, request)
  }
}
