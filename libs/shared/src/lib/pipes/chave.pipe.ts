import { Pipe, PipeTransform } from '@angular/core';
import { validarCelular, validarCNPJ, validarCPF, validarEmail, validarUUID } from '@utils/validators.util';
import { MaskHideCpfCnpjPipe } from './mask-hide-cpf-cnpj.pipe';

/*
 * Retornar a primeira palavra de uma frase
 * Usage:
 *   value | chave
 * Example:
 *   {{ 00000000000 | chave }}
 *   formats to: 000.000.000-00
*/
@Pipe({
  name: 'chave',
  standalone: false
})
export class ChavePipe implements PipeTransform {
  transform(value: string, ...args: any): string {
    if (!value) {
      return value;
    }

    return this.formatarChave(value);
  }

  formatarChave(valor: string) {
    if (validarEmail(valor)) {
      return valor;
    }
    if (validarCelular(valor)) {
      return valor;
    }
    if (validarCNPJ(valor)) {
      const cpfPipe = new MaskHideCpfCnpjPipe();
      return cpfPipe.transform(valor);
    }
    if (validarCPF(valor)) {
      const cpfPipe = new MaskHideCpfCnpjPipe();
      return cpfPipe.transform(valor);
    }
    if (validarUUID(valor)) {
      return valor;
    }
    return valor;
  }
}
