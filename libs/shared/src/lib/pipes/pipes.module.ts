import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IniciaisPipe } from './iniciais.pipe';
import { MaskHideCpfCnpjPipe } from './mask-hide-cpf-cnpj.pipe';
import { FirstNamePipe } from './first-name.pipe';
import { CurrencyBrPipe } from './currency-br.pipe';
import { ChavePipe } from './chave.pipe';
import { PieceOfPipe } from './piece-of.pipe';

const PIPES = [
  FirstNamePipe,
  IniciaisPipe,
  MaskHideCpfCnpjPipe,
  CurrencyBrPipe,
  ChavePipe,
  PieceOfPipe
]

@NgModule({
  imports: [CommonModule],
  declarations: [
    PIPES
  ],
  exports: [
    PIPES
  ]
})
export class PipesModule {
}
