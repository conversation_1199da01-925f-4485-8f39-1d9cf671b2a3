import { Pipe, PipeTransform } from '@angular/core';
/*
 * Retornar as iniciais das palavras de uma frase ou nome
 * Usage:
 *   value | iniciais
 * Example:
 *   {{ <PERSON> | iniciais }}
 *   formats to: JS
 * Usage:
 *   value | iniciais:'first'
 * Example:
 *   {{ <PERSON> | iniciais:'first' }}
 *   formats to: J
*/
@Pipe({
  name: 'iniciais',
  standalone: false
})
export class IniciaisPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    if (!value) {
      return;
    }
    const positions = value.split(' ');
    if (positions.length == 1 || args == 'first') {
      return positions[0].charAt(0);
    }
    return positions[0].charAt(0) + positions.pop().charAt(0);
  }
}
