import { Pipe, PipeTransform } from '@angular/core';
import { CurrencyPipe } from '@angular/common';

@Pipe({
  name: 'currencyBr',
  standalone: false
})

export class CurrencyBrPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    if (!value && !args) {
      return '0,00';
    }
    if (!value && args == 'simbol') {
      return 'R$ 0,00';
    }
    const currencyPipe = new CurrencyPipe('pt-BR');
    if (args == 'simbol') {
      return currencyPipe.transform(0, 'BRL');
    }
    const valor = currencyPipe.transform(value, 'BRL') || '';
    return valor.replace('R$', '');
  }
}
