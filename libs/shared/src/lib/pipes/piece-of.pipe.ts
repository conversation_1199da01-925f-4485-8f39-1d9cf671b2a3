import { Pipe, PipeTransform } from '@angular/core';

/*
 * Retorna tudo após a parte informada
 * Usage:
 *   value | pieceOf:'remover'
 * Example:
 *   {{ Esta é uma string para remover o resto. | pieceOf }}
 *   formats to: Esta é uma string para remover
*/
@Pipe({
  name: 'pieceOf',
  standalone: false
})
export class PieceOfPipe implements PipeTransform {
  transform(value: string, part: string): string {
    if (!value) {
      return value;
    }
    const regex = new RegExp(`(${part}).*`, 'i');
    return value.replace(regex, '');
  }
}
