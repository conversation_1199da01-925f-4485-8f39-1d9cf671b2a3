import { Pipe, PipeTransform } from '@angular/core';
/*
 * Formatar o CPF ou CNPJ
 * Usage:
 *   value | maskCpfCnpj
 * Example:
 *   {{ 00000000000 | maskCpfCnpj }}
 *   format to: 000.000.000-00
*/
@Pipe({
  name: 'maskCpfCnpj'
})
export class MaskCpfCnpjPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    if (!value) {
      return;
    }
    if (value.length <= 11) {
      return value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, '\***.\$2.\$3\-**');
    } else {
      return value.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g, '\**.\$2.\$3\/\$4\***');
    }
  }
}
