import { Pipe, PipeTransform } from '@angular/core';
/*
 * <PERSON>tornar a primeira palavra de uma frase
 * Usage:
 *   value | firstName
 * Example:
 *   {{ <PERSON> | firstName }}
 *   formats to: João
*/
@Pipe({
  name: 'firstName',
  standalone: false
})
export class FirstNamePipe implements PipeTransform {
  transform(value: string, ...args: any ): string {
    if (!value) {
      return value;
    }

    return value.split(' ')[0];
  }
}
