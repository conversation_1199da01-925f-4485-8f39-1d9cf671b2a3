// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { AppEnvironment } from '../interfaces';

export const environment: AppEnvironment = {
  production: false,
  app: '',
  appId: '',
  appName: '',
  env: '',
  default: '',
  idInstituicao: 0,
  idProcessadora: 0,
  idProgramaFidelidade: 0,
  idProdutoInstituicao: 0,
  isbp: 0,
  tokenCaf: '',
  tokenFaceLiveness: '',
  tokenCafBeta: '',
  idGrupoAcesso: 0,
  idGrupoAcessoPj: 0,
  idAplicativo: 0,
  urlMotiva: '',
  onesignalAppId: '',
  buttonType: 'rounded',
  buttonLayout: 'grid',
  homeVersion: 'v1',
  showChat: false,
  showIcon: false,
  showTitleDots: false,
  tituloAjuda: '',
  tipoLoginPj: '',
  tipoLoginPf: '',
  urlApple: '',
  corFuncionalidades: false,
  telefoneContato: ''
};


/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
