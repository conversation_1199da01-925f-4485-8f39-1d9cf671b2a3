import _ from 'lodash';
import { AppEnvironment } from '../interfaces';

export const environment: AppEnvironment = {
  production: false,
  appId: 'br.com.valloo.corporativo',
  app: 'valloo-v2',
  appName: 'Valloo corporativo',
  env: 'commom',
  default: 'valloo',
  idInstituicao: 2001,
  idProcessadora: 10,
  idProgramaFidelidade: 2,
  idProdutoInstituicao: 200101,
  isbp: 0,
  tokenCaf: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenFaceLiveness: 'eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI2NTU2NWZkMjNmMjA3ZjAwMDgwNmMyMmIifQ.L5oTCAAAho3IuThBlNpsD7da238zOh-TaXwPc6IxVmU',
  tokenCafBeta: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2NTljNzMxYWUyMzY2YzAwMDhiOWIzM2UifQ.QoddeSSVsQrm1DeYyCnhW7Wny928_EtiagHPBchsIuo',
  idGrupoAcesso: 0,
  idAplicativo: 26,
  idGrupoAcessoPj: 0,
  urlMotiva: 'https://motiva.itspay-hom.com.br',
  onesignalAppId: '',
  hosts: {
    valloo: {
      host: '',
      protocol: 'https',
      port: '',
      root: '/api/api',
      endpoints: {
        api: '',
        auth: 'corporativo/auth',
        logout: 'auth/logout',
        dadosUsuario: 'loyalty/dados-principais/:idProgramaFidelidade',
        credencial: 'portador/credencial',
        permissoesPortador: 'gerenciador/aplicativo/permissions-portador/:idConta/:nomeApp',
        rotator: 'loyalty/rotator',
        extrato: 'portador/conta/:idConta/extrato',
        downloadExtrato: 'download/extrato',
        buscarComprovante: 'comprovante-pagamento/consultar',
        portador: 'portador',
        pix: 'pix',
        portadorPix: 'portador/pix',
        buscarSaldo: 'portador/conta/buscar-saldo-conta/:idConta',
        tokenFuncionalidade: 'token-funcionalidade',
        validarCadastro: 'portador/login/validar-cadastro-onboard',
        validarCaf: 'antifraude/validar-ocr/caf',
        portadorLogin: 'portador/login',
        registroFacial: 'antifraude/caf/registrar-validacao-facial/:idInstituicao/:documento/:idObjetivo',
        redefinirSenha: 'portador/login/redefinir-senha',
        antifraude: 'antifraude',
        enviarTokenSenha: 'token-redefinicao-senha/enviar/token-redefinir-senha',
        funcionalidades: 'gerenciador/aplicativo/buscar-servicos-habilitados/:idApp',
        diretrizes: 'diretrizes/:idAplicativo/:tipo',
        verificarVersao: 'versao-app/verifica-versao',
        tranferirMesmoBolso: 'portador/conta/v2/transferencia/mesmo-bolso',
        plastico: 'plastico/abrir/mobile/:idPlastico',
        credencialDetalhe: 'portador/credencial/:idCredencial/detalhes',
        validarEtapasCredencial: 'resgate-pontos/validar-etapas-credencial',
        desbloqueio: 'portador/desbloqueio/solicitar-desbloqueio-cartao',
        salvarSenha: 'resgate-pontos/mobile/salvarCredencial',
        criarCartaoVirtual: 'gerador/credencial',
        campanha: 'campanha',
        gerarTokenMotiva: 'token-funcionalidade/gerar-token',
        buscarTarifa: 'perfiltarifario/:idPerfil/codTransacao/:codTransacao',
        gerarCartaoVirtual: 'gerador/credencial',
        perfilTarifario: 'perfiltarifario/conta/:idConta',
        verificarQrcode: 'elo/qrcode/parse',
        pagarQrcode: 'elo/qrcode/send-transaction',
        endereco: 'endereco',
        cep: 'logradouro/card-holder/:cep',
        faleConosco: 'instituicao/get/instituicao',
        saqueFgts: 'fgts/registro-mensagem',
        parametro: 'parametro',
        prontoPaguei: 'pronto-paguei',
        limites: 'limites',
        pagamento: 'gateway-pagamento-externo',
        voucher: 'pedido-produto-giftty',
        recarga: 'gateway-pagamento-externo/recarga',
        banco: 'banco',
        ted: 'ted/efetuar',
        limite: 'limites/verificar-limite',
        notificacoes: 'aplicativo-mensagem',
        migracao: 'migracao',
        extratoMigracao: 'portador/conta/:idConta/extrato',
        consultarComprovanteMigracao: 'migracao/consultar-comprovante',
        downloadExtratoMigracao: 'migracao/download-extrato',
        cancelarCredenciaisMigradas: 'migracao/alterar-status/credenciais',
        buscarTarifas: 'portador/conta/buscar-tarifas/conta/:idConta',
        buscarEmail: 'portador/login/:idProcessadora/:idInstituicao/buscar-email/:documento',
        enviarBoletoEmail: 'boleto/carga/gerar-cobranca-boleto/portador/enviar-boleto-email',
        gerarLinhaDigitavel: 'boleto/carga/gerar-cobranca-boleto/portador',
        gerarLinhaDigitavelFatura: 'portador/fatura/linha/:idConta/:anoMes',
        baixarFaturaPDF: 'portador/fatura/download/:idConta/:anoMes',
        habilitarReceberNotificacao: 'portador-dispositivo/habilitar-receber-notificacao',
        google: 'google-maps-api',
        dadosConsumo: 'portador/conta/buscar-dados-consumo/:idConta',
        validarCadastroOnboarding: 'corporativo/validar-cadastro-onboard',
        enviarTokenSenhaCorporativo: 'corporativo/enviar-token',
        corporativo: 'corporativo',
        notaFiscal: 'notas-fiscais',
      }
    }
  },
  buttonType: 'rounded',
  buttonLayout: 'carousel',
  homeVersion: 'v1',
  showChat: true,
  showIcon: true,
  showTitleDots: true,
  tituloAjuda: 'Ajuda',
  tipoLoginPj: 'VALLOO_CORPORATIVO',
  tipoLoginPf: 'VALLOO_CORPORATIVO',
  urlApple: '',
  corFuncionalidades: false,
  telefoneContato: ''
};
export const merge = (...env: AppEnvironment[]): AppEnvironment => _.merge({}, environment, ...env);
