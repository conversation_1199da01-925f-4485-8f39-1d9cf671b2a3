export enum TipoTransacaoEnum {
  Boleto = 1,
  <PERSON> = 2,
  <PERSON>x = 3,
  QrcodeElo = 4,
  VcnElo = 5,
  Shopping = 6,
  Recarga = 7,
  Lancamento = 8,
  Pontos = 9,
  LoginIssuer = 10,
  LoginPortador = 11,
  TrocarSenhaIssuer = 12,
  TrocarSenhaPortador = 13,
  TrocarSenhaCartao = 14,
  ReceberSenhaCartao = 15,
  TrocarEmail = 16,
  TrocarCelular = 17,
  TrocarEndereco = 18,
  CriarContaIssuer = 19,
  ValidarCriarOcr = 20,
  CriarContaPortador = 21,
  CriarPortadorLogin = 22,
  ExclusaoContaPortador = 23,
  InserirPedidoGiftty = 24,
  ResgatarUtilizarVoucher = 25,
  TransferenciaInternaBackoffice = 26,
  TransferenciaInternaPortador = 27
}
