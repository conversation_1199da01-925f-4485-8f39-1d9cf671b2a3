import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BrMaskDirective } from './br-mask.directive';
import { DynamicClassDirective } from './dynamic-class.directive';

@NgModule({
  imports: [CommonModule],
  declarations: [
    BrMaskDirective,
    DynamicClassDirective
  ],
  exports: [
    BrMaskDirective,
    DynamicClassDirective
  ]
})
export class DirectivesModule {
}
