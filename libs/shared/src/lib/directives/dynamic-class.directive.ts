import { Directive, ElementRef, Renderer2, OnInit } from '@angular/core';
import { environment } from '@corporativo/shared';

@Directive({
  selector: '[appDynamicClass]',
  standalone: false
})
export class DynamicClassDirective implements OnInit {

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    const dynamicClasses = this.getDynamicClasses();
    dynamicClasses.forEach(dynamicClass => {
      this.renderer.addClass(this.el.nativeElement, dynamicClass);
    });
  }

  private getDynamicClasses(): string[] {
    const layoutClass = environment.buttonLayout === 'carousel' ? 'carousel' : 'buttons-grid';
    switch (environment.buttonType) {
      case 'rounded':
        return ['button-rounded', layoutClass];
      case 'squared':
        return ['button-squared', layoutClass];
      default:
        return ['button-rounded', layoutClass];
    }
  }
}
