import { environment } from '@corporativo/shared';
import { TextUtil } from '@utils/text.util';

export class CadastroLoginRequest {

  cpf: string | undefined;
  cnpj: string | undefined;
  acessoConta: string | undefined;
  credencial: string | undefined;
  dataNascimento: string | undefined;
  dataFundacao: string | undefined;
  dataValidadeCredencial: string | undefined;
  email: string | undefined;
  senha: string | undefined;
  idTipoConta: number | undefined;
  isEsqueciSenha = false;
  origemCadastroLogin = 1;
  idInstituicao: number = environment.idInstituicao;
  idProcessadora: number = environment.idProcessadora;
  architectureInfo: string | undefined;
  deviceId: string | undefined;
  model: string | undefined;
  platformName: string | undefined;
  plataformVersion: string | undefined;
  tipoLogin: string | undefined;
  grupoAcesso: null | number | undefined;
  documento: string | undefined;
  documentoRepresentante: string | undefined;

  constructor(data?: any) {
    if(data){
      this.cpf = TextUtil.removeNotDigit(data.cpf);
      this.cnpj = TextUtil.removeNotDigit(data.cnpj);
      this.acessoConta = TextUtil.removeNotDigit(data.acessoConta);
      this.credencial = data.credencial;
      this.dataNascimento = data.dataNascimento;
      this.dataFundacao = data.dataFundacao;
      this.email = data.email;
      this.senha = data.senha;
      this.idTipoConta = data.idTipoConta;
      this.dataValidadeCredencial = data.dataValidadeCredencial;
      this.architectureInfo = data.architectureInfo;
      this.deviceId = data.deviceId;
      this.model = data.model;
      this.platformName = data.platformName;
      this.plataformVersion = data.plataformVersion;
      if (this.cnpj && this.cnpj.length > 11) {
        // go horse
        this.cpf = '';
        this.dataNascimento = this.dataFundacao;
      }
      this.grupoAcesso = data.grupoAcesso;
    }
  }
}
