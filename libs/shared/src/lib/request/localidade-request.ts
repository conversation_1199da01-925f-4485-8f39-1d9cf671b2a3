import { Uf } from './uf-request';

export class Localidade {
  codLocalidade: number | undefined;
  nome: string | undefined;
  cep: string | undefined;
  codIbge: number | undefined;
  codIbgeReduzido: number | undefined;
  uf: Uf | null | undefined;

  constructor(data?: any) {
    if (data) {
      this.codLocalidade = data.codLocalidade;
      this.nome = data.nome;
      this.cep = data.cep;
      this.codIbge = data.codIbge;
      this.codIbgeReduzido = data.codIbgeReduzido;
      this.uf = data.uf ? new Uf(data.uf) : null;
    }
  }
}
