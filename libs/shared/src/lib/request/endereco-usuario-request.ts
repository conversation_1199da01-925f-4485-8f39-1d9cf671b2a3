export class EnderecoUsuario {
  cep: string | undefined;
  logradouro: number | undefined;
  numero: number | undefined;
  complemento: number | undefined;
  bairro: number | undefined;
  cidade: number | undefined;
  uf: number | undefined;

  constructor(data?: any) {
    if (data) {
      this.cep = data.cep;
      this.logradouro = data.logradouro;
      this.numero = data.numero;
      this.complemento = data.complemento;
      this.bairro = data.bairro;
      this.cidade = data.cidade;
      this.uf = data.uf;
    }
  }
}
