import { <PERSON>rro } from './bairro-request';

export class Endereco {
  nome = '';
  nomeAbreviado = '';
  codLogradouro = '';
  infoAdicional = '';
  cep = '';
  bairroInicio: Bairro | null = null;

  constructor(data?: any) {
    if (data) {
      this.nome = data.nome;
      this.nomeAbreviado = data.nomeAbreviado;
      this.codLogradouro = data.codLogradouro;
      this.infoAdicional = data.infoAdicional;
      this.cep = data.cep;
      this.bairroInicio = data.bairroInicio ? new Bairro(data.bairroInicio) : null;
    }
  }
}
