import { EnderecoUsuario } from './endereco-usuario-request';
import { TextUtil } from '@utils/text.util';
import { environment } from '@corporativo/shared';

export class AtualizarDadosRequest {
  dataNascimento: string | undefined;
  dddTelefoneCelular: string | undefined;
  dddTelefoneResidencial: string | undefined;
  documento: string | undefined;
  rg: string | undefined;
  email: string | undefined;
  nomeCompleto: string | undefined;
  telefoneCelular: string | undefined;
  telefoneResidencial: string | undefined;
  origemConfirmacao: string | undefined;
  idSexo: number | undefined;
  enderecoResidencial: EnderecoUsuario | null | undefined;
  idInstituicao: number = environment.idInstituicao;
  idParceiroPrograma: number = environment.idProgramaFidelidade;
  idProcessadora: number = environment.idProcessadora;
  idProdutoInstituicao: number = environment.idProdutoInstituicao;
  origemAcesso: number = 0;
  estadoCivil = 0;
  nacionalidade = 'Brasileiro';
  nomePai: string | undefined;
  nomeMae: string | undefined;

  constructor(data?: any) {
    if (data) {
      this.dataNascimento = data.dataNascimento;
      this.dddTelefoneCelular = data.dddTelefoneCelular;
      this.dddTelefoneResidencial = data.dddTelefoneResidencial;
      this.documento = TextUtil.removeNotDigit(data.documento);
      this.email = data.email;
      this.nomeCompleto = data.nomeCompleto;
      this.telefoneCelular = data.telefoneCelular;
      this.telefoneResidencial = data.telefoneResidencial;
      this.idSexo = data.idSexo;
      this.enderecoResidencial = data.enderecoResidencial ? new EnderecoUsuario(data.enderecoResidencial) : null;
    }
  }
}
