import { inject, Injectable } from '@angular/core';
import { HttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerFn, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { AuthService } from '../services';
import { inject as injectApp } from '@utils/inject.util';
import { toast } from '@utils/toast.util';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  constructor(
    private authService: AuthService,
    private router: Router,
    private modalController: ModalController
  ) {
  }

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    if (skipTokenByUrl(request.url)) {
      return next.handle(request);
    }
    let token = this.authService.getToken() || '';
    if (!token) {
      return next.handle(request);
    }
    const req = request.clone({
      setHeaders: { AuthorizationCorporativo: token },
      withCredentials: true
    });

    return next.handle(req).pipe(
      catchError((err) => {
        console.log('erro interceptor', err);
        if (err.status === 401) {
          this.modalController.dismiss();
          this.authService.clearToken();
          // toast('Sessão expirada, por favor, realize login novamente.', 'dark');
          this.router.navigate(['/login']);
        }
        return throwError(err);
      })
    );
  }
}

export function skipTokenByUrl(urlSend: string) {
  const byPassUrls = [
    'corporativo/auth',
    'antifraude/caf/registrar-validacao-facial',
    'antifraude/validar-ocr/caf',
    'portador/login/validar-cadastro-onboard',
    'versao-app/verifica-versao',
    'loyalty/rotator/buscar-elementos',
    'loyalty/rotator/buscar-imagem-elemento',
    'token-acesso/enviar/token-cadastro-login',
    'token-redefinicao-senha/enviar/token-redefinir-senha',
    'portador/login/redefinir-senha',
    'portador/login/auth',
    'portador/login/descobre-tipo-login',
    'portador/login/encontra-caf-necessario/instituicao',
    'versao-app/verifica-versao',
  ];
  return !!byPassUrls.find(x => urlSend.indexOf(x) !== -1);
}

export function loggingInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<any> {
  if (skipTokenByUrl(req.url)) {
    return next(req);
  }
  const authService = inject(AuthService);
  const token = authService.getToken();
  if (!token) {
    return next(req);
  }
  const reqWithHeader = req.clone({
    headers: req.headers.set('AuthorizationCorporativo', token),
    withCredentials: true
  });
  return next(reqWithHeader).pipe(
    catchError(async (err) => {
      console.log('erro interceptor', err);
      if (err.status === 401) {
        const authService: AuthService = await injectApp(AuthService);
        toast('Sessão expirada, por favor, realize login novamente.', 'dark');
        authService.clearToken();
        const router: Router = await injectApp(Router);
        router.navigate(['/login']);
      }
      return throwError(err);
    })
  );
}
