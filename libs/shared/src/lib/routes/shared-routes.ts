import { Routes } from '@angular/router';
import { versaoGuard } from '../guards/versao.guard';
import { onboardGuard } from '../guards/onboard.guard';
import { posAuthGuard } from '../guards/pos-auth.guard';
import { authGuard } from '../guards/auth.guard';

export const sharedRoutes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('apps/valloo/src/app/pages/login/login.module').then(
        (m) => m.LoginPageModule
      ),
    canActivate: [versaoGuard, onboardGuard, posAuthGuard],
  },
  {
    path: 'login',
    loadChildren: () =>
      import('apps/valloo/src/app/pages/login/login.module').then(
        (m) => m.LoginPageModule
      ),
    canActivate: [versaoGuard, onboardGuard, posAuthGuard],
  },
  // {
  //   path: 'tabs',
  //   loadChildren: () => import('apps/multiconta/src/app/pages/tabs/tabs.module').then(m => m.TabsPageModule)
  // },
  {
    path: 'inicio',
    loadChildren: () =>
      import('apps/valloo/src/app/pages/inicio/inicio.module').then(
        (m) => m.InicioPageModule
      ),
    canActivate: [authGuard],
  },
  {
    path: 'criar-conta',
    loadChildren: () => import('apps/valloo/src/app/pages/criar-conta/criar-conta.module').then((m) => m.CriarContaModule),
    canActivate: [versaoGuard, onboardGuard, posAuthGuard],
  },
  {
    path: 'selfie-caf',
    loadChildren: () => import('apps/valloo/src/app/pages/selfie-caf/selfie-caf.module').then((m) => m.SelfieCafPageModule)
  },
  {
    path: 'cadastrar-senha',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/cadastrar-senha/cadastrar-senha.module').then((m) => m.CadastrarSenhaPageModule)
  },
  {
    path: 'cadastro-sucesso',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/cadastro-sucesso/cadastro-sucesso.module').then((m) => m.CadastroSucessoPageModule)
  },
  {
    path: 'esqueci-senha',
    loadChildren: () => import('apps/valloo/src/app/pages/esqueci-senha/esqueci-senha.module').then((m) => m.EsqueciSenhaModule)
  },
  {
    path: 'termos-uso',
    loadChildren: () => import('apps/valloo/src/app/pages/termos-uso/termos-uso.module').then((m) => m.TermosUsoPageModule)
  },
  {
    path: 'politica-privacidade',
    loadChildren: () => import('apps/valloo/src/app/pages/politica-privacidade/politica-privacidade.module').then((m) => m.PoliticaPrivacidadePageModule)
  },
  {
    path: 'informacoes-aplicativo',
    loadChildren: () => import('apps/valloo/src/app/pages/informacoes-aplicativo/informacoes-aplicativo.module').then((m) => m.InformacoesAplicativoPageModule)
  },
  {
    path: 'extrato/:idConta',
    loadChildren: () => import('apps/valloo/src/app/pages/extrato/extrato.module').then((m) => m.ExtratoPageModule)
  },
  {
    path: 'pix/:idConta',
    loadChildren: () => import('@corporativo/pix').then(m => m.PixModule)
  },
  {
    path: 'nota-fiscal/:idConta',
    loadChildren: () => import('apps/valloo/src/app/pages/nota-fiscal/nota-fiscal.module').then(m => m.NotaFiscalPageModule)
  },
  {
    path: 'cartoes/:idConta',
    loadChildren: () => import('apps/valloo/src/app/pages/cartoes/cartoes.module').then(m => m.CartoesPageModule)
  },
  {
    path: 'selecionar-tipo-documento',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/selecionar-tipo-documento/selecionar-tipo-documento.module').then(m => m.SelecionarTipoDocumentoPageModule),
    canActivate: [versaoGuard]
  },
  {
    path: 'validar-documento',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/validar-documento/validar-documento.module').then(m => m.ValidarDocumentoPageModule),
    canActivate: [versaoGuard]
  },
  {
    path: 'erro-validacao',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/erro-validacao/erro-validacao.module').then(m => m.ErroValidacaoPageModule),
    canActivate: [versaoGuard]
  },
  {
    path: 'erro-validacao-caf',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/erro-validacao-caf/erro-validacao-caf.module').then(m => m.ErroValidacaoCafPageModule),
    canActivate: [versaoGuard]
  },
  {
    path: 'validar-caf-loading',
    loadChildren: () => import('apps/valloo/src/app/pages/onboarding/validar-caf-loading/validar-caf-loading.module').then(m => m.ValidarCafLoadingPageModule),
    canActivate: [versaoGuard]
  },
  // {
  //   path: 'pos-splash',
  //   loadChildren: () => import('apps/valloo/src/app/pages/pos-splash/pos-splash.module').then(m => m.PosSplashPageModule)
  // },
  // {
  //   path: 'extrato/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/extrato/extrato.module').then(m => m.ExtratoPageModule)
  // },
  // {
  //   path: 'extrato',
  //   loadChildren: () => import('apps/valloo/src/app/pages/extrato/extrato.module').then(m => m.ExtratoPageModule)
  // },
  // {
  //   path: 'funcionalidades',
  //   loadChildren: () => import('apps/valloo/src/app/pages/funcionalidades/funcionalidades.module').then(m => m.FuncionalidadesPageModule)
  // },
  // {
  //   path: 'criar-conta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/criar-conta/criar-conta.module').then(m => m.CriarContaModule)
  // },
  // {
  //   path: 'selecao-tipo-documento',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/selecao-tipo-documento/selecao-tipo-documento.module').then(m => m.SelecaoTipoDocumentoModule)
  // },
  // {
  //   path: 'selecionar-tipo-pessoa',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/selecionar-tipo-pessoa/selecionar-tipo-pessoa.module').then(m => m.SelecionarTipoPessoaModule),
  //   canActivate: [versaoGuard]
  // },
  // {
  //   path: 'validacao-documento-selfie',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/validacao-documento-selfie/validacao-documento-selfie.module').then(m => m.ValidacaoDocumentoSelfieModule)
  // },
  // {
  //   path: 'cadastrar-senha',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/cadastrar-senha/cadastrar-senha.module').then(m => m.CadastrarSenhaModule)
  // },
  // {
  //   path: 'validacao-caf-loading',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/validacao-caf-loading/validacao-caf-loading.module').then(m => m.ValidacaoCafLoadingModule)
  // },
  // {
  //   path: 'erro-validacao',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/erro-validacao/erro-validacao.module').then(m => m.ErroValidacaoModule)
  // },
  // {
  //   path: 'informacoes-app',
  //   loadChildren: () => import('apps/valloo/src/app/pages/informacoes-aplicativo/informacoes-aplicativo.module').then(m => m.InformacoesAplicativoModule)
  // },
  // {
  //   path: 'politicas-privacidade',
  //   loadChildren: () => import('apps/valloo/src/app/pages/politica-privacidade/politica-privacidade.module').then(m => m.PoliticaPrivacidadeModule)
  // },
  // {
  //   path: 'termos-uso',
  //   loadChildren: () => import('apps/valloo/src/app/pages/termos-uso/termos-uso.module').then(m => m.TermosUsoModule)
  // },
  // {
  //   path: 'fale-conosco',
  //   loadChildren: () => import('apps/valloo/src/app/pages/fale-conosco/fale-conosco.module').then(m => m.FaleConoscoModule)
  // },
  // {
  //   path: 'esqueci-senha',
  //   loadChildren: () => import('apps/valloo/src/app/pages/esqueci-senha/esqueci-senha.module').then(m => m.EsqueciSenhaModule)
  // },
  // {
  //   path: 'selfie',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/selfie/selfie.module').then(m => m.SelfieModule)
  // },
  // {
  //   path: 'erro-alterar-dispositivo',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/dispositivo/erro-alterar-dispositivo/erro-alterar-dispositivo.module').then(m => m.ErroAlterarDispositivoModule)
  // },
  // {
  //   path: 'transferir-saldo/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/transferir-saldo/transferir-saldo.module').then(m => m.TransferirSaldoModule)
  // },
  // {
  //   path: 'campanhas/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/campanhas/campanhas.module').then(m => m.CampanhasModule)
  // },
  {
    path: 'cartao-fisico',
    loadChildren: () => import('apps/valloo/src/app/pages/cartoes/cartao-fisico/cartao-fisico.module').then(m => m.CartaoFisicoModule)
  },
  // {
  //   path: 'desbloqueio-fisico',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/desbloqueio-fisico/desbloqueio-fisico.module').then(m => m.DesbloqueioFisicoModule)
  // },
  {
    path: 'senha-cartao',
    loadChildren: () => import('apps/valloo/src/app/pages/cartoes/senha-cartao/senha-cartao.module').then(m => m.SenhaCartaoModule)
  },
  // {
  //   path: 'cartao-virtual',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/cartao-virtual/cartao-virtual.module').then(m => m.CartaoVirtualModule)
  // },
  {
    path: 'alterar-senha',
    loadChildren: () => import('apps/valloo/src/app/pages/cartoes/alterar-senha/alterar-senha.module').then(m => m.AlterarSenhaModule)
  },
  // {
  //   path: 'bloquear-cartao',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/bloquear-cartao/bloquear-cartao.module').then(m => m.BloquearCartaoModule)
  // },
  // {
  //   path: 'segunda-via',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/segunda-via/segunda-via.module').then(m => m.SegundaViaModule)
  // },
  {
    path: 'aproximacao/:idConta',
    loadChildren: () => import('apps/valloo/src/app/pages/cartoes/aproximacao/aproximacao.module').then(m => m.AproximacaoModule)
  },
  // {
  //   path: 'cadastro-sucesso',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/cadastro-sucesso/cadastro-sucesso.module').then(m => m.CadastroSucessoModule)
  // },
  {
    path: 'meu-perfil',
    loadChildren: () => import('apps/valloo/src/app/pages/meu-perfil/meu-perfil.module').then(m => m.MeuPerfilModule)
  },
  {
    path: 'ajustar-imagem',
    loadChildren: () => import('apps/valloo/src/app/pages/meu-perfil/ajustar-imagem/ajustar-imagem.module').then(m => m.AjustarImagemModule)
  },
  // {
  //   path: 'qrcode-elo/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/qrcode-elo/qrcode-elo.module').then(m => m.QrcodeEloModule)
  // },
  // {
  //   path: 'pagar-qrcode',
  //   loadChildren: () => import('apps/valloo/src/app/pages/qrcode-elo/pagar-qrcode/pagar-qrcode.module').then(m => m.PagarQrcodeModule)
  // },
  // {
  //   path: 'confirmar-dados-pessoais',
  //   loadChildren: () => import('apps/valloo/src/app/pages/atualizar-dados/confirmar-dados-pessoais/confirmar-dados-pessoais.module').then(m => m.ConfirmarDadosPessoaisModule)
  // },
  // {
  //   path: 'confirmar-endereco',
  //   loadChildren: () => import('apps/valloo/src/app/pages/atualizar-dados/confirmar-endereco/confirmar-endereco.module').then(m => m.ConfirmarEnderecoModule)
  // },
  // {
  //   path: 'atualizar-dados',
  //   loadChildren: () => import('apps/valloo/src/app/pages/atualizar-dados/atualizar-dados.module').then(m => m.AtualizarDadosModule)
  // },
  {
    path: 'inicio-app',
    loadChildren: () =>
      import('apps/valloo/src/app/pages/inicio-app/inicio-app.module').then(
        (m) => m.InicioAppModule
      ),
  },
  // {
  //   path: 'atualizar-versao',
  //   loadChildren: () => import('apps/multi conta/src/app/pages/atualizar-versao/atualizar-versao.module').then(m => m.AtualizarVersaoModule)
  // },
  // {
  //   path: 'erro-validacao-caf',
  //   loadChildren: () => import('apps/valloo/src/app/pages/onboarding/erro-validacao-caf/erro-validacao-caf.module').then(m => m.ErroValidacaoCafPageModule)
  // },
  // {
  //   path: 'alterar-senha-acesso',
  //   loadChildren: () => import('apps/valloo/src/app/pages/atualizar-dados/alterar-senha-acesso/alterar-senha-acesso.module').then(m => m.AlterarSenhaAcessoModule)
  // },
  // {
  //   path: 'saque-fgts',
  //   loadChildren: () => import('apps/valloo/src/app/pages/saque-fgts/saque-fgts.module').then(m => m.SaqueFgtsModule)
  // },
  // {
  //   path: 'parcelar-debitos',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/parcelar-debitos.module').then(m => m.ParcelarDebitosModule)
  // },
  // {
  //   path: 'pagar-boleto',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/pagar-boleto.module').then(m => m.PagarBoletoModule)
  // },
  // {
  //   path: 'digitar-codigo',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/digitar-codigo/digitar-codigo.module').then(m => m.DigitarCodigoModule)
  // },
  // {
  //   path: 'dados-boleto',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/dados-boleto/dados-boleto.module').then(m => m.DadosBoletoModule)
  // },
  // {
  //   path: 'adicionar-boleto',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/adicionar-boleto/adicionar-boleto.module').then(m => m.AdicionarBoletoModule)
  // },
  // {
  //   path: 'opcoes-boleto',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/opcoes-boleto/opcoes-boleto.module').then(m => m.OpcoesBoletoModule)
  // },
  // {
  //   path: 'endereco-boleto',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/endereco-boleto/endereco-boleto.module').then(m => m.EnderecoBoletoModule)
  // },
  // {
  //   path: 'dados-cartao',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-boleto/dados-cartao/dados-cartao.module').then(m => m.DadosCartaoModule)
  // },
  // {
  //   path: 'pagar-debitos-veiculares',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-debitos-veiculares/pagar-debitos-veiculares.module').then(m => m.ParcelarDebitosModule)
  // },
  // {
  //   path: 'selecionar-debitos',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/selecionar-debitos/selecionar-debitos.module').then(m => m.SelecionarDebitosModule)
  // },
  // {
  //   path: 'pagar-tributos',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/pagar-tributos/pagar-tributos.module').then(m => m.PagarTributosModule)
  // },
  // {
  //   path: 'historico',
  //   loadChildren: () => import('apps/valloo/src/app/pages/parcelar-debitos/historico/historico.module').then(m => m.HistoricoModule)
  // },
  // {
  //   path: 'diretrizes/:tipo',
  //   loadChildren: () => import('apps/valloo/src/app/pages/diretrizes/diretrizes.module').then(m => m.DiretrizesModule)
  // },
  {
    path: 'pagar-contas/:idConta',
    loadChildren: () => import('apps/valloo/src/app/pages/pagar/pagar.module').then(m => m.PagarPageModule)
  },
  {
    path: 'criar-senha',
    loadChildren: () => import('apps/valloo/src/app/pages/cartoes/criar-senha/criar-senha.module').then(m => m.CriarSenhaPageModule)
  },
  // {
  //   path: 'recarga/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/recarga/recarga.module').then(m => m.RecargaModule)
  // },
  // {
  //   path: 'operadora',
  //   loadChildren: () => import('apps/valloo/src/app/pages/recarga/operadora/operadora.module').then(m => m.OperadoraModule)
  // },
  // {
  //   path: 'recarga-valor',
  //   loadChildren: () => import('apps/valloo/src/app/pages/recarga/recarga-valor/recarga-valor.module').then(m => m.RecargaValorModule)
  // },
  // {
  //   path: 'confirmar-recarga',
  //   loadChildren: () => import('apps/valloo/src/app/pages/recarga/confirmar-recarga/confirmar-recarga.module').then(m => m.ConfirmarRecargaModule)
  // },
  // {
  //   path: 'ted/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/ted/ted.module').then(m => m.TedModule)
  // },
  // {
  //   path: 'transferir-pontos/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/transferir-pontos/transferir-pontos.module').then(m => m.TransferirPontosModule)
  // },
  // {
  //   path: 'ajuda',
  //   loadChildren: () => import('apps/valloo/src/app/pages/chat/chat.module').then(m => m.ChatPageModule)
  // },
  {
    path: 'notificacoes/:idConta',
    loadChildren: () => import('apps/valloo/src/app/pages/notificacoes/notificacoes.module').then(m => m.NotificacoesPageModule)
  },
  // {
  //   path: 'selecionar-tarifa',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/selecionar-tarifa/selecionar-tarifa.module').then(m => m.SelecionarTarifaModule)
  // },
  // {
  //   path: 'extrato-contas-migradas/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/extrato-contas-migradas/extrato-contas-migradas.module').then(m => m.ExtratoContasMigradasPageModule)
  // },
  // {
  //   path: 'cartoes-migrados',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes-migrados/cartoes-migrados.module').then(m => m.CartoesMigradosPageModule)
  // },
  // {
  //   path: 'cartoes-selecionados',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes-migrados/cartoes-selecionados/cartoes-selecionados.module').then(m => m.CartoesSelecionadosPageModule)
  // },
  // {
  //   path: 'cartoes-migrados-sucesso',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes-migrados/cartoes-migrados-sucesso/cartoes-migrados-sucesso.module').then(m => m.CartoesMigradosSucessoPageModule)
  // },
  // {
  //   path: 'inserir-carga/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/inserir-carga/inserir-carga.module').then(m => m.InserirCargaModule)
  // },
  // {
  //   path: 'onde-aceita',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/onde-aceita/onde-aceita.module').then(m => m.OndeAceitaModule)
  // },
  // {
  //   path: 'onde-aceita/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/onde-aceita/onde-aceita.module').then(m => m.OndeAceitaModule)
  // },
  // {
  //   path: 'dados-consumo/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/dados-consumo/dados-consumo.module').then(m => m.DadosConsumoModule)
  // },
  // {
  //   path: 'dados-consumo-pagina-inicial/:idConta',
  //   loadChildren: () => import('apps/valloo/src/app/pages/cartoes/dados-consumo/dados-consumo.module').then(m => m.DadosConsumoModule)
  // },
];
