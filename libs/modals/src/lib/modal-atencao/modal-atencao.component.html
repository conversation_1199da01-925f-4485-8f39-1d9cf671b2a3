<ion-content [fullscreen]="true" [scrollX]="false" [scrollY]="false">
  <section></section>
  <section>
    <div class="destaque">
      <ion-icon [name]="nomeIcon" [color]="colorIcon"></ion-icon>
    </div>
    <article>
      <h1>{{ titulo }}</h1>
      <h2 *ngIf="environment.idInstituicao != 8401" [ngClass]="mensagem.length > 53 ? 'texto-justificado' : 'texto-centralizado'" [innerHTML]="mensagem"></h2>
      <div *ngIf="environment.idInstituicao == 8401" [innerHTML]="mensagem"></div>
    </article>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="fazerAcaoPrimaria()">{{ tituloBotaoPrimario }}</ion-button>
  <ion-button class="ion-margin-top" *ngIf="tituloBotaoSecundario" expand="block" fill="outline"
              (click)="fazerAcaoSecundaria()">{{ tituloBotaoSecundario }}
  </ion-button>
</ion-footer>
