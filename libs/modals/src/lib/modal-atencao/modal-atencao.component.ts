import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { environment } from '@corporativo/shared';

@Component({
  selector: 'mobile-modal-atencao',
  templateUrl: './modal-atencao.component.html',
  styleUrls: ['./modal-atencao.component.scss'],
  standalone: false
})
export class ModalAtencaoComponent implements OnInit {
  @Input() titulo: string = 'Atenção';
  @Input() mensagem: string = '';
  @Input() tituloBotaoPrimario = 'Voltar para o início';
  @Input() tituloBotaoSecundario = '';
  @Input() nomeIcon = 'alert-outline';
  @Input() colorIcon = 'warning';

  constructor(private router: Router, private modalController: ModalController) {
  }

  ngOnInit() {
  }

  fazerAcaoPrimaria() {
    this.modalController.dismiss({role: 'primaria'});
  }

  fazerAcaoSecundaria() {
    this.modalController.dismiss({role: 'secundaria'});
  }

  protected readonly environment = environment;
}
