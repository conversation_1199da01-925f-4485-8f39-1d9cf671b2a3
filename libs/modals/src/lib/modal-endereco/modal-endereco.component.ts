import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { cep } from '@utils/masks.util';
import {
  AuthService,
  Credencial,
  EnderecoService,
  FuncionalidadeService,
  StorageService,
  Usuario
} from '@corporativo/shared';
import { TextUtil } from '@utils/text.util';
import { loading } from '@utils/loading.util';
import { toast } from '@utils/toast.util';
import { ModalSucessoComponent } from '../modal-sucesso/modal-sucesso.component';
import { ModalErroComponent } from '../modal-erro/modal-erro.component';
import { format } from 'date-fns';

@Component({
  selector: 'mobile-modal-endereco',
  templateUrl: './modal-endereco.component.html',
  styleUrls: ['./modal-endereco.component.scss'],
  standalone: false
})
export class ModalEnderecoComponent implements OnInit {
  @Input() tituloSecao = 'Confirme o endereço de entrega do cartão';
  @Input() credencial!: Credencial;
  @Input() mostrarVoltar = true;
  form: FormGroup;
  usuario!: Usuario;
  readonly cepMaskOptions: MaskitoOptions = cep;
  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();

  constructor(
    public router: Router,
    private funcionalidadeService: FuncionalidadeService,
    private modalController: ModalController,
    private enderecoService: EnderecoService,
    private authService: AuthService,
    private storageService: StorageService
  ) {
    this.form = new FormGroup(
      {
        cep: new FormControl('', [Validators.required]),
        logradouro: new FormControl('', [Validators.required]),
        numero: new FormControl('', [Validators.required]),
        complemento: new FormControl(''),
        bairro: new FormControl('', [Validators.required]),
        cidade: new FormControl('', [Validators.required]),
        uf: new FormControl('', [Validators.required])
      }
    );
  }

  ngOnInit() {
    this.usuario = this.authService.getUser();
    this.form.patchValue(this.usuario.enderecoResidencial);
  }

  async buscarCep() {
    if (this.form.value.cep && this.form.value.cep.length === 9) {
      const cep = TextUtil.removeNotDigit(this.form.value.cep);
      await loading(this.enderecoService.buscarCep(cep).subscribe({
        next: (endereco: any) => {
          if (endereco.cep) {
            this.form.patchValue({
              logradouro: endereco.nomeAbreviado,
              bairro: endereco.bairroInicio.nome,
              cidade: endereco.bairroInicio.localidade.nome,
              uf: endereco.bairroInicio.localidade.uf.sigla
            });
          } else {
            this.form.reset();
          }
        }, error: (error: any) => {
          this.form.reset();
          toast(error.error.msg);
        }
      }));
    }
  }

  fechar() {
    return this.modalController.dismiss();
  }

  async continuar() {
    if (this.form.invalid) {
      return;
    }
    const dadosPessoais = {
      cpf: this.usuario.documentoRepresentante,
      nome: this.usuario.razaoSocial,
      dataNascimento: this.usuario.dataNascimento == null ? format(this.usuario.dataFundacao, 'dd/MM/yyyy') : format(this.usuario.dataNascimento, 'dd/MM/yyyy'),
      estadoCivil: this.usuario.estadoCivil,
      sexo: this.usuario.idSexo,
      nacionalidade: this.usuario.nacionalidade
    };
    const endereco: any = this.form.getRawValue();
    await loading(this.funcionalidadeService.atualizarDados(endereco, this.usuario, dadosPessoais).subscribe({
      next: () => {
        endereco.dtHrConfirmacao = new Date();
        this.usuario.enderecoResidencial = endereco;
        this.storageService.setUser(this.usuario);
        this.modalController.dismiss(endereco);
      }, error: (error) => {
        const message = error?.msg ? error?.error?.msg : error.message;
        this.abrirModalError(message);
      }
    }));
  }

  async abrirModalSucesso() {
    const modal = await this.modalController.create({
      component: ModalSucessoComponent,
      componentProps: {
        titulo: 'Endereço atualizado',
        mensagem: 'Seus dados de endereço foram atualizados com sucesso.',
        tituloBotao: 'Continuar'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      await this.modalController.dismiss(this.form.getRawValue());
    }
  }

  async abrirModalError(message: string) {
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: message ? 'Ocorreu um erro' : 'Erro inesperado',
        mensagem: message ? message : 'Houve um erro inesperado. Tente novamente mais tarde.',
        tituloBotao: 'Tentar novamente',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'secundaria') {
      await this.fechar();
    }
  }
}
