<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [mostrarVoltar]="mostrarVoltar" [buttonModal]="true" (closeEmitter)="fechar()">Endereço</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">
  <mobile-titulo-secao>{{ tituloSecao }}</mobile-titulo-secao>
  <section>
    <form [formGroup]="form">
      <ion-item lines="none">
        <ion-input label="CEP" placeholder="00000-00" [maskito]="cepMaskOptions" (focusout)="buscarCep()"
                   [maskitoElement]="maskPredicate" labelPlacement="stacked" type="tel" formControlName="cep"
                   errorText="Informe o CEP"></ion-input>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input label="Endereço" placeholder="Rua do amores" labelPlacement="stacked" type="text"
                   formControlName="logradouro"
                   errorText="Informe o endereço"></ion-input>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input label="Número" placeholder="1421" labelPlacement="stacked" type="tel" formControlName="numero"
                   errorText="Informe o numero"></ion-input>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input label="Complemento" placeholder="Bloco 2" labelPlacement="stacked" type="text"
                   formControlName="complemento"
                   errorText="Informe o complemento"></ion-input>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input label="Bairro" placeholder="Campo largo" labelPlacement="stacked" type="text"
                   formControlName="bairro"
                   errorText="Informe o bairro"></ion-input>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input label="Cidade" placeholder="Campo grande" labelPlacement="stacked" type="text"
                   formControlName="cidade"
                   errorText="Informe a cidade"></ion-input>
      </ion-item>

      <ion-item class="ion-margin-top" lines="none">
        <ion-input label="Estado" placeholder="UF" labelPlacement="stacked" type="text" formControlName="uf"
                   errorText="Informe o estado"></ion-input>
      </ion-item>
    </form>
  </section>
</ion-content>
<ion-footer class="ion-no-border">
  <ion-toolbar>
    <ion-button class="btn" expand="block" (click)="continuar()">Confirmar</ion-button>
  </ion-toolbar>
</ion-footer>
