import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TitleToolbarModule, TituloSecaoModule } from '@corporativo/components';
import { ModalEnderecoComponent } from './modal-endereco.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MaskitoDirective } from '@maskito/angular';

@NgModule({
  declarations: [ModalEnderecoComponent],
  exports: [ModalEnderecoComponent],
  imports: [
    CommonModule,
    IonicModule,
    TituloSecaoModule,
    TitleToolbarModule,
    ReactiveFormsModule,
    MaskitoDirective
  ]
})
export class ModalEnderecoModule {
}
