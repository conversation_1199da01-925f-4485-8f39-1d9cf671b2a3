ion-content {
  --background: transparent;
  display: flex;
  flex-direction: column;
}

section {
  height: 50%;
}

section:last-child {
  position: relative;
  background: var(--ion-color-background);
  padding-top: 2.5rem;
  text-align: center;
  border-radius: 1.5rem 1.5rem 0 0;
}

.destaque {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--ion-color-secondary);
  background: var(--ion-color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 53%;
  margin-left: -40px;
  top: -40px;

  ion-icon {
    font-size: 40px;
  }
}

h1 {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.8px;
  margin: 1.5rem 0;
  color: var(--ion-color-gray-50);
}

h2 {
  font-size: 16px;
  font-weight: 300;
  line-height: 19.2px;
  color: var(--ion-color-gray-50);
  margin: 1.5rem;
  text-align: center;
}

ion-footer {
  background: var(--ion-color-background);
  padding: 1.5rem;
}

