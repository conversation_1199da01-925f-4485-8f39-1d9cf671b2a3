<ion-content [fullscreen]="true" [scrollX]="false" [scrollY]="false">
  <section></section>
  <section>
    <div class="destaque">
      <ion-icon name="notificacao"></ion-icon>
    </div>
    <article>
      <h1>{{ mensagem.aplicativoMensagem.txTituloMensagem }}</h1>
      <h2>{{mensagem.aplicativoMensagem.txMensagem}}</h2>
    </article>
  </section>
</ion-content>

<ion-footer>
  <ion-button *ngIf="mensagem.aplicativoMensagem.aplicativoServico != null" expand="block" (click)="saberMais()">Saber mais</ion-button>
  <ion-button class="ion-margin-top" expand="block" fill="outline" (click)="sair()">Fechar</ion-button>
</ion-footer>
