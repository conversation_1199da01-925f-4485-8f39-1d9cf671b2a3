import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { NotificacoesService, ProdutoService } from '@corporativo/shared';

const PRODUTO_MOEDEIRO = 280139;

@Component({
  selector: 'mobile-modal-notificacao-destaque',
  templateUrl: './modal-notificacao-destaque.component.html',
  styleUrls: ['./modal-notificacao-destaque.component.scss'],
  standalone: false,
})
export class ModalNotificacaoDestaqueComponent implements OnInit {
  produtos!: any[];
  @Input() mensagem: any;
  @Input() credencial: any;
  @Input() idCredencial!: number;
  @Input() idConta!: number;

  constructor(
    private router: Router,
    private notificacoesService: NotificacoesService,
    private produtoService: ProdutoService,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.buscarProdutos();
  }

  buscarProdutos() {
    this.produtoService.getFuncionalidades(this.idConta).subscribe({
      next: (produto: any) => {
        this.produtos = [];
        this.produtos = produto;
        const maisServicos: any[] = this.produtoService.getMaisServicos();
        const funcionalidesCartao: any[] =
          this.produtoService.getFuncionalidadesCartao(this.credencial);
        this.produtos = this.produtos.concat(funcionalidesCartao, maisServicos);
      },
    });
  }

  async sair() {
    this.notificacoesService
      .marcarMensagemComoLida(this.mensagem.id)
      .subscribe();
    await this.modalController.dismiss();
  }

  saberMais() {
    this.navegarParaRota(
      this.mensagem.aplicativoMensagem.aplicativoServico.nomeServico
    );
  }

  fazerAcaoSecundaria() {
    this.modalController.dismiss({ role: 'secundaria' });
  }

  navegarParaRota(nomeServico: string) {
    this.notificacoesService
      .marcarMensagemComoLida(this.mensagem.id)
      .subscribe();
    const produto = this.produtos.filter((x: any) => x.codigo == nomeServico);
    if (produto.length > 0) {
      if (produto[0].codigo == 'campanhas') {
        const conta = this.credencial.contas.filter(
          (x: any) => x.idProdutoInstituicao == PRODUTO_MOEDEIRO
        );
        this.modalController.dismiss();
        this.router.navigate(['/' + produto[0].url + '/' + conta[0].idConta]);
      }
      if (
        produto[0].codigo == 'saque_fgts' ||
        produto[0].codigo == 'parcelar_debitos'
      ) {
        this.modalController.dismiss();
        this.router.navigate(['/' + produto[0].url]);
      }
      this.router.navigate(['/' + produto[0].url + '/' + this.idConta]);
      this.modalController.dismiss();
    } else {
      this.modalController.dismiss();
    }
  }
}
