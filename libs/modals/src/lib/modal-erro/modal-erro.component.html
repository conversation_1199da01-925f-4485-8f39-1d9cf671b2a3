<ion-content [fullscreen]="true" [scrollX]="false" [scrollY]="false">
  <section></section>
  <section>
    <div class="destaque">
      <ion-icon name="close-outline"></ion-icon>
    </div>
    <article>
      <h1>{{ titulo }}</h1>
      <h2 [ngClass]="mensagem.length > 53 ? 'texto-justificado' : 'texto-centralizado'" [innerHTML]="mensagem"></h2>
    </article>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="fechar()">{{ tituloBotao }}</ion-button>
  <ion-button class="ion-margin-top" *ngIf="tituloBotaoSecundario" expand="block" fill="outline"
              (click)="fazerAcaoSecundaria()">{{ tituloBotaoSecundario }}
  </ion-button>
</ion-footer>
