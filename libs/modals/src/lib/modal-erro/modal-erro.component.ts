import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'mobile-modal-erro',
  templateUrl: './modal-erro.component.html',
  styleUrls: ['./modal-erro.component.scss'],
  standalone: false
})
export class ModalErroComponent implements OnInit {
  @Input() titulo: string = 'Error';
  @Input() mensagem: string = '';
  @Input() urlRetorno: string = '/inicio';
  @Input() classeImagem = 'sucesso';
  @Input() tituloBotao = 'Voltar para o início';
  @Input() tituloBotaoSecundario = '';

  constructor(private router: Router, private modalController: ModalController) {
  }

  ngOnInit() {
  }

  fechar() {
    this.modalController.dismiss({role: 'primaria'});
  }

  fazerAcaoSecundaria() {
    this.modalController.dismiss({role: 'secundaria'});
  }

}
