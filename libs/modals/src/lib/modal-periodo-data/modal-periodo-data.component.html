<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [buttonModal]="true" (closeEmitter)="fechar()">Selecionar período</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>
<ion-content class="content-default">
  <p class="label-date">Data início</p>
  <ion-item lines="full" [button]="true" [detail]="false" (click)="selecionarData($event, true)">
    <ion-label>{{dataInicio | date:'mediumDate'}}</ion-label>
    <ion-icon class="icon-calendar" slot="end" name="calendar-outline"></ion-icon>
  </ion-item>
  <p class="label-date">Data fim</p>
  <ion-item lines="full" [button]="true" [detail]="false" (click)="selecionarData($event, false)">
    <ion-label>{{dataFim | date:'mediumDate'}}</ion-label>
    <ion-icon class="icon-calendar" slot="end" name="calendar-outline"></ion-icon>
  </ion-item>
</ion-content>
<ion-footer class="ion-no-border">
  <ion-button class="btn ion-margin-bottom" expand="block" (click)="continuar()">Selecionar</ion-button>
  <ion-button expand="block" fill="outline" (click)="fechar()">Cancelar</ion-button>
</ion-footer>
