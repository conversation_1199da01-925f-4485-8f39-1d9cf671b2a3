import { Component, OnInit } from '@angular/core';
import { ModalController, PopoverController } from '@ionic/angular';
import { format } from 'date-fns';
import { toast } from '@utils/toast.util';
import { PopoverDataComponent } from '../../../../components/src/lib/popover-data/popover-data.component';

@Component({
  selector: 'mobile-modal-periodo-data',
  templateUrl: './modal-periodo-data.component.html',
  styleUrls: ['./modal-periodo-data.component.scss'],
  standalone: false,
})
export class ModalPeriodoDataComponent implements OnInit {
  dataInicio = format(
    new Date().setDate(new Date().getDate() - 15),
    "yyyy-MM-dd'T'HH:mm:ss"
  );
  dataFim = format(new Date(), "yyyy-MM-dd'T'HH:mm:ss");

  constructor(
    private modalController: ModalController,
    private popoverController: PopoverController
  ) {}

  ngOnInit() {}

  fechar() {
    this.modalController.dismiss();
  }

  async selecionarData(e: Event, first: boolean) {
    const popover = await this.popoverController.create({
      component: PopoverDataComponent,
      componentProps: { max: format(new Date(), "yyyy-MM-dd'T'HH:mm:ss") },
      event: e,
      size: 'auto',
      alignment: 'center',
      cssClass: 'popover-data',
    });

    await popover.present();

    const { data } = await popover.onDidDismiss();
    if (!data) {
      return;
    }
    if (first) {
      this.dataInicio = data;
    } else {
      this.dataFim = data;
    }
  }

  continuar() {
    if (this.dataInicio > this.dataFim) {
      toast('Data início não pode maior que a data fim.');
      return;
    }
    this.modalController.dismiss({
      dataInicio: this.dataInicio,
      dataFim: this.dataFim,
    });
  }
}
