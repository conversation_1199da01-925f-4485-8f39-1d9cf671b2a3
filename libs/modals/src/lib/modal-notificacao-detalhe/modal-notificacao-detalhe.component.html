<ion-header class="ion-no-border">
  <ion-toolbar>
    <mobile-title-toolbar [mostrarVoltar]="true" [buttonModal]="true" (closeEmitter)="fechar()">Notificações</mobile-title-toolbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="content-default">

  <section class="imagem">
    <article class="imagem-notificacao">
      <ion-img *ngIf="mensagem.aplicativoMensagem.img" [src]="mensagem.aplicativoMensagem.img"></ion-img>
    </article>

    <div>
      <h3>{{mensagem.aplicativoMensagem.txTituloDetalhe | titlecase}}</h3>
      <p>{{mensagem.aplicativoMensagem.txDescricaoDetalhe}}</p>
    </div>
  </section>
</ion-content>

<ion-footer>
  <ion-button *ngIf="mensagem.aplicativoMensagem.aplicativoServico != null" expand="block" (click)="saberMais()">Saber mais</ion-button>
  <ion-button class="ion-margin-top" fill="outline" expand="block" (click)="fechar()">Fechar</ion-button>
</ion-footer>
