ion-footer {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
}

.imagem-notificacao {
  border-radius: 50%;
  width: 50%;
  background: var(--ion-color-medium);
  margin-top: 15%;
}

.imagem {
  display: flex;
  flex-direction: column;
  align-items: center;
}

ion-img {
  margin: 20%;
}

h3 {
  text-align: center;
  color: var(--ion-color-gray-50);
  margin-top: 40px;
  font-weight: bold;
}

p {
  text-align: justify;
  margin-top: 40px;
}
