import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { NotificacoesService, ProdutoService } from '@corporativo/shared';
import { lastValueFrom } from 'rxjs';

const PRODUTO_MOEDEIRO = 280139;

@Component({
  selector: 'mobile-modal-notificacao-detalhe',
  templateUrl: './modal-notificacao-detalhe.component.html',
  styleUrls: ['./modal-notificacao-detalhe.component.scss'],
  standalone: false,
})
export class ModalNotificacaoDetalheComponent implements OnInit {
  produtos: any[] = [];
  @Input() mensagem: any;
  @Input() contas: any;
  @Input() idConta!: number;
  @Input() idCredencial!: number;
  @Input() credencial: any;

  constructor(
    private router: Router,
    private notificacoesService: NotificacoesService,
    private produtoService: ProdutoService,
    private modalController: ModalController
  ) {}

  async ngOnInit() {
    await this.buscarProdutos();
    if (!this.mensagem.blLido) {
      await lastValueFrom(
        this.notificacoesService.marcarMensagemComoLida(this.mensagem.id)
      );
    }
  }

  buscarProdutos() {
    this.produtoService.getFuncionalidades(this.idConta).subscribe({
      next: (produto: any) => {
        this.produtos = [];
        this.produtos = produto;
        const maisServicos: any[] = this.produtoService.getMaisServicos();
        const funcionalidesCartao: any[] =
          this.produtoService.getFuncionalidadesCartao(this.credencial);
        this.produtos = this.produtos.concat(funcionalidesCartao, maisServicos);
      },
    });
  }

  saberMais() {
    return this.navegarParaRota(
      this.mensagem.aplicativoMensagem.aplicativoServico.nomeServico
    );
  }

  navegarParaRota(nomeServico: string) {
    const produto = this.produtos.filter((x: any) => x.codigo == nomeServico);
    if (!produto.length) {
      this.modalController.dismiss();
      return;
    }
    if (produto[0].codigo == 'campanhas') {
      const conta = this.contas.filter(
        (x: any) => x.idProdutoInstituicao == PRODUTO_MOEDEIRO
      );
      this.modalController.dismiss();
      this.router.navigate(['/' + produto[0].url + '/' + conta[0].idConta]);
      return;
    }
    if (
      produto[0].codigo == 'saque_fgts' ||
      produto[0].codigo == 'parcelar_debitos'
    ) {
      this.modalController.dismiss();
      this.router.navigate(['/' + produto[0].url]);
      return;
    }
    this.router.navigate(['/' + produto[0].url + '/' + this.idConta]);
    this.modalController.dismiss();
  }

  fechar() {
    if (!this.mensagem.blLido) {
      this.modalController.dismiss({ role: 'primaria' });
      return;
    }
    this.modalController.dismiss();
  }
}
