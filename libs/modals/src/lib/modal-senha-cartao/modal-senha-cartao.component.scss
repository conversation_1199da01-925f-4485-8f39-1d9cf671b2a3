ion-content {
  --background: transparent;
  display: flex;
  flex-direction: column;
}

section:first-child {
  height: 45%;
}

.imagem {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

img {
  width: 60%;
}

section:last-child {
  position: relative;
  background: var(--ion-color-background);
  text-align: center;
  border-radius: 1.5rem 1.5rem 0 0;
  height: 55%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.destaque {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--A<PERSON><PERSON><PERSON>alloo, #2DC2E3);
  background: var(--ion-color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  margin-left: -40px;
  top: -40px;

  ion-icon {
    font-size: 40px;
  }
}

h1 {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.8px;
  margin: 2.5rem 0;
}

h2 {
  font-size: 16px;
  font-weight: 300;
  line-height: 24.2px;
  color: var(--ion-color-gray-50);
  margin: 2rem 1rem;
  text-align: center;
}

.acoes {
  background: var(--ion-color-background);
  padding: 1rem;
}

.input-cod {
  display: flex;
  align-items: center;
  justify-content: center;

  ion-input {
    font-size: 54px;
    font-weight: 700;
    --placeholder-opacity: 0.2;
    --border-radius: 8px;
    --border: 1px solid #B3B3AC;
    letter-spacing: 1rem;
    text-align: center;
    --padding-start: 2rem;
  }
}

.plastico-senha {
  width: 65%;
}
