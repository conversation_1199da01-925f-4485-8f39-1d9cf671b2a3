import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import {
  AuthService,
  CredencialService,
  MetodoSegurancaEnum,
  OnboardingService,
  TipoObjetivoEnum,
  Usuario,
} from '@corporativo/shared';
import { Router } from '@angular/router';
import { ModalSegurancaComponent } from '../modal-seguranca/modal-seguranca.component';

@Component({
  selector: 'vlo-modal-senha-cartao',
  templateUrl: './modal-senha-cartao.component.html',
  styleUrls: ['./modal-senha-cartao.component.scss'],
  standalone: false,
})
export class ModalSenhaCartaoComponent implements OnInit {
  @Input() credencial: any;
  usuario!: Usuario;
  cafNecessario: boolean = false;

  constructor(
    private modalController: ModalController,
    private credencialService: CredencialService,
    private authService: AuthService,
    public router: Router,
    private onboardingService: OnboardingService
  ) {
    this.usuario = this.authService.getUser();
  }

  ngOnInit() {
    this.buscarCafNecessarioPorConta();
  }

  buscarCafNecessarioPorConta() {
    this.onboardingService
      .encontraCafNecessarioPorConta(this.credencial.idConta)
      .subscribe({
        next: (value: any) => {
          this.cafNecessario = value;
        },
      });
  }

  async continuar() {
    if (this.cafNecessario) {
      const fluxoDados: any = {};
      fluxoDados.documento = this.usuario.documento;
      fluxoDados.documentoRepresentante =
        this.usuario.documentoRepresentante == null
          ? null
          : this.usuario.documentoRepresentante;
      fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaCartao;
      fluxoDados.tipo = 'troca-senha-cartao';
      fluxoDados.fecharModal = true;
      await this.modalController.dismiss();
      await this.router.navigate(['/selfie'], {
        state: { fluxoDados: fluxoDados, dados: this.credencial },
      });
    } else {
      const valido = await this.verificarSeguranca(this.credencial);
      if (!valido) {
        return;
      }
      await this.trocarSenhaCartao();
    }
  }

  async verificarSeguranca(usuario: any) {
    if (usuario.metodoSegurancaTransacao == MetodoSegurancaEnum.NaoVerificar) {
      return true;
    }
    const modal = await this.modalController.create({
      component: ModalSegurancaComponent,
      componentProps: {
        metodoSegurancaTransacao: 2,
        idCredencial: usuario.idCredencial,
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    return data;
  }

  async trocarSenhaCartao() {
    await this.fecharModais();
    const fluxoDados: any = {};
    fluxoDados.documento = this.usuario.documento;
    fluxoDados.documentoRepresentante =
      this.usuario.documentoRepresentante == null
        ? null
        : this.usuario.documentoRepresentante;
    fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaCartao;
    fluxoDados.tipo = 'troca-senha-cartao';
    fluxoDados.fecharModal = true;
    return this.router.navigate(['/senha-cartao'], {
      state: {
        fluxoDados: fluxoDados,
        dados: this.credencial,
      },
    });
  }

  async fecharModais() {
    let modais: any = await this.modalController.getTop();
    while (modais) {
      await this.modalController.dismiss();
      modais = await this.modalController.getTop();
    }
  }
}
