import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'mobile-modal-sucesso',
  templateUrl: './modal-sucesso.component.html',
  styleUrls: ['./modal-sucesso.component.scss'],
  standalone: false,
})
export class ModalSucessoComponent implements OnInit {
  @Input() titulo: string = 'Sucesso';
  @Input() mensagem: string = '';
  @Input() urlRetorno: string = '/inicio';
  @Input() classeImagem = 'sucesso';
  @Input() tituloBotao = 'Voltar para o início';
  @Input() tituloBotaoSecundario = '';

  constructor(
    private router: Router,
    private modalController: ModalController
  ) {}

  ngOnInit() {}

  fechar() {
    this.router.navigate([this.urlRetorno]);
    this.modalController.dismiss();
  }

  fazerAcaoSecundaria() {
    this.modalController.dismiss({ role: 'secundaria' });
  }
}
