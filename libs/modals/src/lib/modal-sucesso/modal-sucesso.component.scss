ion-content {
  --background: transparent;
  display: flex;
  flex-direction: column;
}

section {
  height: 50%;
}

section:last-child {
  position: relative;
  background: var(--ion-color-background);
  padding-top: 2.5rem;
  text-align: center;
  border-radius: 1.5rem 1.5rem 0 0;
}

.destaque {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--ion-color-success);
  background: var(--ion-color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  margin-left: -40px;
  top: -40px;

  ion-icon {
    font-size: 40px;
    color: var(--ion-color-success);
  }
}

h1 {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.8px;
  margin: 1.5rem 15px;
}

h2 {
  font-size: 16px;
  font-weight: 300;
  line-height: 19.2px;
  color: var(--ion-color-gray-50);
  margin: 1.5rem;
}

ion-footer {
  background: var(--ion-color-background);
  padding: 1.5rem;
}

.texto-justificado {
  text-align: justify;
}

.texto-centralizado {
  text-align: center;
}
