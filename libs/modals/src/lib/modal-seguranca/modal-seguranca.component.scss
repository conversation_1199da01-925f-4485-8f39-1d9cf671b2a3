ion-content {
  --background: transparent;
  display: flex;
  flex-direction: column;
}

section:first-child {
  height: 45%;
}

section:last-child {
  position: relative;
  background: var(--ion-color-background);
  padding-top: 2.5rem;
  text-align: center;
  border-radius: 1.5rem 1.5rem 0 0;
  height: 55%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.destaque {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, #2DC2E3);
  background: var(--ion-color-background);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  margin-left: -40px;
  top: -40px;

  ion-icon {
    font-size: 40px;
  }
}

h1 {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.8px;
  margin: 1.5rem 0;
}

h2 {
  font-size: 16px;
  font-weight: 300;
  line-height: 19.2px;
  color: var(--ion-color-gray-50);
  margin: 1.5rem 0;
}

//.inputs {
//  display: flex;
//  align-items: center;
//  justify-content: center;
//  gap: 0.5rem;
//  margin: 2rem 0 0 0;
//
//  div {
//    //min-width: 55px;
//    //width: 55px;
//    //height: 55px;
//    //border-radius: 8px;
//    //border: 1px solid #B3B3AC;
//    //background: #FAFAFA;
//    display: flex;
//    align-items: center;
//    justify-content: center;
//  }
//
//  ion-input {
//    font-size: 54px;
//    font-weight: 700;
//    //width: 32px;
//    //height: 32px;
//    --placeholder-opacity: 0.2;
//    --border-radius: 8px;
//    --border: 1px solid #B3B3AC;
//  }
//
//  input {
//    text-align: center;
//    outline: none;
//    padding: 0 0 8px 0;
//    font-size: 54px;
//    border: none;
//    width: 32px;
//    height: 32px;
//    background: #FAFAFA;
//    font-weight: 700;
//  }
//
//  &.token {
//    div {
//      width: 48px;
//      min-width: 48px;
//      height: 48px;
//    }
//
//    input {
//      padding: 0;
//      font-size: 40px;
//      width: 40px;
//      height: 40px;
//    }
//  }
//}

.acoes {
  background: var(--ion-color-background);
  padding: 1rem;
}

.input-cod {
  display: flex;
  align-items: center;
  justify-content: center;

  ion-input {
    font-size: 54px;
    font-weight: 700;
    --placeholder-opacity: 0.2;
    --border-radius: 8px;
    --border: 1px solid #B3B3AC;
    letter-spacing: 1rem;
    text-align: center;
    --padding-start: 2rem;
  }
}
