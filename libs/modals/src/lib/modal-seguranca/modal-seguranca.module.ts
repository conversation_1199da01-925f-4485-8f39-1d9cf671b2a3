import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ModalSegurancaComponent } from '@corporativo/modals';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TitleToolbarModule } from '@corporativo/components';
import { MaskitoDirective } from '@maskito/angular';

@NgModule({
  declarations: [ModalSegurancaComponent],
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    TitleToolbarModule,
    MaskitoDirective,
  ],
})
export class ModalSegurancaModule {}
