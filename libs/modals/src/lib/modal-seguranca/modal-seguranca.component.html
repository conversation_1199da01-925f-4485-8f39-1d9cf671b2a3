<ion-header></ion-header>
<ion-content [fullscreen]="true" [scrollX]="false" [scrollY]="false">
  <section (click)="fechar()"></section>
  <section>
    <div>
      <div class="destaque">
        <ion-icon [name]="metodo == metodoSenhaCartao ? 'senha-cartao' : 'confirmar-token-sms'"></ion-icon>
      </div>

      @if (metodo == metodoSenhaCartao) {
        <form [formGroup]="formSeguranca">
          <h1>Confirmar senha do cartão</h1>
          <h2>Digite sua senha de 4 dígitos</h2>
          <div class="input-cod">
            <ion-input #fieldValue maxlength="4" formControlName="valor" placeholder="••••" inputmode="numeric" type="password"></ion-input>
          </div>
        </form>
      } @else {
        <form [formGroup]="formSeguranca">
          <h1>Confirmar token</h1>
          <h2>Digite o token recebido no seu celular</h2>
          <div class="input-cod">
            <ion-input #fieldValue maxlength="6" formControlName="valor" placeholder="••••••" inputmode="numeric" type="password"></ion-input>
          </div>
        </form>
      }
    </div>
    <article class="acoes">
      <ion-button *ngIf="metodo != metodoSenhaCartao" expand="block" fill="outline" (click)="enviarSms()"
                  class="ion-margin-bottom">
        Reenviar token
      </ion-button>
      <ion-button class="btn" expand="block" (click)="confirmar()" [disabled]="formSeguranca.invalid">Confirmar</ion-button>
    </article>
  </section>

</ion-content>


