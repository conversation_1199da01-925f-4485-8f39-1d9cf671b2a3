import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { IonInput, ModalController, Platform } from '@ionic/angular';
import {
  AuthService,
  CredencialService,
  MetodoSegurancaEnum,
  StorageService,
  TipoObjetivoEnum,
  TokenFuncionalidadeService,
  Usuario,
} from '@corporativo/shared';
import { DatePipe } from '@angular/common';
import { loading } from '@utils/loading.util';
import { toast } from '@utils/toast.util';
import { concatMap, lastValueFrom } from 'rxjs';
import { HashUtil } from '@utils/hash.util';
import { Clipboard } from '@capacitor/clipboard';
import { numberMask } from '@utils/masks.util';
import { MaskitoElementPredicate, MaskitoOptions } from '@maskito/core';
import { ModalErroComponent } from '../modal-erro/modal-erro.component';
import { Router } from '@angular/router';
import { Keyboard } from '@capacitor/keyboard';

@Component({
  selector: 'mobile-modal-seguranca',
  templateUrl: './modal-seguranca.component.html',
  styleUrl: './modal-seguranca.component.scss',
  standalone: false,
})
export class ModalSegurancaComponent implements OnInit {
  @Input() metodoSegurancaTransacao!: number;
  @Input() telefoneCelular!: string;
  @Input() idCredencial!: number;
  @Input() documento!: string;
  usuarioSelecionado: any;

  @ViewChild('fieldValue', { static: false }) fieldValue!: IonInput;

  formSeguranca = new FormGroup({
    valor: new FormControl('', Validators.required),
  });

  usuario!: Usuario;
  metodo!: number;
  metodoSenhaCartao = MetodoSegurancaEnum.SenhaCartao;
  // readonly numberMaskOptions: MaskitoOptions = numberMask;
  readonly maskPredicate: MaskitoElementPredicate = async (el) =>
    (el as HTMLIonInputElement).getInputElement();

  constructor(
    private modalController: ModalController,
    private authService: AuthService,
    private tokenFuncionalidadeService: TokenFuncionalidadeService,
    public router: Router,
    private storageService: StorageService,
    private credencialService: CredencialService,
    private platform: Platform
  ) {
    this.usuarioSelecionado = this.storageService.getUser();
  }

  async handlePaste(event: ClipboardEvent) {
    event.preventDefault();
    // await this.fillByTransferArea();
  }

  async ngOnInit() {
    this.usuario = this.authService.getUser();

    if (!this.metodoSegurancaTransacao) {
      this.metodoSegurancaTransacao = MetodoSegurancaEnum.SenhaCartao;
    }

    if (!this.telefoneCelular) {
      this.telefoneCelular = `${this.usuario.dddTelefoneCelular}${this.usuario.telefoneCelular}`;
    }

    if (!this.documento) {
      this.documento = this.usuario.documento;
    }

    switch (this.metodoSegurancaTransacao) {
      case MetodoSegurancaEnum.NaoVerificar: {
        await this.modalController.dismiss(true);
        break;
      }
      case MetodoSegurancaEnum.SenhaCartao: {
        this.metodo = MetodoSegurancaEnum.SenhaCartao;
        this.formSeguranca
          ?.get('valor')
          ?.setValidators([Validators.minLength(4), Validators.required]);
        // await this.fillByTransferArea();
        break;
      }
      case MetodoSegurancaEnum.TokenSms: {
        this.metodo = MetodoSegurancaEnum.TokenSms;
        this.formSeguranca
          ?.get('valor')
          ?.setValidators([Validators.minLength(6), Validators.required]);
        // await this.fillByTransferArea();
        this.enviarSms();
        break;
      }
    }
    setTimeout(async () => {
      await this.fieldValue.setFocus();
      const fieldElment = await this.fieldValue.getInputElement();
      fieldElment.addEventListener('paste', this.handlePaste.bind(this));
    }, 100);

    // Listen for changes in the form control
    this.formSeguranca.get('valor')?.valueChanges.subscribe(async (value) => {
      const expectedLength =
        this.metodoSegurancaTransacao === MetodoSegurancaEnum.SenhaCartao
          ? 4
          : 6;
      if (value != null && value.length === expectedLength) {
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement) {
          activeElement.blur();
        }
        if (this.platform.is('hybrid')) {
          Keyboard.hide();
        }
      }
    });
  }

  async fillByTransferArea() {
    try {
      const area = await Clipboard.read();
      if (area.type != 'text/plain') {
        return;
      }
      if (isNaN(Number(area.value))) {
        return;
      }
      if (
        this.metodoSegurancaTransacao == MetodoSegurancaEnum.TokenSms &&
        area.value.length != 6
      ) {
        return;
      }
      if (
        this.metodoSegurancaTransacao == MetodoSegurancaEnum.SenhaCartao &&
        area.value.length != 4
      ) {
        return;
      }
      this.formSeguranca?.get('valor')?.setValue(area.value);
      const fieldElment = await this.fieldValue.getInputElement();
      fieldElment.blur();
    } catch (e) {
      console.log(e);
    }
  }

  enviarSms() {
    lastValueFrom(this.tokenFuncionalidadeService.enviarSms(this.usuario)).then(
      (r) => {
        if (r.sucesso) {
          toast('Token enviado com sucesso.');
        }
      }
    );
  }

  fechar() {
    this.modalController.dismiss(null);
  }

  async confirmar() {
    switch (this.metodoSegurancaTransacao) {
      case MetodoSegurancaEnum.SenhaCartao: {
        await this.validarSenha();
        break;
      }
      case MetodoSegurancaEnum.TokenSms: {
        await this.validarCodigo();
        break;
      }
    }
  }

  async validarCodigo() {
    const datePipe = new DatePipe('en-US');
    const chave =
      this.usuario.telefoneCelularRepresentante != null
        ? this.usuario.dddTelefoneCelularRepresentante +
          '' +
          this.usuario.telefoneCelularRepresentante +
          '' +
          datePipe.transform(new Date(), 'yyyy-MM-dd')
        : this.usuario.dddTelefoneCelular +
          '' +
          this.usuario.telefoneCelular +
          '' +
          datePipe.transform(new Date(), 'yyyy-MM-dd');

    const form = this.formSeguranca.getRawValue();
    await loading(
      this.tokenFuncionalidadeService
        .validar(chave, form.valor + '')
        .subscribe({
          next: (resposta: any) => {
            if (resposta && resposta.sucesso === true) {
              this.modalController.dismiss(true);
            } else {
              toast(resposta.erro);
              this.modalController.dismiss(false);
            }
          },
          error: (resposta) => {
            toast(
              resposta?.msg || 'Houve um erro inesperado ao validar senha.'
            );
          },
        })
    );
  }

  async validarSenha() {
    const form = this.formSeguranca.getRawValue();
    const senhaToken = form.valor + '' + this.authService.getToken();
    const pin = HashUtil.hash(senhaToken);
    const data: any = { idCredencial: this.idCredencial, pin };
    await loading(
      this.credencialService
        .validarSenhaCartao(data)
        .pipe(
          concatMap((resposta: any) => {
            if (resposta && resposta.sucesso === true) {
              this.authService.setPinCredencial(pin);
              return this.modalController.dismiss(true);
            } else {
              return this.credencialService.consultarContadorSenhaInvalida(
                this.idCredencial
              );
            }
          })
        )
        .subscribe({
          next: async (consulta: any) => {
            if (consulta != true) {
              const resultadoContador = await this.contar(
                consulta.contadorSenhaInvalida
              );
              if (consulta.bloqueioSenhaInvalida == true) {
                await this.modalController.dismiss(false);
                await this.apresentarErro(
                  consulta.bloqueioSenhaInvalida,
                  resultadoContador
                );
              } else {
                await this.modalController.dismiss(false);
                await this.apresentarErro(
                  consulta.bloqueioSenhaInvalida,
                  resultadoContador
                );
              }
            }
          },
          error: (resposta) => {
            toast(
              resposta?.erro?.msg ||
                'Houve um erro inesperado ao validar senha.'
            );
          },
        })
    );
  }

  async contar(numero: number) {
    return numero === 1 ? 2 : numero === 2 ? 1 : 0;
  }

  async apresentarErro(bloqueioSenha: boolean, contador: number) {
    const palavraFormatada = contador == 2 ? 'tentativas' : 'tentativa';
    const modal = await this.modalController.create({
      component: ModalErroComponent,
      componentProps: {
        titulo: bloqueioSenha ? 'Senha bloqueada' : 'Senha incorreta',
        mensagem: bloqueioSenha
          ? 'Você não tem mais tentativas. Redefina sua senha em duas etapas. A primeira etapa é uma validação facial, a segunda etapa é o cadastro de uma nova senha.'
          : 'Você tem mais <b>' +
            contador +
            '  ' +
            palavraFormatada +
            ' </b> antes do bloqueio da sua senha. Caso tenha esquecido, redefina sua senha.',
        tituloBotao: 'Redefinir senha',
        tituloBotaoSecundario: bloqueioSenha
          ? 'Redefinir depois'
          : 'Tentar novamente',
      },
      cssClass: 'modal-half-default',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.role == 'primaria') {
      const fluxoDados: any = {};
      const dados: any = {};
      dados.idCredencial = this.idCredencial;
      fluxoDados.documento = this.usuarioSelecionado.documento;
      fluxoDados.documentoRepresentante =
        this.usuario.documentoRepresentante == null
          ? null
          : this.usuario.documentoRepresentante;
      fluxoDados.tipoObjetivo = TipoObjetivoEnum.TrocaSenhaCartao;
      fluxoDados.tipo = 'troca-senha-cartao';
      await this.router.navigate(['/selfie'], {
        state: { fluxoDados: fluxoDados, dados: dados },
      });
    } else if (bloqueioSenha && data.role == 'secundaria') {
      this.modalController.dismiss(false);
    }
  }
}
