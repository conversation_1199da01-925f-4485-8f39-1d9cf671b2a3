<ion-content class="background-modal-ativar" [fullscreen]="true" [scrollX]="false" [scrollY]="false">
  <section>
    <div class="plastico-senha">
      <component-plastico-cartao [credencial]="credencial"></component-plastico-cartao>
    </div>
  </section>
  <section>
    <h1>Ative seu cartão {{tipo === 'fisico' ? 'físico' : 'virtual' }}</h1>
    <h2>Ative o seu cartão {{tipo === 'fisico' ? 'físico' : 'virtual' }} agora e tenha acesso a Pix, transferências, compras online e muito mais. </h2>
  </section>
</ion-content>
<ion-footer>
  <ion-button class="btn" expand="block" (click)="ativar()">Ativar cartão {{tipo === 'fisico' ? 'físico' : 'virtual' }}</ion-button>
  <ion-button class="ion-margin-top" expand="block" fill="outline" (click)="ativarDepois()">
    Ativar depois
  </ion-button>
</ion-footer>
