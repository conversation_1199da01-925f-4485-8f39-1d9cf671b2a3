import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController } from '@ionic/angular';
import { Credencial, CredencialService } from '@corporativo/shared';

@Component({
  selector: 'mobile-modal-ativar-cartao',
  templateUrl: './modal-ativar-cartao.component.html',
  styleUrls: ['./modal-ativar-cartao.component.scss'],
  standalone: false
})
export class ModalAtivarCartaoComponent implements OnInit {
  @Input() credencial!: Credencial;
  credencialVirtual!: Credencial;
  tipo = 'fisico';

  constructor(
    private router: Router,
    private modalController: ModalController,
    private credencialService: CredencialService) {
  }

  ngOnInit() {
    this.buscarCredencialVirtual();
    this.tipo = this.credencial.virtual ? 'virtual' : 'fisico';
  }

  buscarCredencialVirtual() {
    this.credencialService.buscarCredencialVirtual(this.credencial.idConta).subscribe((value: any) => {
      this.credencialVirtual = value;
    });
  }

  async ativar() {
    await this.router.navigate([this.tipo == 'fisico' ? '/cartao-fisico' : '/cartao-virtual'], {
      state: {
        credencial: this.credencial,
        cartaoSelecionado: this.credencialVirtual,
        credencialVirtual: false
      }
    });
    await this.modalController.dismiss(false);
  }

  ativarDepois() {
    return this.modalController.dismiss(false);
  }

}
