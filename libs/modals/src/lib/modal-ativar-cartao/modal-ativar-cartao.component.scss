ion-content {
  //--background: linear-gradient(180deg, #52565F 0%, #B3B3AC 50%);
  display: flex;
  flex-direction: column;
}

section:first-child {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 60%;
}

section:last-child {
  position: relative;
  background: var(--ion-color-background);
  padding-top: 2.5rem;
  text-align: center;
  border-radius: 1.5rem 1.5rem 0 0;
  height: 40%;
}

h1 {
  font-size: 24px;
  font-weight: 700;
  line-height: 28.8px;
  margin: 1.5rem 0;
}

h2 {
  font-size: 16px;
  font-weight: 300;
  line-height: 19.2px;
  color: var(--ion-color-gray-50);
  margin: 1.5rem;
  text-align: justify;
}

ion-footer {
  background: var(--ion-color-background);
  padding: 1.5rem;
}
