{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "nxCloudId": "68716c296f399d24e5570715", "targetDefaults": {"@angular/build:application": {"cache": false, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.mjs"]}}, "generators": {"@nx/angular:application": {"e2eTestRunner": "none", "linter": "eslint", "style": "scss", "unitTestRunner": "none"}, "@nx/angular:library": {"linter": "eslint", "unitTestRunner": "none"}, "@nx/angular:component": {"style": "scss"}}}