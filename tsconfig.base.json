{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@corporativo/components": ["libs/components/src/index.ts"], "@corporativo/modals": ["libs/modals/src/index.ts"], "@corporativo/pix": ["libs/pix/src/index.ts"], "@corporativo/saldo": ["libs/saldo/src/index.ts"], "@corporativo/shared": ["libs/shared/src/index.ts"], "@utils/*": ["libs/utils/*"]}}, "exclude": ["node_modules", "tmp"]}