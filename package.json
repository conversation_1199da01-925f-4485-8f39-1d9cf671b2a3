{"name": "@corporativo/source", "version": "0.0.0", "author": "Valloo Tecnologia", "homepage": "https://valloo.com.br", "license": "MIT", "scripts": {"valloo:hom": "npx nx serve valloo --configuration=hom", "valloo:prod": "npx nx serve valloo --configuration=production", "build:valloo:hom": "npx nx build valloo --configuration=hom", "build:valloo:hom:android": "npx nx build valloo --configuration=hom && nx run valloo:sync:android && nx run valloo:open:android", "build:valloo:hom:ios": "npx nx build valloo --configuration=hom && nx run valloo:sync:ios && nx run valloo:open:ios", "build:valloo:prod": "npx nx build valloo --configuration=production", "build:valloo:prod:android": "npx nx build valloo --configuration=production && nx run valloo:sync:android && nx run valloo:open:android", "build:valloo:prod:ios": "npx nx build valloo --configuration=production && nx run valloo:sync:ios && nx run valloo:open:ios"}, "private": true, "dependencies": {"@angular/common": "~20.1.0", "@angular/compiler": "~20.1.0", "@angular/core": "~20.1.0", "@angular/forms": "~20.1.0", "@angular/platform-browser": "~20.1.0", "@angular/platform-browser-dynamic": "^20.1.0", "@angular/router": "~20.1.0", "@capacitor-community/file-opener": "^6.0.0", "@capacitor/android": "^6.1.2", "@capacitor/app": "6.0.1", "@capacitor/barcode-scanner": "^1.0.2", "@capacitor/camera": "^6.1.2", "@capacitor/clipboard": "^6.0.1", "@capacitor/core": "6.2.1", "@capacitor/device": "^6.0.1", "@capacitor/filesystem": "^6.0.1", "@capacitor/geolocation": "^6.0.1", "@capacitor/haptics": "6.0.1", "@capacitor/ios": "^6.1.2", "@capacitor/keyboard": "6.0.2", "@capacitor/screen-orientation": "^6.0.3", "@capacitor/share": "^6.0.2", "@capacitor/status-bar": "6.0.1", "@ionic/angular": "^8.0.0", "@maskito/angular": "^3.10.0", "@maskito/core": "^3.10.0", "@maskito/kit": "^3.10.0", "@mrmgomes/boleto-utils": "^1.1.0", "@valloo-tecnologia/caf-document-detector": "^1.0.6", "@valloo-tecnologia/caf-face-auth": "^1.0.0", "@valloo-tecnologia/caf-face-liveness": "^1.0.0", "capacitor-native-biometric": "^4.2.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "ionicons": "^7.0.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "ngx-capture": "^0.14.0", "ngx-image-cropper": "^9.1.5", "onesignal-cordova-plugin": "^5.2.15", "qrcode": "^1.5.4", "rxjs": "~7.8.0", "swiper": "^11.2.10", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.1.0", "@angular-devkit/core": "~20.1.0", "@angular-devkit/schematics": "~20.1.0", "@angular-eslint/builder": "^20.1.0", "@angular-eslint/eslint-plugin": "^20.1.0", "@angular-eslint/eslint-plugin-template": "^20.1.0", "@angular-eslint/schematics": "^20.1.0", "@angular-eslint/template-parser": "^20.1.0", "@angular/build": "~20.1.0", "@angular/cli": "~20.1.0", "@angular/compiler-cli": "~20.1.0", "@angular/language-service": "~20.1.0", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "7.4.2", "@eslint/js": "^9.8.0", "@ionic/angular-toolkit": "^12.0.0", "@nx/angular": "21.2.3", "@nx/eslint": "21.2.3", "@nx/eslint-plugin": "21.2.3", "@nx/js": "21.2.3", "@nx/web": "21.2.3", "@nx/workspace": "21.2.3", "@schematics/angular": "~20.0.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.20", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@typescript-eslint/utils": "^8.29.0", "@valloo-tecnologia/nx-ionic-cap": "^1.0.0", "angular-eslint": "^20.0.0", "eslint": "^9.16.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "nx": "21.2.3", "prettier": "^2.6.2", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0"}}